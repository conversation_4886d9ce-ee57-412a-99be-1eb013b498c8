import fs from "fs"
import path from "path"
import { aliasPath } from "../path.js"
import { getNameById } from "./id2name.js"

// 别名文件路径
const charAliasPath = path.join(aliasPath, "char_alias.json")
const weaponAliasPath = path.join(aliasPath, "weapon_alias.json")
const sonataAliasPath = path.join(aliasPath, "sonata_alias.json")
const echoAliasPath = path.join(aliasPath, "echo_alias.json")

// 全局别名数据缓存
let charAliasData = null
let weaponAliasData = null
let sonataAliasData = null
let echoAliasData = null

/**
 * 加载别名数据
 * @param {string} filePath - 别名文件路径
 * @param {string} type - 别名类型
 * @returns {Object} 别名数据
 */
function loadAliasData(filePath, type) {
  try {
    if (fs.existsSync(filePath)) {
      const rawData = fs.readFileSync(filePath, "utf8")
      const data = JSON.parse(rawData)
      console.log(`[Rover Plugin] 成功加载${type}别名数据，共${Object.keys(data).length}条记录`)
      return data
    } else {
      console.warn(`[Rover Plugin] ${type}别名文件不存在: ${filePath}`)
      return {}
    }
  } catch (error) {
    console.error(`[Rover Plugin] 加载${type}别名数据失败:`, error)
    return {}
  }
}

/**
 * 初始化角色别名数据
 */
function initCharAliasData() {
  if (charAliasData === null) {
    charAliasData = loadAliasData(charAliasPath, "角色")
  }
  return charAliasData
}

/**
 * 初始化武器别名数据
 */
function initWeaponAliasData() {
  if (weaponAliasData === null) {
    weaponAliasData = loadAliasData(weaponAliasPath, "武器")
  }
  return weaponAliasData
}

/**
 * 初始化声律别名数据
 */
function initSonataAliasData() {
  if (sonataAliasData === null) {
    sonataAliasData = loadAliasData(sonataAliasPath, "套装")
  }
  return sonataAliasData
}

/**
 * 初始化声骸别名数据
 */
function initEchoAliasData() {
  if (echoAliasData === null) {
    echoAliasData = loadAliasData(echoAliasPath, "声骸")
  }
  return echoAliasData
}

/**
 * 别名转角色名称
 * @param {string} charName - 输入的角色名称或别名
 * @returns {string} 标准角色名称
 */
export function aliasToCharName(charName) {
  const aliasData = initCharAliasData()

  // 遍历所有标准名称
  for (const standardName in aliasData) {
    // 检查是否为标准名称本身
    if (charName === standardName) {
      return standardName
    }

    // 检查是否在别名列表中
    const aliases = aliasData[standardName]
    if (Array.isArray(aliases) && aliases.includes(charName)) {
      console.log(`[Rover Plugin] 别名转换: ${charName} -> ${standardName}`)
      return standardName
    }
  }

  // 如果没有找到匹配的别名，返回原名称
  return charName
}

/**
 * 别名转武器名称
 * @param {string} weaponName - 输入的武器名称或别名
 * @returns {string} 标准武器名称
 */
export function aliasToWeaponName(weaponName) {
  const aliasData = initWeaponAliasData()

  for (const standardName in aliasData) {
    if (weaponName === standardName) {
      return standardName
    }

    const aliases = aliasData[standardName]
    if (Array.isArray(aliases) && aliases.includes(weaponName)) {
      console.log(`[Rover Plugin] 武器别名转换: ${weaponName} -> ${standardName}`)
      return standardName
    }
  }

  return weaponName
}

/**
 * 别名转声律名称
 * @param {string} sonataName - 输入的声律名称或别名
 * @returns {string} 标准声律名称
 */
export function aliasToSonataName(sonataName) {
  const aliasData = initSonataAliasData()

  for (const standardName in aliasData) {
    if (sonataName === standardName) {
      return standardName
    }

    const aliases = aliasData[standardName]
    if (Array.isArray(aliases) && aliases.includes(sonataName)) {
      console.log(`[Rover Plugin] 声律别名转换: ${sonataName} -> ${standardName}`)
      return standardName
    }
  }

  return sonataName
}

/**
 * 别名转声骸名称
 * @param {string} echoName - 输入的声骸名称或别名
 * @returns {string} 标准声骸名称
 */
export function aliasToEchoName(echoName) {
  const aliasData = initEchoAliasData()

  for (const standardName in aliasData) {
    if (echoName === standardName) {
      return standardName
    }

    const aliases = aliasData[standardName]
    if (Array.isArray(aliases) && aliases.includes(echoName)) {
      console.log(`[Rover Plugin] 声骸别名转换: ${echoName} -> ${standardName}`)
      return standardName
    }
  }

  return echoName
}

/**
 * 角色ID转角色名称
 * @param {string|number} charId - 角色ID
 * @returns {string|null} 角色名称，如果找不到则返回null
 */
export function charIdToCharName(charId) {
  const name = getNameById(charId)
  return name === charId ? null : name
}

/**
 * 综合别名和ID转换为角色名称
 * @param {string} input - 输入的角色名称、别名或ID
 * @returns {string} 标准角色名称
 */
export function resolveCharName(input) {
  // 首先尝试别名转换
  const aliasResult = aliasToCharName(input)
  if (aliasResult !== input) {
    return aliasResult
  }

  // 如果别名转换无效果，尝试ID转换
  const idResult = charIdToCharName(input)
  if (idResult !== null) {
    return idResult
  }

  // 都无效果，返回原输入
  return input
}

initCharAliasData()
initWeaponAliasData()
initSonataAliasData()
initEchoAliasData()
