
.avatar-card {
  @px: 1.5px;
  margin: @px*3;
  box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.8);
  font-size: @px*13;
  border-radius: @px*7;


  .card {
    border-radius: @px*7;
    box-shadow: 0 2px @px*6 0 rgb(132 93 90 / 30%);
    position: relative;
    overflow: hidden;
    background: #e7e5d9;
    width: @px*70;
  }

  .avatar-face {
    width: @px*70;
    height: @px*70;
    border-radius: @px*7 @px*7 @px*15 0;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    position: relative;
    box-shadow: 0 0 2px 0 rgba(0, 0, 0, .5);

    .img {
      background-position: center bottom;
    }

    .avatar-level {
      position: absolute;
      bottom: 0;
      background: rgba(0, 0, 0, 0.6);
      left: 0;
      padding: @px*2 @px*5 @px*2 @px*3;
      border-radius: 0 @px*4 0 0;
      color: #fff;
      font-size: 16px;
      text-shadow: 0 0 1px #000;

      span {
        font-size: 12px;
      }
    }

  }

  .popularity {
    border-radius: 0 0 @px*5 0;
    padding: @px*2 @px*5;
    position: absolute;
    left: 0;
    top: 0;
    font-size: 16px;
    text-shadow: 0 0 1px #000, 1px 1px 3px rgba(0, 0, 0, .8);
    background-color: #C00000;
  }

  .cons {
    border-radius: 0 0 0 @px*5;
    padding: @px*2 @px*5;
    position: absolute;
    right: 0;
    top: 0;
    font-size: 16px;
    text-shadow: 0 0 1px #000, 1px 1px 3px rgba(0, 0, 0, .8);

    &.cons-0 {
      display: none;
    }

    &.cons-试用 {
      background-color: rgb(217, 118, 124);
    }
    &.cons-助演 {
      background-color: rgb(72, 137, 191);
    }
  }

  .avatar-talent {
    height: @px*20;
    padding: @px*3 @px*5 @px*2;
    font-size: @px*12;
    width: 100%;
    color: #222;
    text-align: center;
    display: flex;

    .talent-item {
      width: @px*20;
      height: @px*16;
      line-height: @px*17;
      margin: 0 @px*2;
      text-align: center;
      display: block;
      background-size: contain;
      opacity: 0.8;
      position: relative;
      border-radius: @px*3;
      box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.5);

      &.talent-plus {
        font-weight: bold;
        color: #0284b9;
      }

      &.talent-crown {
        background: #d3bc8e;
        color: #3a2702;
        box-shadow: 0 0 @px*2 0 #000;
      }
    }

    &.no-talent {
      font-size: @px*12;
      color: rgba(100, 100, 100, .5);
      text-align: center;
      padding: @px*3 0 @px*2;

      span {
        transform: scale(.75);
        white-space: nowrap;
        margin-left: -1px;
      }
    }
  }

  &.card-mini {
    .wide, .line {
      display: none;
    }
  }

  .avatar-name {
    padding: @px*8 0 0 @px*5;
    color: #333;

    strong {
      font-size: @px*20px;
      display: block;
      height: @px*23;
      line-height: @px*20;
    }

    .cons {
      position: initial;
      border-radius: 4px;
      padding: @px @px*3;
      vertical-align: baseline;
    }
  }

  &.card-wide {
    .mini {
      display: none;
    }

    .card {
      width: @px*146;
      display: flex;
    }

    .avatar-face {
      height: @px * 126;
      width: @px*76;
      border-radius: @px*7 0 0 @px*7;

      .img {
        background-size: 100% auto;
        background-position: 0 10%;
        height: @px*135;
        margin-top: @px*-9;
      }
    }

    .avatar-info {
      width: @px*70;

      strong {
        display: block;
        height: @px * 30;
        line-height: @px*30;
      }

      .lv-info {
        height: @px*20;
      }
    }

    .line {
      display: block;
      height: 1px;
      width: 100%;
      margin: 5px 0;
      background: linear-gradient(to right, rgba(0, 0, 0, 0), rgba(100, 100, 100, .5) 20%, rgba(100, 100, 100, .5) 80%, rgba(0, 0, 0, 0));
      transform: scale(.8)
    }
  }

  &.wide2 {
    .card {
      width: @px*298;
    }

    .avatar-face {
      width: @px*146;

      .img {
        margin-top: @px*-50;
        height: @px*176;
      }
    }

    .avatar-info {
      width: @px*146;
      padding-left: @px*5;
    }


  }


  .avatar-detail {
    display: flex;
    padding: 0 @px*1 @px*2 @px*2;

    .item {
      width: @px*31;
      height: @px*31;
      border-radius: @px*3;
      margin: @px*1;
      overflow: hidden;
    }
  }

  .avatar-weapon {
    .icon {
      border-radius: 4px;
    }

    .cons {
      top: initial;
      bottom: 0;
      padding: @px @px*3;
      border-radius: @px*3 0 0 0;
    }
  }

  .avatar-artis {
    position: relative;

    &.artis0 {
      .item-icon {
        background: url('./item/artifact-icon.webp') rgba(0, 0, 0, .3) center no-repeat;
        background-size: auto 80%;
      }
    }

    .artis {
      background: rgba(0, 0, 0, 0.4)
    }

    &.artis2 {
      .img {
        position: absolute;
        transform: scale(.7);
        width: 92%;
        height: 92%;
        margin: 4%;

        &:first-child {
          transform-origin: left top;
        }

        &:last-child {
          transform-origin: right bottom;
        }
      }
    }
  }
}