import path from "path"
import fs from "fs"
import { fileURLToPath } from "url"

// 将 URL 转换为文件路径
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 插件路径
export const pluginPath = path.join(__dirname, "../")
// 插件名
export const pluginName = path.basename(pluginPath)

// -----------------------------------------------------------------
// 插件数据目录

// apps 路径
export const appPath = path.join(pluginPath, "apps")

// resources 路径
export const resourcesPath = path.join(pluginPath, "resources")

// 存放基础数据的路径
// map路径 (已移动到 utils/map)
export const mapPath = path.join(pluginPath, "utils/map")
// 别名
export const aliasPath = path.join(mapPath, "alias")
// 权重
export const charWeightPath = path.join(mapPath, "character")
// id2name
export const id2NamePath = path.join(mapPath, "id2name.json")

// config 路径
export const configPath = path.join(pluginPath, "config")
// 默认配置路径
export const defPath = path.join(pluginPath, "defSet")

// -----------------------------------------------------------------
// 数据目录 - 使用Yunzai根目录下的data文件夹
// 从插件目录向上两级到达Yunzai根目录
export const dataPath = path.join(pluginPath, "../../data")

// 创建数据目录
if (!fs.existsSync(`${dataPath}`)) {
  fs.mkdirSync(`${dataPath}`, { recursive: true })
}
