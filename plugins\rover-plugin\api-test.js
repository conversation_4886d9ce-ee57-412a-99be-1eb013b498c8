/**
 * API功能测试脚本 - 测试已绑定用户的API功能
 */

import { RoverAPI } from "./lib/api.js"

const TEST_USER_ID = "test_user_12345"

// 测试结果统计
let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: [],
}

function logTest(name, success, message = "") {
  testResults.total++
  if (success) {
    testResults.passed++
    console.log(`✅ ${name}: 通过 ${message}`)
  } else {
    testResults.failed++
    testResults.errors.push(`${name}: ${message}`)
    console.log(`❌ ${name}: 失败 ${message}`)
  }
}

async function safeTest(name, testFn) {
  try {
    const result = await testFn()
    logTest(name, true, result || "")
    return result
  } catch (error) {
    logTest(name, false, error.message)
    return null
  }
}

async function testBindingStatus() {
  console.log("\n🔍 === 检查绑定状态 ===")
  
  await safeTest("检查用户绑定", async () => {
    const result = await RoverAPI.isUserBound(TEST_USER_ID)
    return `绑定状态: ${result}`
  })

  await safeTest("获取绑定详情", async () => {
    const result = await RoverAPI.checkUserBinding(TEST_USER_ID)
    return `绑定: ${result.hasBinding}, Token: ${result.hasToken}, 账号: ${result.hasValidAccount}`
  })

  await safeTest("获取用户UID", async () => {
    const result = await RoverAPI.getUserUid(TEST_USER_ID)
    return `UID: ${result || "无"}`
  })
}

async function testGameDataAPIs() {
  console.log("\n🎮 === 游戏数据API测试 ===")

  await safeTest("获取基础数据", async () => {
    const result = await RoverAPI.getBaseData(TEST_USER_ID)
    if (result.success) {
      return `成功获取基础数据`
    } else {
      return `失败: ${result.message}`
    }
  })

  await safeTest("获取角色数据", async () => {
    const result = await RoverAPI.getRoleData(TEST_USER_ID)
    if (result.success) {
      return `成功获取角色数据`
    } else {
      return `失败: ${result.message}`
    }
  })

  await safeTest("获取探索数据", async () => {
    const result = await RoverAPI.getExploreData(TEST_USER_ID)
    if (result.success) {
      return `成功获取探索数据`
    } else {
      return `失败: ${result.message}`
    }
  })

  await safeTest("获取挑战数据", async () => {
    const result = await RoverAPI.getChallengeData(TEST_USER_ID)
    if (result.success) {
      return `成功获取挑战数据`
    } else {
      return `失败: ${result.message}`
    }
  })
}

async function testAdvancedAPIs() {
  console.log("\n🚀 === 高级API测试 ===")

  await safeTest("刷新用户面板", async () => {
    const result = await RoverAPI.refreshUserPanel(TEST_USER_ID)
    if (result.success) {
      return `成功刷新面板数据`
    } else {
      return `失败: ${result.message}`
    }
  })

  await safeTest("获取完整游戏数据", async () => {
    const result = await RoverAPI.getUserGameData(TEST_USER_ID, true)
    if (result.success) {
      return `成功获取完整数据`
    } else {
      return `失败: ${result.message}`
    }
  })
}

async function printTestSummary() {
  console.log("\n📊 === 测试结果汇总 ===")
  console.log(`总测试数: ${testResults.total}`)
  console.log(`通过: ${testResults.passed}`)
  console.log(`失败: ${testResults.failed}`)
  console.log(`成功率: ${((testResults.passed / testResults.total) * 100).toFixed(2)}%`)

  if (testResults.errors.length > 0) {
    console.log("\n❌ 失败的测试:")
    testResults.errors.forEach(error => console.log(`  - ${error}`))
  }

  console.log(
    testResults.failed === 0 ? "\n🎉 所有测试通过！" : "\n⚠️ 部分测试失败，请检查上述错误"
  )
}

async function runAPITests() {
  console.log("🧪 开始API功能测试...")
  console.log(`测试用户ID: ${TEST_USER_ID}`)

  try {
    await testBindingStatus()
    await testGameDataAPIs()
    await testAdvancedAPIs()
    await printTestSummary()
  } catch (error) {
    console.error("❌ 测试过程中发生严重错误:", error)
  }
}

// 运行测试
runAPITests()
