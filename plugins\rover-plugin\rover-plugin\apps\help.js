import plugin from "../../../lib/plugins/plugin.js"
import config from "../components/config.js"

export class Help extends plugin {
  constructor() {
    super({
      name: "鸣潮-帮助",
      event: "message",
      priority: 1007,
      rule: [
        {
          reg: config.generateCommandRegex("帮助"),
          fnc: "help",
        },
      ],
    })
  }

  async help(e) {
    // 获取动态前缀示例
    const prefix = config.getExamplePrefix()
    const separator = config.getCommandSeparator()

    // 生成命令示例
    const cmd = command => `${prefix}${separator}${command}`

    const helpMsg = `
🌊 鸣潮插件帮助

📋 功能列表
• 角色面板查询
• 角色权重分析
• 面板数据刷新
• 账号绑定管理
• 排行榜查询
• 网页登录
• 兑换码查询

🎮 基础命令
${cmd("面板")} - 查看默认角色面板
${cmd("长离面板")} - 查看指定角色面板
${cmd("长离权重")} - 查看指定角色权重分析
${cmd("刷新面板")} - 刷新所有角色数据
${cmd("刷新长离面板")} - 刷新指定角色数据

🎯 练度统计
${cmd("练度统计")} - 查看所有角色练度排行对比

🔗 绑定管理
${cmd("绑定uid 123456789")} - 绑定鸣潮UID
${cmd("添加token token,did")} - 添加登录token和设备ID
${cmd("我的绑定")} - 查看当前绑定信息
${cmd("获取token")} - 获取已绑定的token,did

📋 UID管理
${cmd("查看")} - 查看所有绑定的UID
${cmd("切换 123456789")} - 切换到指定UID
${cmd("删除 123456789")} - 删除指定UID
${cmd("删除全部uid")} - 删除所有绑定的UID
${cmd("删除token")} - 删除当前UID的token
${cmd("删除token 123456789")} - 删除指定UID的token

📊 排行榜
${cmd("群排行")} - 查看群内排行
${cmd("长离群排行")} - 查看群内指定角色排行
${cmd("总排行")} - 查看全服排行（需要token）
${cmd("长离总排行")} - 查看全服指定角色排行

🎁 兑换码
${cmd("兑换码")} - 获取最新可用兑换码

🔐 登录方式

【网页登录方式】（推荐）
1. 发送 ${cmd("登录")} 获取登录链接
2. 在浏览器中打开链接完成登录
3. 登录成功后会自动保存到数据库
4. 即可开始使用面板功能

【手动方式】
1. 手动绑定你的鸣潮UID（9位数字）
2. 手动绑定token（从库洛游戏通行证APP获取）
3. 绑定完成后即可查询角色面板

🔧 获取token方法（手动绑定时需要）
1. 打开库洛游戏通行证APP
2. 登录后进入鸣潮游戏
3. 使用浏览器开发者工具查看网络请求
4. 找到请求头中的 b-at 字段值

⚠️ 注意事项
• 推荐使用网页登录，更安全便捷
• token具有时效性，过期后需要重新登录
• 请勿将token分享给他人
• 如遇问题请联系管理员

📝 示例
【网页登录方式】
${cmd("登录")}
（在浏览器中完成登录）
${cmd("刷新面板")}
${cmd("长离面板")}

【手动绑定方式】
${cmd("绑定uid 123456789")}
${cmd("绑定token abc123def456")}
${cmd("刷新面板")}
${cmd("长离面板")}
${cmd("秧秧面板")}
    `.trim()

    await e.reply(helpMsg)
    return true
  }
}
