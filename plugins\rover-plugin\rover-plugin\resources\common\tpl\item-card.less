
.item-card {
  width: 66px;
  margin: 4px;
  position: relative;
  border-radius: 5px;


  .badge {
    overflow: hidden;
    border-radius: 50%;
    display: block;
    width: 26px;
    height: 26px;
    position: absolute;
    right: -4px;
    top: -4px;
    background: #e9e5dc;
    box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.8), 2px 2px 2px rgba(50, 50, 50, .5);
    z-index: 10;


    img {
      width: 140%;
      left: -27%;
      top: -45%;
      overflow: hidden;
      background-size: 100%;
      background-repeat: no-repeat;
      position: absolute;
    }
  }

  .item-bg {
    width: 100%;
    padding: 100% 0 0;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    margin: 0;
  }

  each(range(5), {
    .bg@{value} {
      background-image: url("./item/bg@{value}.png");
      border-radius: 5px 5px 15px 0;
    }
    .bg-@{value} {
      background-image: url("./item/bg@{value}.png");
      border-radius: 5px 5px 15px 0;
    }
  })

  .box {
    border-radius: 5px;
    overflow: hidden;
    background: #e9e5dc;

    .item-desc {
      display: block;
      font-weight: 500;
      text-align: center;
      bottom: 0;
      background: #e9e5dc;
      width: 100%;
      font-size: 12px;
      line-height: 16px;
      white-space: nowrap;
      overflow: hidden;
      padding: 0 3px;

      &:last-child {
        padding-bottom: 2px;
      }
    }

    .name {
      overflow: hidden;
      white-space: nowrap;
      margin-top: 5px;
      font-weight: 500;
      text-align: center;
      font-size: 14px;
    }

    .item-img {
      width: 100%;
      overflow: hidden;
      background-size: 100%;
      background-repeat: no-repeat;
      position: absolute;
      top: 0;
    }
  }

  .item-life {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9;
    font-size: 13px;
    text-align: center;
    color: #fff;
    padding: 1px 4px;
    border-radius: 3px;
  }

  .life(@idx, @color) {
    .life@{idx} {
      background-color: @color;
    }
  }

  .life(1, #62a8ea);
  .life(2, #62a8ea);
  .life(3, #62a8ea);
  .life(4, #ff5722);
  .life(5, #ff5722);
}

