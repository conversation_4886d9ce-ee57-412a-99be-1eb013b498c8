import plugin from "../../../lib/plugins/plugin.js"
import User from "../components/User.js"
import redis from "../components/redis.js"
import config from "../components/config.js"
import { CharacterCache } from "../components/cache.js"
import { renderer } from "../utils/renderer.js"

export class Rank extends plugin {
  constructor() {
    super({
      name: "鸣潮-排行榜",
      event: "message",
      priority: 1003,
      rule: [
        {
          reg: config.generateCommandRegex("群排行"),
          fnc: "groupRank",
        },
        {
          reg: config.generateCommandRegex("(.+)群排行"),
          fnc: "groupCharRank",
        },
        {
          reg: config.generateCommandRegex("总排行"),
          fnc: "globalRank",
        },
        {
          reg: config.generateCommandRegex("(.+)总排行"),
          fnc: "globalCharRank",
        },
        {
          reg: config.generateCommandRegex("更新排行"),
          fnc: "updateRank",
        },
      ],
    })
  }

  /**
   * 群排行
   */
  async groupRank(e) {
    if (!e.group_id) {
      await e.reply("❌ 此功能仅支持群聊使用")
      return false
    }

    try {
      await e.reply("🔄 正在统计群排行数据...")

      // 获取群内所有UID
      const groupUids = User.getGroupAllUid(e.group_id)
      if (groupUids.length === 0) {
        await e.reply("❌ 群内暂无绑定用户")
        return false
      }

      // 收集所有用户的角色数据
      const rankData = []
      for (const uid of groupUids) {
        try {
          const roleDetailList = await CharacterCache.getRoleDetailList(uid)
          if (roleDetailList && roleDetailList.length > 0) {
            // 计算用户总战力
            let totalPower = 0
            let maxLevel = 0
            let roleCount = roleDetailList.length
            let maxStarLevel = 0

            roleDetailList.forEach(roleDetail => {
              if (roleDetail.role) {
                const level = roleDetail.role.level || 0
                const starLevel = roleDetail.role.starLevel || 0
                const weaponLevel = roleDetail.weapon?.level || 0

                maxLevel = Math.max(maxLevel, level)
                maxStarLevel = Math.max(maxStarLevel, starLevel)

                // 优化的战力计算公式
                totalPower += level * 100 + starLevel * 1000 + weaponLevel * 50
              }
            })

            rankData.push({
              uid,
              totalPower,
              maxLevel,
              maxStarLevel,
              roleCount,
              updateTime: CharacterCache.getCacheTime(uid),
            })
          }
        } catch (error) {
          console.warn(`获取用户 ${uid} 数据失败:`, error)
        }
      }

      if (rankData.length === 0) {
        await e.reply(
          `❌ 群内暂无有效角色数据\n请先使用 ${config.getCommandExample("刷新面板")} 获取角色数据`,
        )
        return false
      }

      // 按总战力排序
      rankData.sort((a, b) => b.totalPower - a.totalPower)

      // 缓存排行数据到Redis（如果可用）
      await this.cacheGroupRankData(e.group_id, rankData)

      // 生成排行榜图片
      const img = await this.generateGroupRankImage(e, rankData)

      if (img) {
        await e.reply(img)
      } else {
        // 如果图片生成失败，发送文本版本
        await this.sendTextRank(e, rankData, "群排行榜")
      }

      return true
    } catch (error) {
      console.error("群排行错误:", error)
      await e.reply("❌ 群排行统计失败")
      return false
    }
  }

  /**
   * 群角色排行 - 使用实时Redis数据
   */
  async groupCharRank(e) {
    // 构建动态正则提取角色名
    const prefixes = config.getAllPrefixes()
    const separator = config.getCommandSeparator()
    const allowEmpty = config.isEmptyPrefixAllowed()

    let regexParts = []
    if (prefixes.length > 0) {
      const escapedPrefixes = prefixes.map(prefix => config.escapeRegex(prefix))
      regexParts.push(`(${escapedPrefixes.join("|")})${config.escapeRegex(separator)}`)
    }
    if (allowEmpty) {
      regexParts.push("")
    }

    const prefixPattern = regexParts.length > 1 ? `(${regexParts.join("|")})` : regexParts[0]
    const regex = new RegExp(`^${prefixPattern}(.+)群排行$`)
    const match = e.msg.match(regex)
    const charName = match && match[match.length - 1] ? match[match.length - 1].trim() : ""

    if (!e.group_id) {
      await e.reply("❌ 此功能仅支持群聊使用")
      return false
    }

    try {
      await e.reply(`🔄 正在获取群内 ${charName} 实时排行数据...`)

      // 使用新的实时排行方法
      const displayCount = config.get("groupRank.displayCount") || 20
      const charRankData = await Rank.getCharacterRank(e.group_id, charName, displayCount)

      if (charRankData.length === 0) {
        await e.reply(
          `❌ 暂无 ${charName} 的排行数据\n\n💡 提示：\n• 群成员需要先使用【#鸣潮刷新面板】来更新数据\n• 确认角色名称是否正确`,
        )
        return false
      }

      // 生成排行榜消息
      let msg = [`🏆 ${charName} 群内排行榜 (前${charRankData.length}名)\n`]

      charRankData.forEach(item => {
        const medal = item.rank <= 3 ? ["🥇", "🥈", "🥉"][item.rank - 1] : `${item.rank}.`
        msg.push(`${medal} UID: ${item.uid}`)
        msg.push(`   Lv.${item.level} ${item.starLevel}星 ${item.attributeName}`)
        msg.push(`   武器类型: ${item.weaponTypeName}`)
        msg.push(`   分数: ${item.score}\n`)
      })

      msg.push(`\n📊 实时排行数据`)
      msg.push(`💡 使用【#鸣潮刷新面板】可更新排行数据`)

      await e.reply(msg.join("\n"))
      return true
    } catch (error) {
      console.error("群角色排行错误:", error)
      await e.reply(`❌ ${charName} 群排行统计失败`)
      return false
    }
  }

  /**
   * 总排行
   */
  async globalRank(e) {
    try {
      await e.reply("🔄 正在获取总排行数据...")

      // 检查配置
      const globalRankConfig = config.getGlobalRankConfig()
      if (!globalRankConfig.token) {
        await e.reply("❌ 总排行功能未配置token，请联系管理员")
        return false
      }

      // 先检查缓存
      const cacheKey = "ww:global_rank:total"
      let globalRankData = null

      if (redis.isConnected()) {
        const cached = await redis.get(cacheKey)
        if (cached) {
          const cacheData = JSON.parse(cached)
          // 直接使用缓存数据，不检查时间（排行数据永久有效）
          globalRankData = cacheData.data
          console.log("使用缓存的总排行数据")
        }
      }

      // 如果没有缓存，从API获取（这里暂时模拟）
      if (!globalRankData) {
        // TODO: 实现从库街区API获取总排行数据
        // 这里先返回提示信息
        await e.reply("⚠️ 总排行功能需要配置库街区API，暂时无法使用\n请联系管理员配置相关token")
        return true
      }

      // 发送总排行数据
      await this.sendGlobalRank(e, globalRankData)
      return true
    } catch (error) {
      console.error("总排行错误:", error)
      await e.reply("❌ 总排行获取失败")
      return false
    }
  }

  /**
   * 总角色排行
   */
  async globalCharRank(e) {
    // 构建动态正则提取角色名
    const prefixes = config.getAllPrefixes()
    const separator = config.getCommandSeparator()
    const allowEmpty = config.isEmptyPrefixAllowed()

    let regexParts = []
    if (prefixes.length > 0) {
      const escapedPrefixes = prefixes.map(prefix => config.escapeRegex(prefix))
      regexParts.push(`(${escapedPrefixes.join("|")})${config.escapeRegex(separator)}`)
    }
    if (allowEmpty) {
      regexParts.push("")
    }

    const prefixPattern = regexParts.length > 1 ? `(${regexParts.join("|")})` : regexParts[0]
    const regex = new RegExp(`^${prefixPattern}(.+)总排行$`)
    const match = e.msg.match(regex)
    const charName = match && match[match.length - 1] ? match[match.length - 1].trim() : ""

    try {
      await e.reply(`🔄 正在获取 ${charName} 总排行数据...`)

      // 检查配置
      const globalRankConfig = config.getGlobalRankConfig()
      if (!globalRankConfig.token) {
        await e.reply("❌ 总排行功能未配置token，请联系管理员")
        return false
      }

      // 检查缓存
      const cacheKey = `ww:global_char_rank:${charName}`
      let globalCharRankData = null

      if (redis.isConnected()) {
        const cached = await redis.get(cacheKey)
        if (cached) {
          const cacheData = JSON.parse(cached)
          // 直接使用缓存数据，不检查时间（排行数据永久有效）
          globalCharRankData = cacheData.data
          console.log(`使用缓存的${charName}总排行数据`)
        }
      }

      // 如果没有缓存，从API获取（这里暂时模拟）
      if (!globalCharRankData) {
        // TODO: 实现从库街区API获取角色总排行数据
        await e.reply(
          `⚠️ ${charName} 总排行功能需要配置库街区API，暂时无法使用\n请联系管理员配置相关token`,
        )
        return true
      }

      // 发送角色总排行数据
      await this.sendGlobalCharRank(e, globalCharRankData, charName)
      return true
    } catch (error) {
      console.error("总角色排行错误:", error)
      await e.reply(`❌ ${charName} 总排行获取失败`)
      return false
    }
  }

  /**
   * 更新排行
   */
  async updateRank(e) {
    if (!e.isMaster && !e.member?.is_admin) {
      await e.reply("❌ 只有主人或群管理员才能执行此操作")
      return false
    }

    await e.reply("🔄 正在更新排行数据...")

    try {
      let cleanedCount = 0

      // 如果启用了Redis，清理Redis排行缓存
      if (redis.isConnected()) {
        // 清理群排行缓存
        if (e.group_id) {
          const groupRankKey = `ww:group_rank:${e.group_id}`
          await redis.del(groupRankKey)

          // 清理角色排行缓存
          const charRankPattern = `ww:char_rank:${e.group_id}:*`
          // 注意：这里简化处理，实际应该使用SCAN命令
          cleanedCount += 1
        }

        console.log("Redis排行缓存已清理")
      }

      // 验证和修复缓存数据
      const allUids = CharacterCache.getAllCachedUids()
      let repairedCount = 0

      for (const uid of allUids) {
        const isValid = await CharacterCache.validateCacheData(uid)
        if (!isValid) {
          await CharacterCache.repairCacheData(uid)
          repairedCount++
        }
      }

      let message = `✅ 排行数据更新完成\n`
      if (cleanedCount > 0) {
        message += `清理Redis缓存: ${cleanedCount} 个\n`
      }
      if (repairedCount > 0) {
        message += `修复损坏缓存: ${repairedCount} 个\n`
      }
      message += `当前缓存用户: ${allUids.length} 个`

      await e.reply(message)
      return true
    } catch (error) {
      console.error("更新排行数据错误:", error)
      await e.reply("❌ 更新排行数据失败")
      return false
    }
  }

  /**
   * 缓存群排行数据到Redis
   * @param {string} groupId - 群ID
   * @param {Array} rankData - 排行数据
   */
  async cacheGroupRankData(groupId, rankData) {
    try {
      if (redis.isConnected()) {
        const cacheKey = `ww:group_rank:${groupId}`
        const cacheData = {
          data: rankData,
          updateTime: new Date().toISOString(),
          count: rankData.length,
        }

        // 永久缓存（不设置过期时间）
        await redis.set(cacheKey, JSON.stringify(cacheData))
        console.log(`群排行数据已缓存: ${groupId}`)
      }
    } catch (error) {
      console.warn("缓存群排行数据失败:", error)
    }
  }

  /**
   * 从Redis获取缓存的群排行数据
   * @param {string} groupId - 群ID
   * @returns {Promise<Object|null>} 缓存数据或null
   */
  async getCachedGroupRankData(groupId) {
    try {
      if (redis.isConnected()) {
        const cacheKey = `ww:group_rank:${groupId}`
        const cached = await redis.get(cacheKey)

        if (cached) {
          const data = JSON.parse(cached)
          // 直接返回缓存数据，不检查时间（排行数据永久有效）
          return data
        }
      }
    } catch (error) {
      console.warn("获取缓存群排行数据失败:", error)
    }
    return null
  }

  /**
   * 生成群排行图片
   * @param {Object} e - 事件对象
   * @param {Array} rankData - 排行数据
   * @returns {Promise<string|null>} 图片base64或null
   */
  async generateGroupRankImage(e, rankData) {
    try {
      const renderData = {
        groupId: e.group_id,
        rankList: rankData.slice(0, 20), // 显示前20名
        updateTime: new Date().toLocaleString(),
        totalCount: rankData.length,
      }

      // 使用统一渲染工具
      return await renderer.render(e, "rank/group-rank", renderData)
    } catch (error) {
      console.error("生成群排行图片失败:", error)
      return null
    }
  }

  /**
   * 发送文本版排行榜
   * @param {Object} e - 事件对象
   * @param {Array} rankData - 排行数据
   * @param {string} title - 标题
   */
  async sendTextRank(e, rankData, title) {
    try {
      let message = `📊 ${title} (${rankData.length}人)\n\n`

      rankData.slice(0, 10).forEach((data, index) => {
        const medal = index < 3 ? ["🥇", "🥈", "🥉"][index] : `${index + 1}.`
        message += `${medal} UID:${data.uid}\n`
        message += `   战力:${data.totalPower} 等级:${data.maxLevel} 角色:${data.roleCount}个\n\n`
      })

      message += `\n💡 使用 ${config.getCommandExample("刷新面板")} 更新角色数据`
      await e.reply(message.trim())
    } catch (error) {
      console.error("发送文本排行失败:", error)
      await e.reply("❌ 排行榜生成失败")
    }
  }

  /**
   * 发送角色排行榜
   * @param {Object} e - 事件对象
   * @param {Array} charRankData - 角色排行数据
   * @param {string} charName - 角色名
   */
  async sendCharacterRank(e, charRankData, charName) {
    try {
      let message = `📊 群内 ${charName} 排行榜 (${charRankData.length}人)\n\n`

      charRankData.slice(0, 10).forEach((data, index) => {
        const medal = index < 3 ? ["🥇", "🥈", "🥉"][index] : `${index + 1}.`
        const stars = "★".repeat(data.starLevel)
        const weaponStars = data.weaponStarLevel ? `${data.weaponStarLevel}星` : ""

        message += `${medal} UID:${data.uid}\n`
        message += `   ${data.roleName} Lv.${data.level} ${stars}\n`
        message += `   ${data.attributeName ? `${data.attributeName}属性` : ""}\n`
        message += `   武器: ${data.weaponName} Lv.${data.weaponLevel} ${weaponStars}\n`
        message += `   战力: ${data.power}\n\n`
      })

      message += `💡 战力 = 角色等级×100 + 星级×1000 + 武器等级×50 + 声骸等级×20`
      await e.reply(message.trim())
    } catch (error) {
      console.error("发送角色排行失败:", error)
      await e.reply("❌ 角色排行榜生成失败")
    }
  }

  /**
   * 发送总排行榜
   * @param {Object} e - 事件对象
   * @param {Array} globalRankData - 总排行数据
   */
  async sendGlobalRank(e, globalRankData) {
    try {
      let message = `🌍 全服总排行榜 (前${globalRankData.length}名)\n\n`

      globalRankData.slice(0, 20).forEach((data, index) => {
        const medal = index < 3 ? ["🥇", "🥈", "🥉"][index] : `${index + 1}.`
        message += `${medal} UID:${data.uid}\n`
        message += `   战力:${data.totalPower} 等级:${data.maxLevel}\n`
        message += `   角色:${data.roleCount}个 服务器:${data.server || "未知"}\n\n`
      })

      message += `\n📊 数据来源: 库街区\n⏰ 更新时间: ${new Date().toLocaleString()}`
      await e.reply(message.trim())
    } catch (error) {
      console.error("发送总排行失败:", error)
      await e.reply("❌ 总排行榜生成失败")
    }
  }

  /**
   * 发送总角色排行榜
   * @param {Object} e - 事件对象
   * @param {Array} globalCharRankData - 总角色排行数据
   * @param {string} charName - 角色名
   */
  async sendGlobalCharRank(e, globalCharRankData, charName) {
    try {
      let message = `🌍 全服 ${charName} 排行榜 (前${globalCharRankData.length}名)\n\n`

      globalCharRankData.slice(0, 20).forEach((data, index) => {
        const medal = index < 3 ? ["🥇", "🥈", "🥉"][index] : `${index + 1}.`
        const stars = "★".repeat(data.starLevel || 0)

        message += `${medal} UID:${data.uid}\n`
        message += `   ${data.roleName} Lv.${data.level} ${stars}\n`
        message += `   武器: ${data.weaponName} Lv.${data.weaponLevel}\n`
        message += `   战力: ${data.power} 服务器: ${data.server || "未知"}\n\n`
      })

      message += `\n📊 数据来源: 库街区\n⏰ 更新时间: ${new Date().toLocaleString()}`
      await e.reply(message.trim())
    } catch (error) {
      console.error("发送总角色排行失败:", error)
      await e.reply("❌ 总角色排行榜生成失败")
    }
  }

  /**
   * 实时更新用户排行 - 参考miao-plugin模式
   * 在用户查看面板时调用此方法更新排行
   * @param {string} uid - 用户UID
   * @param {string} groupId - 群ID
   * @param {Object} characterData - 角色数据
   */
  static async updateUserRank(uid, groupId, characterData) {
    if (!redis.isConnected() || !groupId || !uid || !characterData) {
      return false
    }

    try {
      // 遍历用户的所有角色，更新排行
      if (characterData.characters && Array.isArray(characterData.characters)) {
        for (const char of characterData.characters) {
          await this.updateCharacterRank(uid, groupId, char)
        }
      }

      // 存储用户基本信息
      const userInfoKey = `rover:user:${uid}`
      const userInfo = {
        uid: uid,
        playerName: characterData.playerInfo?.name || "未知玩家",
        level: characterData.playerInfo?.level || 1,
        worldLevel: characterData.playerInfo?.worldLevel || 1,
        updateTime: new Date().toISOString(),
      }
      await redis.set(userInfoKey, JSON.stringify(userInfo))

      console.log(`✅ 已更新用户 ${uid} 的排行数据`)
      return true
    } catch (error) {
      console.error("更新用户排行失败:", error)
      return false
    }
  }

  /**
   * 更新单个角色排行
   * @param {string} uid - 用户UID
   * @param {string} groupId - 群ID
   * @param {Object} char - 角色数据
   */
  static async updateCharacterRank(uid, groupId, char) {
    if (!char || !char.roleName) {
      return false
    }

    try {
      // 计算角色分数（等级 + 星级权重）
      const levelScore = (char.level || 1) * 100
      const starScore = (char.starLevel || 1) * 1000
      const totalScore = levelScore + starScore

      // 使用miao-plugin风格的Redis键名
      const rankKey = `rover:rank:${groupId}:level:${char.roleName}`

      // 更新排行榜（使用有序集合）
      await redis.zAdd(rankKey, { score: totalScore, value: uid })

      return true
    } catch (error) {
      console.error("更新角色排行失败:", error)
      return false
    }
  }

  /**
   * 获取群内角色排行 - 使用实时Redis数据
   * @param {string} groupId - 群ID
   * @param {string} charName - 角色名称
   * @param {number} limit - 限制数量
   * @returns {Promise<Array>} 排行数据
   */
  static async getCharacterRank(groupId, charName, limit = 20) {
    if (!redis.isConnected() || !groupId || !charName) {
      return []
    }

    try {
      const rankKey = `rover:rank:${groupId}:level:${charName}`

      // 获取排行榜数据（按分数降序）
      const rankList = await redis.zRevRange(rankKey, 0, limit - 1, { WITHSCORES: true })

      if (!rankList || rankList.length === 0) {
        return []
      }

      // 构建排行榜数据
      const rankData = []
      for (let i = 0; i < rankList.length; i += 2) {
        const uid = rankList[i]
        const score = rankList[i + 1]

        // 从缓存获取角色详细信息
        const charInfoKey = `rover:char:${uid}:${charName}`
        const charInfo = await redis.get(charInfoKey)

        if (charInfo) {
          const info = JSON.parse(charInfo)
          rankData.push({
            rank: Math.floor(i / 2) + 1,
            uid: uid,
            roleName: info.roleName,
            level: info.level,
            starLevel: info.starLevel,
            attributeName: info.attributeName,
            weaponTypeName: info.weaponTypeName,
            score: score,
            updateTime: info.updateTime,
          })
        }
      }

      return rankData
    } catch (error) {
      console.error("获取角色排行失败:", error)
      return []
    }
  }
}

export default Rank
