.item-card {
  width: 52px;
  margin: 0px 0 5px 7px;
  position: relative;
}
.item-card .badge {
  overflow: hidden;
  border-radius: 5px;
  position: relative;
  background: #e9e5dc;
  box-shadow: 0 2px 6px 0 rgba(132, 93, 90, 0.3);
}
.item-card .badge img {
  width: 100%;
  overflow: hidden;
  background-size: 100%;
  background-repeat: no-repeat;
  position: absolute;
  top: 0;
  /*filter: contrast(95%);*/
}
.item-card .bg1 {
  background-image: url("imgs/bg1.png");
  width: 100%;
  height: 70px;
  background-size: 100%;
  background-repeat: no-repeat;
}
.item-card .bg2 {
  background-image: url("imgs/bg2.png");
  width: 100%;
  height: 70px;
  background-size: 100%;
  background-repeat: no-repeat;
}
.item-card .bg3 {
  background-image: url("imgs/bg3.png");
  width: 100%;
  height: 70px;
  background-size: 100%;
  background-repeat: no-repeat;
}
.item-card .bg4 {
  background-image: url("imgs/bg4.png");
  width: 100%;
  height: 70px;
  background-size: 100%;
  background-repeat: no-repeat;
}
.item-card .bg5 {
  background-image: url("imgs/bg5.png");
  width: 100%;
  height: 70px;
  background-size: 100%;
  background-repeat: no-repeat;
}
.item-card .box:after {
  content: "";
  display: block;
  position: absolute;
  width: 15px;
  right: 0;
  bottom: 15px;
}
.item-card .box .desc {
  font-weight: 500;
  text-align: center;
  position: absolute;
  bottom: 0;
  background: #e9e5dc;
  width: 100%;
  height: 16px;
  font-size: 12px;
  line-height: 16px;
  font-family: Number;
}
.item-card .box .name {
  overflow: hidden;
  white-space: nowrap;
  margin-top: 5px;
  font-weight: 500;
  text-align: center;
  font-size: 14px;
}
.item-card .life {
  position: absolute;
  top: 0px;
  left: 0px;
  z-index: 9;
  font-size: 13px;
  text-align: center;
  color: #fff;
  border-radius: 2px;
  padding: 1px 4px;
  border-radius: 3px;
  font-family: "tttgbnumber";
}
.item-card .life1 {
  background-color: #62a8ea;
}
.item-card .life2 {
  background-color: #62a8ea;
}
.item-card .life3 {
  background-color: #62a8ea;
}
.item-card .life4 {
  background-color: #ff5722;
}
.item-card .life5 {
  background-color: #ff5722;
}
/*# sourceMappingURL=item-card.less.map */