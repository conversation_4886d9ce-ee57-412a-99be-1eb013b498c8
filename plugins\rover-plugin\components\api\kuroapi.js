import ky from "ky"
import { API_CONFIG } from "../../utils/constants.js"
import { getPublicIp } from "../../utils/network.js"

import config from "../config.js"
import { WavesUser } from "../db/database.js"

// 获取完整URL
const getUrl = path => `${API_CONFIG.BASE_URL}${path}`

// API接口路径
const URI = {
  GET_SMS_CODE: "/user/getSmsCode", // 获取验证码
  SDK_LOGIN: "/user/sdkLogin", // 登录
  SDK_LOGIN_H5: "/user/sdkLoginForH5", // H5登录
  ROLE_LIST: "/gamer/role/list", // 角色列表
  LOGIN_LOG: "/user/login/log", // 登录日志
  REQUEST_TOKEN_URL: "/aki/roleBox/requestToken", // 获取bat token
  BASE_DATA: "/aki/roleBox/akiBox/baseData", // 基础数据
  ROLE_DATA: "/aki/roleBox/akiBox/roleData", // 角色数据
  ROLE_DETAIL: "/aki/roleBox/akiBox/getRoleDetail", // 角色详情

  // 扩展API接口
  CALABASH_DATA: "/aki/roleBox/akiBox/calabashData", // 数据坞
  EXPLORE_DATA: "/aki/roleBox/akiBox/exploreIndex", // 探索度
  CHALLENGE_DATA: "/aki/roleBox/akiBox/challengeDetails", // 全息战略
  CHALLENGE_INDEX: "/aki/roleBox/akiBox/challengeIndex", // 全息战略索引
  TOWER_INDEX: "/aki/roleBox/akiBox/towerIndex", // 深渊索引
  TOWER_DETAIL: "/aki/roleBox/akiBox/towerDataDetail", // 深渊详情
  SLASH_INDEX: "/aki/roleBox/akiBox/slashIndex", // 冥海索引
  SLASH_DETAIL: "/aki/roleBox/akiBox/slashDetail", // 冥海详情
  MORE_ACTIVITY: "/aki/roleBox/akiBox/moreActivity", // 更多活动
  DAILY_INFO: "/gamer/widget/game3/refresh", // 每日数据
  GACHA_LOG: "https://gmserver-api.aki-game2.com/gacha/record/query", // 抽卡记录

  // 计算器相关
  ONLINE_LIST_ROLE: "/aki/calculator/listRole", // 在线角色列表
  ONLINE_LIST_WEAPON: "/aki/calculator/listWeapon", // 在线武器列表
  ONLINE_LIST_PHANTOM: "/aki/calculator/listPhantom", // 在线声骸列表
  OWNED_ROLE: "/aki/calculator/queryOwnedRole", // 已拥有角色
  ROLE_CULTIVATE_STATUS: "/aki/calculator/roleCultivateStatus", // 角色培养状态
  BATCH_ROLE_COST: "/aki/calculator/batchRoleCost", // 批量角色成本

  // 资源简报
  PERIOD_LIST: "/aki/resource/period/list", // 资源简报列表
  MONTH_LIST: "/aki/resource/month", // 月报
  WEEK_LIST: "/aki/resource/week", // 周报
  VERSION_LIST: "/aki/resource/version", // 版本报告

  // Wiki相关
  WIKI_HOME: "/wiki/core/homepage/getPage", // Wiki首页
  WIKI_DETAIL: "/wiki/core/catalogue/item/getPage", // Wiki详情
  WIKI_ENTRY_DETAIL: "/wiki/core/catalogue/item/getEntryDetail", // 条目详情
}

// 库街区API接口类
class KuroApi {
  constructor() {
    this.defaultHeaders = {
      source: "ios",
      "Content-Type": "application/x-www-form-urlencoded; charset=utf-8",
      "User-Agent":
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
      version: "2.5.0",
    }
    this.kyApi = this.createKyInstance()
  }

  // 创建ky实例，支持代理配置
  createKyInstance() {
    const options = {
      timeout: 30000,
      retry: {
        limit: 2,
        methods: ["get", "post"],
        statusCodes: [408, 413, 429, 500, 502, 503, 504],
      },
    }

    // 代理配置将在每个请求中动态处理
    return ky.create(options)
  }

  // 生成随机设备码 - 按照WutheringWavesUID的generate_random_string方式
  generateRandomString(length = 32) {
    // 使用与WutheringWavesUID相同的字符集：字母+数字+标点符号
    const chars =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!\"#$%&'()*+,-./:;<=>?@[\\]^_`{|}~"
    let result = ""
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }

  // 获取通用请求头 - 按照WutheringWavesUID的get_common_header方式
  async getCommonHeader(platform = "ios") {
    const devCode = this.generateRandomString()
    return {
      source: platform,
      "Content-Type": "application/x-www-form-urlencoded; charset=utf-8",
      "User-Agent":
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
      devCode: devCode,
      version: "2.5.0",
    }
  }

  // 获取iOS设备码请求头 - 按照WutheringWavesUID的get_headers_ios方式
  async getHeadersIOS() {
    const ip = await getPublicIp()
    const userAgent =
      "Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) KuroGameBox/2.5.0"
    return {
      source: "ios",
      "Content-Type": "application/x-www-form-urlencoded; charset=utf-8",
      "User-Agent": userAgent,
      devCode: `${ip}, ${userAgent}`,
      version: "2.5.0",
    }
  }

  // 获取服务器ID
  getServerId(wavesId) {
    return API_CONFIG.SERVER_ID
  }

  // 通用请求方法，支持代理
  async makeRequest(url, headers, body, funcName = "unknown", forceProxy = null) {
    let needProxy
    if (forceProxy !== null) {
      needProxy = forceProxy
    } else {
      needProxy = config.needProxy(funcName)
    }

    const proxyUrl = needProxy ? config.getLocalProxyUrl() : null

    // 如果需要代理但没有配置代理URL，则使用直连
    const actuallyUseProxy = needProxy && proxyUrl

    console.log(`📡 请求: POST ${url}`)
    if (actuallyUseProxy) {
      console.log(`🌐 使用代理: ${proxyUrl}`)
    } else {
      if (needProxy && !proxyUrl) {
        console.log(`⚠️ 需要代理但未配置代理URL，使用直连`)
      } else {
        console.log(`🌐 直连请求`)
      }
    }

    const options = {
      headers,
      body: body,
    }

    if (actuallyUseProxy) {
      try {
        const { HttpsProxyAgent } = await import("https-proxy-agent")
        const agent = new HttpsProxyAgent(proxyUrl)
        options.agent = { https: agent }
        console.log(`🌐 代理请求: ${proxyUrl}`)
      } catch (error) {
        console.warn("代理配置失败，使用直连:", error.message)
      }
    }

    return await this.kyApi.post(url, options).json()
  }

  // 获取bat token - 完全按照WutheringWavesUID的实现方式
  async getRequestToken(wavesId, token, did, serverId = null, forceRefresh = false) {
    try {
      // 如果不强制刷新，先从数据库读取已有的bat token
      if (!forceRefresh) {
        try {
          const userAccount = await WavesUser.selectDataByCookieAndUid(token, wavesId)
          if (userAccount && userAccount.bat) {
            console.log(`🔑 [数据库缓存] bat token读取成功: ${userAccount.bat.substring(0, 10)}...`)
            return userAccount.bat
          }
        } catch (dbError) {
          console.warn("从数据库读取bat token失败:", dbError.message)
          // 继续执行获取新token的流程
        }
      }

      const url = getUrl(URI.REQUEST_TOKEN_URL)

      // 完全按照WutheringWavesUID的方式设置头部
      const headers = await this.getHeaders(token, "ios", wavesId)
      headers.token = token
      headers.did = did
      // 关键：清空b-at头部，这是WutheringWavesUID的做法
      delete headers["b-at"]

      const body = new URLSearchParams({
        serverId: serverId || this.getServerId(wavesId),
        roleId: wavesId,
      })

      console.log(`🔑 获取bat token: 角色=${wavesId}${forceRefresh ? " (强制刷新)" : ""}`)
      console.log(`📡 请求: POST ${url}`)

      const response = await this.makeRequest(url, headers, body, "getRequestToken")

      if (response.code === 200 || response.code === 10902) {
        let accessToken = ""
        const contentData = response.data

        if (typeof contentData === "string") {
          try {
            const jsonData = JSON.parse(contentData)
            accessToken = jsonData.accessToken || ""
          } catch (e) {
            console.error(`解析bat token失败: ${e}`)
          }
        } else if (typeof contentData === "object") {
          accessToken = contentData.accessToken || ""
        }

        if (accessToken) {
          console.log(`✅ bat token获取成功: ${accessToken.substring(0, 20)}...`)

          // 永久保存bat token到数据库
          try {
            // 直接使用原始SQL更新，避免Sequelize的问题
            const sequelize = (await import("../db/sequelize.js")).default
            const [results] = await sequelize.query(
              "UPDATE wavesuser SET bat = ? WHERE cookie = ? AND uid = ?",
              {
                replacements: [accessToken, token, wavesId],
              },
            )

            if (results.changes > 0) {
              console.log(`💾 bat token已保存到数据库`)
            } else {
              console.log(`⚠️ 数据库中未找到匹配记录，无法保存bat token`)
            }
          } catch (dbError) {
            console.warn("保存bat token到数据库失败:", dbError.message)
          }

          return accessToken
        } else {
          console.error("❌ 响应中未找到accessToken")
          return ""
        }
      } else {
        console.error("获取bat token失败:", response)
        return ""
      }
    } catch (error) {
      console.error("获取bat token异常:", error)
      return ""
    }
  }

  async getLoginLog(wavesId, token, did) {
    const url = getUrl(URI.LOGIN_LOG)

    const headers = {
      ...this.defaultHeaders,
      token: token,
      devCode: did,
    }

    const body = new URLSearchParams()

    try {
      const response = await this.makeRequest(url, headers, body, "getLoginLog")
      return response
    } catch (error) {
      console.error("获取登录日志失败:", error)
      throw error
    }
  }

  // 验证码登录 - 按照WutheringWavesUID的login方式
  async sdkLogin(mobile, code, did) {
    if (!did) {
      did = await this.getDevCode()
    }

    const url = getUrl(URI.SDK_LOGIN)
    // 使用iOS平台的头部，按照WutheringWavesUID的login_platform()返回"ios"
    const headers = await this.getHeadersIOS()

    const body = new URLSearchParams({
      mobile: mobile,
      code: code,
      devCode: did,
    })

    try {
      console.log(`🔐 尝试登录: 手机号=${mobile}, 设备ID=${did}`)
      console.log(`🔐 使用iOS平台登录，User-Agent=${headers["User-Agent"]}`)
      const response = await this.makeRequest(url, headers, body, "sdkLogin")
      return response
    } catch (error) {
      console.error("登录失败:", error)
      throw error
    }
  }

  // 获取用户绑定的游戏角色列表 - 完全按照WutheringWavesUID的get_kuro_role_list
  async getRoleList(token, did) {
    const url = getUrl(URI.ROLE_LIST)

    // 按照WutheringWavesUID的方式：使用login_platform()返回"ios"，然后使用get_common_header
    const platform = "ios" // login_platform() 返回 "ios"
    const headers = await this.getCommonHeader(platform)

    // 按照WutheringWavesUID的方式设置请求头
    headers.token = token
    headers.devCode = did

    const body = new URLSearchParams({
      gameId: API_CONFIG.GAME_ID,
    })

    try {
      console.log(`🔍 [KuroAPI] 获取角色列表请求`)
      console.log(`🔍 [KuroAPI] URL: ${url}`)
      console.log(`🔍 [KuroAPI] Token: ${token.substring(0, 20)}...`)
      console.log(`🔍 [KuroAPI] DID: ${did}`)
      console.log(`🔍 [KuroAPI] Platform: ${platform}`)

      const response = await this.makeRequest(url, headers, body, "get_kuro_role_list")

      // 检查风险检测
      if (response && response.code === 270) {
        console.warn(`⚠️ [KuroAPI] 检测到风险控制: ${response.msg}`)
        console.warn(`⚠️ [KuroAPI] 建议：1. 更换代理IP 2. 等待一段时间后重试 3. 检查请求频率`)

        // 返回特殊的错误信息，让调用方知道这是风险检测
        return {
          code: 270,
          msg: response.msg,
          isRiskDetection: true,
          suggestion: "当前网络环境被标记为风险，建议更换网络环境或稍后重试",
        }
      }

      return response
    } catch (error) {
      console.error("❌ [KuroAPI] 获取角色列表失败:", error)
      throw error
    }
  }

  // 获取鸣潮角色基础数据
  async getBaseData(wavesId, token, bat) {
    const url = getUrl(URI.BASE_DATA)

    const headers = {
      ...this.defaultHeaders,
      token: token,
      "b-at": bat,
    }

    const body = new URLSearchParams({
      gameId: API_CONFIG.GAME_ID,
      roleId: wavesId,
      serverId: this.getServerId(wavesId),
    })

    try {
      const response = await this.makeRequest(url, headers, body, "getBaseData")
      return response
    } catch (error) {
      console.error("获取基础数据失败:", error)
      throw error
    }
  }

  // 获取角色数据
  async getRoleData(wavesId, bat) {
    const url = getUrl(URI.ROLE_DATA)

    const headers = {
      ...this.defaultHeaders,
      "b-at": bat,
    }

    const body = new URLSearchParams({
      gameId: API_CONFIG.GAME_ID,
      roleId: wavesId,
      serverId: this.getServerId(wavesId),
    })

    try {
      const response = await this.makeRequest(url, headers, body, "getRoleData")
      return response
    } catch (error) {
      console.error("获取角色数据失败:", error)
      throw error
    }
  }

  // 获取角色详细信息
  async getRoleDetail(wavesId, charId, bat) {
    const url = getUrl(URI.ROLE_DETAIL)

    const headers = {
      ...this.defaultHeaders,
      "b-at": bat,
    }

    const body = new URLSearchParams({
      gameId: API_CONFIG.GAME_ID,
      serverId: this.getServerId(wavesId),
      roleId: wavesId,
      channelId: "19",
      countryCode: "1",
      id: charId,
    })

    try {
      const response = await this.makeRequest(url, headers, body, "get_role_detail_info")
      return response
    } catch (error) {
      console.error("获取角色详细信息失败:", error)
      throw error
    }
  }

  // ==================== 扩展API方法 ====================

  /**
   * 获取数据坞信息
   * @param {string} wavesId - 鸣潮UID
   * @param {string} bat - bat token
   * @returns {Promise<Object>} API响应
   */
  async getCalabashData(wavesId, bat) {
    const url = getUrl(URI.CALABASH_DATA)

    const headers = {
      ...this.defaultHeaders,
      "b-at": bat,
    }

    const body = new URLSearchParams({
      gameId: API_CONFIG.GAME_ID,
      roleId: wavesId,
      serverId: this.getServerId(wavesId),
    })

    try {
      const response = await this.makeRequest(url, headers, body, "getCalabashData")
      return response
    } catch (error) {
      console.error("获取数据坞信息失败:", error)
      throw error
    }
  }

  /**
   * 获取探索度数据
   * @param {string} wavesId - 鸣潮UID
   * @param {string} bat - bat token
   * @param {string} countryCode - 国家代码，默认为"1"
   * @returns {Promise<Object>} API响应
   */
  async getExploreData(wavesId, bat, countryCode = "1") {
    const url = getUrl(URI.EXPLORE_DATA)

    const headers = {
      ...this.defaultHeaders,
      "b-at": bat,
    }

    const body = new URLSearchParams({
      gameId: API_CONFIG.GAME_ID,
      roleId: wavesId,
      serverId: this.getServerId(wavesId),
      countryCode: countryCode,
    })

    try {
      const response = await this.makeRequest(url, headers, body, "getExploreData")
      return response
    } catch (error) {
      console.error("获取探索度数据失败:", error)
      throw error
    }
  }

  /**
   * 获取全息战略数据
   * @param {string} wavesId - 鸣潮UID
   * @param {string} bat - bat token
   * @returns {Promise<Object>} API响应
   */
  async getChallengeData(wavesId, bat) {
    const url = getUrl(URI.CHALLENGE_DATA)

    const headers = {
      ...this.defaultHeaders,
      "b-at": bat,
    }

    const body = new URLSearchParams({
      gameId: API_CONFIG.GAME_ID,
      roleId: wavesId,
      serverId: this.getServerId(wavesId),
    })

    try {
      const response = await this.makeRequest(url, headers, body, "getChallengeData")
      return response
    } catch (error) {
      console.error("获取全息战略数据失败:", error)
      throw error
    }
  }

  /**
   * 获取深渊索引
   * @param {string} wavesId - 鸣潮UID
   * @param {string} bat - bat token
   * @returns {Promise<Object>} API响应
   */
  async getTowerIndex(wavesId, bat) {
    const url = getUrl(URI.TOWER_INDEX)

    const headers = {
      ...this.defaultHeaders,
      "b-at": bat,
    }

    const body = new URLSearchParams({
      gameId: API_CONFIG.GAME_ID,
      roleId: wavesId,
      serverId: this.getServerId(wavesId),
    })

    try {
      const response = await this.makeRequest(url, headers, body, "getTowerIndex")
      return response
    } catch (error) {
      console.error("获取深渊索引失败:", error)
      throw error
    }
  }

  /**
   * 获取深渊详情
   * @param {string} wavesId - 鸣潮UID
   * @param {string} bat - bat token
   * @returns {Promise<Object>} API响应
   */
  async getTowerDetail(wavesId, bat) {
    const url = getUrl(URI.TOWER_DETAIL)

    const headers = {
      ...this.defaultHeaders,
      "b-at": bat,
    }

    const body = new URLSearchParams({
      gameId: API_CONFIG.GAME_ID,
      roleId: wavesId,
      serverId: this.getServerId(wavesId),
    })

    try {
      const response = await this.makeRequest(url, headers, body, "getTowerDetail")
      return response
    } catch (error) {
      console.error("获取深渊详情失败:", error)
      throw error
    }
  }

  /**
   * 获取冥海索引
   * @param {string} wavesId - 鸣潮UID
   * @param {string} bat - bat token
   * @returns {Promise<Object>} API响应
   */
  async getSlashIndex(wavesId, bat) {
    const url = getUrl(URI.SLASH_INDEX)

    const headers = {
      ...this.defaultHeaders,
      "b-at": bat,
    }

    const body = new URLSearchParams({
      gameId: API_CONFIG.GAME_ID,
      roleId: wavesId,
      serverId: this.getServerId(wavesId),
    })

    try {
      const response = await this.makeRequest(url, headers, body, "getSlashIndex")
      return response
    } catch (error) {
      console.error("获取冥海索引失败:", error)
      throw error
    }
  }

  /**
   * 获取冥海详情
   * @param {string} wavesId - 鸣潮UID
   * @param {string} bat - bat token
   * @returns {Promise<Object>} API响应
   */
  async getSlashDetail(wavesId, bat) {
    const url = getUrl(URI.SLASH_DETAIL)

    const headers = {
      ...this.defaultHeaders,
      "b-at": bat,
    }

    const body = new URLSearchParams({
      gameId: API_CONFIG.GAME_ID,
      roleId: wavesId,
      serverId: this.getServerId(wavesId),
    })

    try {
      const response = await this.makeRequest(url, headers, body, "getSlashDetail")
      return response
    } catch (error) {
      console.error("获取冥海详情失败:", error)
      throw error
    }
  }

  /**
   * 获取更多活动数据（浸梦海床+激斗！向着荣耀之丘）
   * @param {string} wavesId - 鸣潮UID
   * @param {string} bat - bat token
   * @returns {Promise<Object>} API响应
   */
  async getMoreActivity(wavesId, bat) {
    const url = getUrl(URI.MORE_ACTIVITY)

    const headers = {
      ...this.defaultHeaders,
      "b-at": bat,
    }

    const body = new URLSearchParams({
      gameId: API_CONFIG.GAME_ID,
      roleId: wavesId,
      serverId: this.getServerId(wavesId),
    })

    try {
      const response = await this.makeRequest(url, headers, body, "getMoreActivity")
      return response
    } catch (error) {
      console.error("获取更多活动数据失败:", error)
      throw error
    }
  }

  /**
   * 获取每日数据
   * @param {string} wavesId - 鸣潮UID
   * @param {string} token - 用户token
   * @returns {Promise<Object>} API响应
   */
  async getDailyInfo(wavesId, token) {
    const url = getUrl(URI.DAILY_INFO)

    const headers = {
      ...this.defaultHeaders,
      token: token,
    }

    const body = new URLSearchParams({
      type: "1",
      sizeType: "2",
      gameId: API_CONFIG.GAME_ID,
      serverId: this.getServerId(wavesId),
      roleId: wavesId,
    })

    try {
      const response = await this.makeRequest(url, headers, body, "getDailyInfo")
      return response
    } catch (error) {
      console.error("获取每日数据失败:", error)
      throw error
    }
  }

  /**
   * 获取抽卡记录
   * @param {string} cardPoolType - 卡池类型
   * @param {string} recordId - 记录ID
   * @param {string} wavesId - 鸣潮UID
   * @returns {Promise<Object>} API响应
   */
  async getGachaLog(cardPoolType, recordId, wavesId) {
    const isNet = parseInt(wavesId) >= 200000000
    const url = isNet ? "https://gmserver-api.aki-game2.net/gacha/record/query" : URI.GACHA_LOG

    const headers = {
      "Content-Type": "application/json;charset=UTF-8",
    }

    const body = {
      playerId: wavesId,
      cardPoolType: cardPoolType,
      serverId: this.getServerId(wavesId),
      languageCode: "zh-Hans",
      recordId: recordId,
    }

    try {
      const response = await this.kyApi
        .post(url, {
          headers: headers,
          json: body,
          agent: this.getProxyAgent(),
        })
        .json()
      return response
    } catch (error) {
      console.error("获取抽卡记录失败:", error)
      throw error
    }
  }

  // ==================== 计算器相关API ====================

  /**
   * 获取在线角色列表
   * @param {string} token - 用户token
   * @returns {Promise<Object>} API响应
   */
  async getOnlineListRole(token) {
    const url = getUrl(URI.ONLINE_LIST_ROLE)

    const headers = {
      ...this.defaultHeaders,
      token: token,
    }

    try {
      const response = await this.makeRequest(url, headers, {}, "getOnlineListRole")
      return response
    } catch (error) {
      console.error("获取在线角色列表失败:", error)
      throw error
    }
  }

  /**
   * 获取在线武器列表
   * @param {string} token - 用户token
   * @returns {Promise<Object>} API响应
   */
  async getOnlineListWeapon(token) {
    const url = getUrl(URI.ONLINE_LIST_WEAPON)

    const headers = {
      ...this.defaultHeaders,
      token: token,
    }

    try {
      const response = await this.makeRequest(url, headers, {}, "getOnlineListWeapon")
      return response
    } catch (error) {
      console.error("获取在线武器列表失败:", error)
      throw error
    }
  }

  /**
   * 获取在线声骸列表
   * @param {string} token - 用户token
   * @returns {Promise<Object>} API响应
   */
  async getOnlineListPhantom(token) {
    const url = getUrl(URI.ONLINE_LIST_PHANTOM)

    const headers = {
      ...this.defaultHeaders,
      token: token,
    }

    try {
      const response = await this.makeRequest(url, headers, {}, "getOnlineListPhantom")
      return response
    } catch (error) {
      console.error("获取在线声骸列表失败:", error)
      throw error
    }
  }

  /**
   * 获取已拥有角色
   * @param {string} wavesId - 鸣潮UID
   * @param {string} token - 用户token
   * @returns {Promise<Object>} API响应
   */
  async getOwnedRole(wavesId, token) {
    const url = getUrl(URI.OWNED_ROLE)

    const headers = {
      ...this.defaultHeaders,
      token: token,
    }

    const body = new URLSearchParams({
      serverId: this.getServerId(wavesId),
      roleId: wavesId,
    })

    try {
      const response = await this.makeRequest(url, headers, body, "getOwnedRole")
      return response
    } catch (error) {
      console.error("获取已拥有角色失败:", error)
      throw error
    }
  }

  /**
   * 获取角色培养状态
   * @param {string} wavesId - 鸣潮UID
   * @param {string} token - 用户token
   * @param {Array<string>} charIds - 角色ID列表
   * @returns {Promise<Object>} API响应
   */
  async getRoleCultivateStatus(wavesId, token, charIds) {
    const url = getUrl(URI.ROLE_CULTIVATE_STATUS)

    const headers = {
      ...this.defaultHeaders,
      token: token,
    }

    const body = new URLSearchParams({
      serverId: this.getServerId(wavesId),
      roleId: wavesId,
      ids: charIds.join(","),
    })

    try {
      const response = await this.makeRequest(url, headers, body, "getRoleCultivateStatus")
      return response
    } catch (error) {
      console.error("获取角色培养状态失败:", error)
      throw error
    }
  }

  /**
   * 获取批量角色成本
   * @param {string} wavesId - 鸣潮UID
   * @param {string} token - 用户token
   * @param {Array} content - 计算内容
   * @returns {Promise<Object>} API响应
   */
  async getBatchRoleCost(wavesId, token, content) {
    const url = getUrl(URI.BATCH_ROLE_COST)

    const headers = {
      ...this.defaultHeaders,
      token: token,
    }

    const body = new URLSearchParams({
      serverId: this.getServerId(wavesId),
      roleId: wavesId,
      content: JSON.stringify(content),
    })

    try {
      const response = await this.makeRequest(url, headers, body, "getBatchRoleCost")
      return response
    } catch (error) {
      console.error("获取批量角色成本失败:", error)
      throw error
    }
  }

  // ==================== 资源简报相关API ====================

  /**
   * 获取资源简报列表
   * @param {string} wavesId - 鸣潮UID
   * @param {string} token - 用户token
   * @returns {Promise<Object>} API响应
   */
  async getPeriodList(wavesId, token) {
    const url = getUrl(URI.PERIOD_LIST)

    const headers = {
      ...this.defaultHeaders,
      token: token,
    }

    try {
      const response = await this.makeRequest(url, headers, {}, "getPeriodList")
      return response
    } catch (error) {
      console.error("获取资源简报列表失败:", error)
      throw error
    }
  }

  /**
   * 获取资源简报详情
   * @param {string} type - 类型 (month/week/version)
   * @param {string|number} period - 期数
   * @param {string} wavesId - 鸣潮UID
   * @param {string} token - 用户token
   * @returns {Promise<Object>} API响应
   */
  async getPeriodDetail(type, period, wavesId, token) {
    let url
    switch (type) {
      case "month":
        url = getUrl(URI.MONTH_LIST)
        break
      case "week":
        url = getUrl(URI.WEEK_LIST)
        break
      case "version":
        url = getUrl(URI.VERSION_LIST)
        break
      default:
        throw new Error(`不支持的类型: ${type}`)
    }

    const headers = {
      ...this.defaultHeaders,
      token: token,
    }

    const body = new URLSearchParams({
      serverId: this.getServerId(wavesId),
      roleId: wavesId,
      period: period,
    })

    try {
      const response = await this.makeRequest(url, headers, body, "getPeriodDetail")
      return response
    } catch (error) {
      console.error("获取资源简报详情失败:", error)
      throw error
    }
  }

  // ==================== Wiki相关API ====================

  /**
   * 获取Wiki首页
   * @returns {Promise<Object>} API响应
   */
  async getWikiHome() {
    const url = getUrl(URI.WIKI_HOME)

    const headers = {
      ...this.defaultHeaders,
      wiki_type: "9",
    }

    try {
      const response = await this.makeRequest(url, headers, {}, "getWikiHome")
      return response
    } catch (error) {
      console.error("获取Wiki首页失败:", error)
      throw error
    }
  }

  /**
   * 获取Wiki详情
   * @param {string} catalogueId - 目录ID
   * @returns {Promise<Object>} API响应
   */
  async getWiki(catalogueId) {
    const url = getUrl(URI.WIKI_DETAIL)

    const headers = {
      ...this.defaultHeaders,
      wiki_type: "9",
    }

    const body = new URLSearchParams({
      catalogueId: catalogueId,
      limit: 1000,
    })

    try {
      const response = await this.makeRequest(url, headers, body, "getWiki")
      return response
    } catch (error) {
      console.error("获取Wiki详情失败:", error)
      throw error
    }
  }

  /**
   * 获取条目详情
   * @param {string} entryId - 条目ID
   * @returns {Promise<Object>} API响应
   */
  async getEntryDetail(entryId) {
    const url = getUrl(URI.WIKI_ENTRY_DETAIL)

    const headers = {
      ...this.defaultHeaders,
      wiki_type: "9",
    }

    const body = new URLSearchParams({
      id: entryId,
    })

    try {
      const response = await this.makeRequest(url, headers, body, "getEntryDetail")
      return response
    } catch (error) {
      console.error("获取条目详情失败:", error)
      throw error
    }
  }

  // ==================== 资源简报相关API ====================

  /**
   * 获取资源简报列表
   * @param {string} wavesId - 鸣潮UID
   * @param {string} token - 用户token
   * @returns {Promise<Object>} API响应
   */
  async getPeriodList(wavesId, token) {
    const url = getUrl(URI.PERIOD_LIST)

    const headers = {
      ...this.defaultHeaders,
      token: token,
    }

    try {
      const response = await this.makeRequest(url, headers, {}, "getPeriodList")
      return response
    } catch (error) {
      console.error("获取资源简报列表失败:", error)
      throw error
    }
  }

  /**
   * 获取资源简报详情
   * @param {string} type - 类型 (month/week/version)
   * @param {string|number} period - 期数
   * @param {string} wavesId - 鸣潮UID
   * @param {string} token - 用户token
   * @returns {Promise<Object>} API响应
   */
  async getPeriodDetail(type, period, wavesId, token) {
    let url
    switch (type) {
      case "month":
        url = getUrl(URI.MONTH_LIST)
        break
      case "week":
        url = getUrl(URI.WEEK_LIST)
        break
      case "version":
        url = getUrl(URI.VERSION_LIST)
        break
      default:
        throw new Error(`不支持的类型: ${type}`)
    }

    const headers = {
      ...this.defaultHeaders,
      token: token,
    }

    const body = new URLSearchParams({
      serverId: this.getServerId(wavesId),
      roleId: wavesId,
      period: period,
    })

    try {
      const response = await this.makeRequest(url, headers, body, "getPeriodDetail")
      return response
    } catch (error) {
      console.error("获取资源简报详情失败:", error)
      throw error
    }
  }
}

export default KuroApi
