<!DOCTYPE html>
<html lang="zh-cn">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width">
  <link rel="shortcut icon" href="#"/>
  <link rel="preload" href="{{_res_path}}/common/font/HYWH-65W.woff" as="font" type="font/woff">
  <link rel="preload" href="{{_res_path}}/common/font/NZBZ.woff" as="font" type="font/woff">
  <link rel="preload" href="{{_res_path}}/common/font/tttgbnumber.woff" as="font" type="font/woff">
  <link rel="stylesheet" type="text/css" href="{{_res_path}}/common/common.css"/>
  <link rel="stylesheet" type="text/css" href="{{_res_path}}/character/profile-detail.css"/>
  <link rel="stylesheet" type="text/css" href="{{_res_path}}/character/ww-cards.css"/>
  {{if background}}{{@background}}{{/if}}
  <title>鸣潮角色卡片</title>
</head>
{{set elemCls = {火:'pyro',冰:'cryo',风:'anemo',雷:'electro',量子:'quantum',虚数:'geo',物理:'sr', }[element||elem] || element || elem || 'hydro' }}
<body class="elem-{{elemCls}} {{displayMode || mode || `default`}}-mode {{bodyClass}}" {{sys.scale}}>
<div class="container elem-bg" id="container">
{{set demo = chars[0]?.abbr || "长离" }}
<div class="profile-cont game-{{game}}">
  <!-- 头部信息区域 - WutheringWavesUID 风格 -->
  <div class="head-box ww-style">
    <div class="header-bg">
      <div class="header-content">
        <div class="title-area">
          <div class="main-title">角色卡片</div>
          <div class="sub-title">Character Cards</div>
        </div>
        <div class="uid-area">
          <div class="uid-label">UID</div>
          <div class="uid-value">{{uid}}</div>
        </div>
      </div>
    </div>
    <div class="stats-bar">
      <div class="stat-item">
        <span class="stat-label">角色数量</span>
        <span class="stat-value">{{chars.length}}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">更新时间</span>
        <span class="stat-value">{{updateTime.profile}}</span>
      </div>
    </div>
  </div>
  <!-- 角色卡片网格 - WutheringWavesUID 风格 -->
  <div class="char-cards-container ww-style">
    <div class="char-grid">
      {{each chars char}}
      <div class="char-card rarity-{{char.star}}">
        <!-- 卡片背景装饰 -->
        <div class="card-bg">
          <div class="rarity-bg rarity-{{char.star}}"></div>
          <div class="card-frame"></div>
        </div>

        <!-- 角色头像 -->
        <div class="char-avatar">
          <div class="avatar-container">
            <img src="{{_res_path}}{{char.face}}" alt="{{char.name}}" class="avatar-img">
            <div class="avatar-border rarity-{{char.star}}"></div>
          </div>
        </div>

        <!-- 星级显示 -->
        <div class="star-container">
          <div class="stars rarity-{{char.star}}">
            {{char.starDisplay}}
          </div>
        </div>

        <!-- 角色信息 -->
        <div class="char-info">
          <div class="char-name">{{char.name}}</div>
          <div class="char-level">Lv.{{char.level}}</div>
          {{if char.cons > 0}}
          <div class="char-cons">{{char.cons}}命</div>
          {{/if}}
        </div>

        <!-- 元素图标 -->
        <div class="element-icon">
          <div class="element-bg"></div>
        </div>

        <!-- 更新状态 -->
        {{if char.isUpdate && hasNew}}
        <div class="status-badge updated">
          <span>已更新</span>
        </div>
        {{else if char.isNew && hasNew}}
        <div class="status-badge new">
          <span>新获得</span>
        </div>
        {{/if}}
      </div>
      {{/each}}
    </div>
  </div>
  <!-- 底部信息区域 - WutheringWavesUID 风格 -->
  <div class="footer-section ww-style">
    <div class="footer-bg">
      <div class="footer-content">
        {{if hasNew}}
        <div class="status-legend">
          <div class="legend-item">
            <div class="legend-dot new"></div>
            <span>新获得</span>
          </div>
          <div class="legend-item">
            <div class="legend-dot updated"></div>
            <span>已更新</span>
          </div>
        </div>
        {{/if}}

        <div class="footer-info">
          <div class="server-name">{{servName}}</div>
          <div class="tips">使用 #{{demo}}面板 查看详细信息</div>
        </div>
      </div>
    </div>
  </div>
</div>

  <!-- 版权信息 -->
  <div class="copyright ww-copyright">{{@copyright || sys?.copyright}}</div>
</div>
</body>
</html>
