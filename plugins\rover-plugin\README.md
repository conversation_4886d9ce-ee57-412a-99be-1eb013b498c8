# 🌊 Rover Plugin API

基于WutheringWavesUID的核心API实现，为 Yunzai-Bot 提供完整的鸣潮游戏数据接口。

## 📋 API概述

rover-plugin 提供了完整的鸣潮游戏数据API接口，支持：

- 🔐 **用户绑定管理** - 用户账号绑定状态检查和管理
- 🎮 **游戏数据获取** - 角色、探索、深渊、冥海等完整游戏数据
- 🧮 **计算器功能** - 角色培养成本计算、在线数据查询
- 📊 **资源简报** - 月报、周报、版本报告等数据统计
- 🎲 **抽卡记录** - 各卡池抽卡历史记录查询
- 💾 **数据库操作** - 用户数据存储和查询

## ✨ 核心特性

- **🔄 智能Token管理**: 自动处理bat token刷新，外部插件无需关心token状态
- **🛡️ 自动重试机制**: API调用失败时自动刷新token并重试
- **📊 统一接口设计**: 提供RoverAPI统一入口，简化外部插件调用
- **⚡ 并行数据获取**: 支持并行获取多种游戏数据，提升性能
- **🔍 完整错误处理**: 详细的错误信息和状态码，便于调试

### 基础导入

```javascript
// 推荐：使用统一API接口
import { RoverAPI } from "../rover-plugin/lib/api.js"

// 或者：按需导入分类接口
import { UserBinding, KuroAPI, Database, Config } from "../rover-plugin/lib/api.js"
```

### 智能API调用示例

```javascript
// 检查用户绑定状态
const isUserBound = await RoverAPI.isUserBound(userId)

// 刷新用户面板数据 (自动处理token刷新)
const refreshResult = await RoverAPI.refreshUserPanel(userId)

// 获取用户游戏数据 (基础数据)
const gameData = await RoverAPI.getUserGameData(userId, false)

// 获取用户游戏数据 (包含详细数据)
const detailedGameData = await RoverAPI.getUserGameData(userId, true)

// 智能API调用 - 自动处理token刷新和重试
const baseData = await RoverAPI.getBaseData(userId)
const roleData = await RoverAPI.getRoleData(userId)
const exploreData = await RoverAPI.getExploreData(userId)
```

## � 统一API接口（推荐使用）

`RoverAPI` 类提供了所有API的统一调用入口，简化外部插件的使用：

### 用户绑定检查
```javascript
// 检查用户是否已绑定
const isBound = await RoverAPI.isUserBound(userId)

// 获取详细绑定信息
const bindingInfo = await RoverAPI.checkUserBinding(userId)

// 获取用户基础信息
const uid = await RoverAPI.getUserUid(userId)
const token = await RoverAPI.getUserToken(userId)
const batToken = await RoverAPI.getUserBatToken(userId)
```

### 高级组合API
```javascript
// 获取用户完整游戏数据（推荐）
const completeData = await RoverAPI.getUserCompleteData(userId)
if (completeData.success) {
  const { baseData, roleData, calabashData, exploreData, towerIndex } = completeData.data
}

// 获取计算器相关数据
const calculatorData = await RoverAPI.getCalculatorData(userId)
if (calculatorData.success) {
  const { onlineRoles, onlineWeapons, ownedRoles } = calculatorData.data
}

// 获取资源简报数据
const reports = await RoverAPI.getResourceReports(userId)
if (reports.success) {
  const { monthDetail, weekDetail, versionDetail } = reports.data
}
```

### 单个API调用
```javascript
// 基础游戏数据
const baseData = await RoverAPI.getBaseData(uid, token, batToken)
const roleData = await RoverAPI.getRoleData(uid, batToken)
const exploreData = await RoverAPI.getExploreData(uid, batToken)

// 深渊和冥海数据
const towerIndex = await RoverAPI.getTowerIndex(uid, batToken)
const slashIndex = await RoverAPI.getSlashIndex(uid, batToken)

// 抽卡记录
const gachaLog = await RoverAPI.getGachaLog("1", "0", uid)

// 计算器功能
const onlineRoles = await RoverAPI.getOnlineListRole(token)
const ownedRoles = await RoverAPI.getOwnedRole(uid, token)
```

## �📚 分类API接口说明

### UserBinding - 用户绑定管理

```javascript
// 检查用户是否已绑定（简化版）
const isBound = await UserBinding.isUserBound(userId)

// 获取详细绑定信息
const bindingInfo = await UserBinding.checkUserBinding(userId)
// 返回: { userId, hasBinding, hasToken, hasValidAccount, uid, token, did, batToken, accountStatus }

// 获取用户基础信息
const uid = await UserBinding.getUserUid(userId)
const token = await UserBinding.getUserToken(userId)
const batToken = await UserBinding.getUserBatToken(userId)
```

### 库街区API调用

```javascript
// 推荐：使用统一的面板刷新接口
const panelResult = await KuroAPI.refreshUserPanel(userId)
if (panelResult.success) {
  console.log("面板数据:", panelResult.data)
}

// 获取用户完整游戏数据 (bat token会自动刷新)
const gameData = await KuroAPI.getUserGameData(userId, true) // 强制刷新
if (gameData.success) {
  console.log("游戏数据:", gameData.data)
}

// 手动调用单个API (不推荐，仅作备用)
const roleList = await KuroAPI.getRoleList(token, did)
const roleData = await KuroAPI.getRoleData(uid, batToken)
```

### 扩展API调用 (v2.3.0+)

基于WutheringWavesUID项目，rover-plugin现已支持完整的鸣潮游戏API接口：

```javascript
// 游戏内容数据
const calabashData = await KuroAPI.getCalabashData(uid, batToken)      // 数据坞
const exploreData = await KuroAPI.getExploreData(uid, batToken)        // 探索度
const challengeData = await KuroAPI.getChallengeData(uid, batToken)    // 全息战略
const towerIndex = await KuroAPI.getTowerIndex(uid, batToken)          // 深渊索引
const towerDetail = await KuroAPI.getTowerDetail(uid, batToken)        // 深渊详情
const slashIndex = await KuroAPI.getSlashIndex(uid, batToken)          // 冥海索引
const slashDetail = await KuroAPI.getSlashDetail(uid, batToken)        // 冥海详情
const moreActivity = await KuroAPI.getMoreActivity(uid, batToken)      // 更多活动
const dailyInfo = await KuroAPI.getDailyInfo(uid, token)               // 每日数据

// 抽卡记录
const gachaLog = await KuroAPI.getGachaLog("1", "0", uid)              // 抽卡记录

// 计算器相关
const onlineRoles = await KuroAPI.getOnlineListRole(token)             // 在线角色列表
const onlineWeapons = await KuroAPI.getOnlineListWeapon(token)         // 在线武器列表
const onlinePhantoms = await KuroAPI.getOnlineListPhantom(token)       // 在线声骸列表
const ownedRoles = await KuroAPI.getOwnedRole(uid, token)              // 已拥有角色
const cultivateStatus = await KuroAPI.getRoleCultivateStatus(uid, token, charIds) // 角色培养状态
const roleCost = await KuroAPI.getBatchRoleCost(uid, token, content)   // 批量角色成本

// 资源简报
const periodList = await KuroAPI.getPeriodList(uid, token)             // 简报列表
const monthDetail = await KuroAPI.getPeriodDetail("month", period, uid, token)    // 月报
const weekDetail = await KuroAPI.getPeriodDetail("week", period, uid, token)      // 周报
const versionDetail = await KuroAPI.getPeriodDetail("version", period, uid, token) // 版本报告
```

### Database - 数据库操作

```javascript
// 直接使用数据库类
const userAccount = await Database.WavesUser.getPrimaryAccount(userId)
const bindUid = await Database.WavesBind.getUidByUser(userId)
```

## 🎯 外部插件调用示例

### 使用统一API接口（推荐）

```javascript
// 外部插件文件: plugins/my-plugin/apps/waves-data.js
import { RoverAPI } from "../rover-plugin/lib/api.js"

export class WavesDataApp extends plugin {
  constructor() {
    super({
      name: "鸣潮数据查询",
      dsc: "查询鸣潮游戏数据",
      event: "message",
      priority: 5000,
      rule: [
        {
          reg: "^#鸣潮数据$",
          fnc: "getUserData"
        },
        {
          reg: "^#鸣潮完整数据$",
          fnc: "getCompleteData"
        },
        {
          reg: "^#鸣潮计算器$",
          fnc: "getCalculatorData"
        }
      ]
    })
  }

  // 基础数据查询
  async getUserData(e) {
    // 检查绑定状态
    const isBound = await RoverAPI.isUserBound(e.user_id)
    if (!isBound) {
      await e.reply("请先使用 #鸣潮绑定 命令绑定账号")
      return
    }

    try {
      // 获取基础数据
      const uid = await RoverAPI.getUserUid(e.user_id)
      const token = await RoverAPI.getUserToken(e.user_id)
      const batToken = await RoverAPI.getUserBatToken(e.user_id)

      const baseData = await RoverAPI.getBaseData(uid, token, batToken)
      const exploreData = await RoverAPI.getExploreData(uid, batToken)

      if (baseData?.code === 200) {
        const msg = [
          `鸣潮数据查询 - UID: ${uid}`,
          `等级: ${baseData.data.level}`,
          `世界等级: ${baseData.data.worldLevel}`,
          exploreData?.code === 200 ? `探索度: ${exploreData.data.totalPercent}%` : ""
        ].filter(Boolean).join("\n")

        await e.reply(msg)
      } else {
        await e.reply("获取数据失败，请检查绑定状态")
      }
    } catch (error) {
      console.error("获取鸣潮数据失败:", error)
      await e.reply("获取数据时发生错误")
    }
  }

  // 完整数据查询（推荐）
  async getCompleteData(e) {
    try {
      const result = await RoverAPI.getUserCompleteData(e.user_id)

      if (result.success) {
        const { uid, baseData, roleData, exploreData, towerIndex } = result.data

        const msg = [
          `鸣潮完整数据 - UID: ${uid}`,
          baseData?.code === 200 ? `等级: ${baseData.data.level}` : "",
          roleData?.code === 200 ? `角色数量: ${roleData.data.roleList?.length || 0}` : "",
          exploreData?.code === 200 ? `探索度: ${exploreData.data.totalPercent}%` : "",
          towerIndex?.code === 200 ? `深渊层数: ${towerIndex.data.maxFloor}` : ""
        ].filter(Boolean).join("\n")

        await e.reply(msg)
      } else {
        await e.reply(result.message)
      }
    } catch (error) {
      console.error("获取完整数据失败:", error)
      await e.reply("获取数据时发生错误")
    }
  }

  // 计算器数据查询
  async getCalculatorData(e) {
    try {
      const result = await RoverAPI.getCalculatorData(e.user_id)

      if (result.success) {
        const { uid, onlineRoles, ownedRoles } = result.data

        const msg = [
          `鸣潮计算器数据 - UID: ${uid}`,
          onlineRoles?.code === 200 ? `在线角色: ${onlineRoles.data.length}个` : "",
          ownedRoles?.code === 200 ? `已拥有角色: ${ownedRoles.data.length}个` : ""
        ].filter(Boolean).join("\n")

        await e.reply(msg)
      } else {
        await e.reply(result.message)
      }
    } catch (error) {
      console.error("获取计算器数据失败:", error)
      await e.reply("获取数据时发生错误")
    }
  }
}
```

### 错误处理最佳实践

```javascript
async function safeApiCall(userId) {
  try {
    // 使用统一API接口
    const result = await RoverAPI.getUserCompleteData(userId)

    if (result.success) {
      return {
        success: true,
        data: result.data
      }
    } else {
      // 根据错误码处理不同情况
      switch (result.code) {
        case "NO_BINDING":
          return {
            success: false,
            message: "用户未绑定账号，请先使用 #鸣潮绑定 命令"
          }
        case "API_ERROR":
          return {
            success: false,
            message: "API调用失败，请稍后重试"
          }
        default:
          return {
            success: false,
            message: result.message || "未知错误"
          }
      }
    }
  } catch (error) {
    console.error("API调用异常:", error)
    return {
      success: false,
      message: "系统错误，请联系管理员"
    }
  }
}
```

## ⚠️ 注意事项

1. **参数顺序**: 严格按照文档中的参数顺序传递
2. **错误处理**: 始终检查API返回的code字段
3. **绑定检查**: 调用API前必须先检查用户绑定状态
4. **Token有效性**: bat token可能过期，需要处理刷新逻辑
5. **异步调用**: 所有API方法都是异步的，需要使用await
6. **用户ID唯一性**: 所有API只需要用户ID即可，无需botId参数



---

**rover-plugin** 提供了完整的鸣潮游戏数据API接口，让其他插件能够轻松获取和处理游戏数据。
