<!DOCTYPE html>
<html lang="zh-cn">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width">
  <link rel="shortcut icon" href="#"/>
  <link rel="preload" href="{{_res_path}}/common/font/HYWH-65W.woff" as="font" type="font/woff">
  <link rel="preload" href="{{_res_path}}/common/font/NZBZ.woff" as="font" type="font/woff">
  <link rel="preload" href="{{_res_path}}/common/font/tttgbnumber.woff" as="font" type="font/woff">
  <link rel="stylesheet" type="text/css" href="{{_res_path}}/common/common.css"/>
  <link rel="stylesheet" type="text/css" href="{{_res_path}}/character/profile-detail.css"/>
  <link rel="stylesheet" type="text/css" href="{{_res_path}}/character/echo-list.css"/>
  {{if background}}{{@background}}{{/if}}
  <title>鸣潮声骸列表</title>
</head>
{{set elemCls = {火:'pyro',冰:'cryo',风:'anemo',雷:'electro',量子:'quantum',虚数:'geo',物理:'sr', }[element||elem] || element || elem || 'hydro' }}
<body class="elem-{{elemCls}} {{displayMode || mode || `default`}}-mode {{bodyClass}}" {{sys.scale}}>
<div class="container elem-bg" id="container">

<!-- 完全使用 miao-plugin 的 artis-list 结构，只修改数据源 -->
<div class="uid">UID {{uid}}</div>

<div class="profile-cont game-{{game}}">
  <div class="artis">
    {{each artis ds}}
    <div class="item arti">
      {{if ds && ds.name && ds.main && ds.main.key && ds.main.key!="undefined"}}
      <div class="avatar">
        <img src="{{_res_path}}{{ds.side}}" onerror="whenError(this)" />
      </div>
      <div class="arti-icon">
        <div class="img" style="background-image:url('{{_res_path}}{{ds.img}}')"></div>
      </div>
      <div class="head">
        <strong>{{ds.name}}</strong>
        <span class="mark mark-{{ds.markClass}}"><span>{{ds.mark}}分</span> - {{ds.markClass}}</span>
      </div>
      <ul class="detail attr">
        {{each ds.mainProps mainProp}}
        <li class="arti-main"><span class="title">{{artisKeyTitle[mainProp.key] || mainProp.key}}</span><span
            class="val">+{{mainProp.value}}</span>
        </li>
        {{/each}}
        {{each ds.attrs attr}}
        {{if attr && attr.key}}
        <li class="{{ds.charWeight[attr.key]*1 > 79.9 ?`great`:(ds.charWeight[attr.key]*1>0 ? `useful`:`nouse`)}}">
          <span class="title">
            {{if attr.isMax}}<i class="max-mark">MAX</i>{{/if}}
            {{if attr.eff}}<i class="eff">{{attr.eff || ''}}</i>{{/if}}
            {{if attr.upNum}}<i class="up-num up-{{attr.upNum}}"></i>{{/if}}
            {{artisKeyTitle[attr.key] || attr.key}}
          </span>
          <span class="val">+{{attr.value}}</span>
        </li>
        {{/if}}
        {{/each}}
      </ul>
      {{/if}}
    </div>
    {{/each}}
  </div>
</div>

</div>
</body>
</html>
