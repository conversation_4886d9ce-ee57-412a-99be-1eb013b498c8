/* miao-plugin 风格的通用样式 - 练度统计 */

.font-YS {
  font-family: Number, "汉仪文黑-65W", YS, PingFangSC-Medium, "PingFang SC", sans-serif;
}

.font-NZBZ {
  font-family: Number, "印品南征北战NZBZ体", NZBZ, "汉仪文黑-65W", YS, PingFangSC-Medium, "PingFang SC", sans-serif;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  user-select: none;
}

body {
  font-size: 18px;
  color: #1e1f20;
  transform: scale(1.4);
  transform-origin: 0 0;
  background: linear-gradient(135deg, #2a3860 0%, #1e2a4a 100%);
  width: 600px;
  min-height: 800px;
}

.container {
  width: 600px;
  padding: 20px 15px 10px 15px;
  background: url("{{_res_path}}/common/imgs/footer.png") left bottom no-repeat;
  background-size: contain;
}

.head-box {
  border-radius: 15px;
  padding: 10px 20px;
  position: relative;
  color: #fff;
  margin-top: 30px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.head-box .title {
  font-size: 36px;
  font-family: Number, "印品南征北战NZBZ体", NZBZ, "汉仪文黑-65W", YS, PingFangSC-Medium, "PingFang SC", sans-serif;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.head-box .genshin-logo {
  position: absolute;
  top: 1px;
  right: 15px;
  width: 97px;
  height: 60px;
  background: url("{{_res_path}}/common/cont/logo.png") no-repeat center;
  background-size: contain;
}

.head-box .label {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 5px;
}

.msg-cont {
  margin-left: 0;
  margin-right: 0;
}

.cont {
  border-radius: 15px;
  margin-top: 20px;
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
}

.cont-title {
  font-size: 18px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 2px solid #3498db;
}

.cont-body {
  color: #34495e;
}

.cont-msg {
  list-style: none;
  padding: 0;
}

.cont-msg li {
  margin-bottom: 8px;
  padding-left: 15px;
  position: relative;
  line-height: 1.5;
  font-size: 14px;
}

.cont-msg li::before {
  content: "•";
  color: #3498db;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.cont-msg strong {
  color: #2980b9;
  font-weight: bold;
}

.notice {
  color: #888;
  font-size: 12px;
  text-align: right;
  padding: 12px 5px 5px;
}

.notice-center {
  color: #fff;
  text-align: center;
  margin-bottom: 10px;
  text-shadow: 1px 1px 1px #333;
}

.copyright {
  font-size: 16px;
  text-align: center;
  color: #fff;
  position: relative;
  padding-left: 10px;
  text-shadow: 1px 1px 1px #000;
  margin-bottom: 10px;
}
