# 网页登录配置
webLogin:
  # 登录模式: "local" 或 "remote"
  mode: "local"
  
  # 本地登录服务器配置
  local:
    port: 19075
    host: "127.0.0.1"
    # 登录URL - 用于生成给用户的登录链接
    # 如果使用内网穿透，请填写穿透后的公网地址
    # 例如: "https://your-domain.com" 或 "http://your-tunnel-url.com"
    # 留空则使用 http://host:port 格式
    loginUrl: ""
  
  # 远程登录服务器配置
  remote:
    # 外部登录服务地址 (参考https://github.com/tyql688/ww-login进行搭建)
    # 使用此项目在公网上搭建登录服务后，可与其他人公用此登录服务
    url: ""
