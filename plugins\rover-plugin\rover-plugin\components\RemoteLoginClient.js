import fetch from "node-fetch"
import config from "./config.js"

/**
 * 远程登录客户端 - 兼容 ww-login 服务
 */
class RemoteLoginClient {
  constructor() {
    this.remoteUrl = config.getRemoteLoginUrl()
    this.pendingLogins = new Map() // 存储待处理的登录
    this.pollIntervals = new Map() // 存储轮询定时器
  }

  /**
   * 生成登录URL
   */
  async generateLoginUrl(userId, botId = "default") {
    try {
      if (!this.remoteUrl) {
        throw new Error("远程登录URL未配置")
      }

      // 调用远程服务生成token
      const response = await fetch(`${this.remoteUrl}/waves/token`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          user_id: userId,
          bot_id: botId,
        }),
      })

      if (!response.ok) {
        throw new Error(`远程服务响应错误: ${response.status}`)
      }

      const result = await response.json()
      const token = result.token

      if (!token) {
        throw new Error("远程服务未返回token")
      }

      // 存储登录信息（与本地登录服务器保持一致的5分钟过期）
      this.pendingLogins.set(token, {
        userId,
        botId,
        createTime: Date.now(),
        expireTime: Date.now() + 5 * 60 * 1000, // 5分钟过期，与本地登录服务器一致
      })

      // 开始轮询登录结果
      this.startPolling(token)

      // 返回登录URL
      return `${this.remoteUrl}/waves/i/${token}`
    } catch (error) {
      console.error("生成远程登录URL失败:", error)
      throw error
    }
  }

  /**
   * 开始轮询登录结果
   */
  startPolling(token) {
    const pollInterval = setInterval(async () => {
      try {
        const loginData = this.pendingLogins.get(token)
        if (!loginData) {
          clearInterval(pollInterval)
          this.pollIntervals.delete(token)
          return
        }

        // 检查是否过期
        if (Date.now() > loginData.expireTime) {
          console.log(`登录token ${token} 已过期`)
          this.cleanup(token)
          return
        }

        // 轮询登录结果
        const response = await fetch(`${this.remoteUrl}/waves/get`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            token: token,
          }),
        })

        if (!response.ok) {
          console.error(`轮询登录结果失败: ${response.status}`)
          return
        }

        const result = await response.json()

        // 检查是否有登录结果
        if (result.ck && result.did) {
          console.log(`✅ 远程登录成功: token=${token}`)

          // 处理登录成功
          await this.handleLoginSuccess(token, result)

          // 清理资源
          this.cleanup(token)
        }
      } catch (error) {
        console.error(`轮询登录结果失败:`, error)
      }
    }, 3000) // 每3秒轮询一次

    this.pollIntervals.set(token, pollInterval)

    // 5分钟后自动清理（与本地登录服务器一致）
    setTimeout(
      () => {
        this.cleanup(token)
      },
      5 * 60 * 1000,
    )
  }

  /**
   * 处理登录成功
   */
  async handleLoginSuccess(token, loginResult) {
    try {
      const loginData = this.pendingLogins.get(token)
      if (!loginData) {
        console.error(`登录数据不存在: ${token}`)
        return
      }

      const { userId, botId } = loginData
      const { ck: token_value, did, mobile } = loginResult

      console.log(`🔄 处理远程登录成功: 用户=${userId}, 手机=${mobile}`)

      // 先保存基础token信息，即使后续步骤失败也要保存
      const { WavesUser } = await import("./db/database.js")

      let uid = null
      let roleName = "未知角色"
      let defaultRole = null

      // 尝试验证token并获取角色信息（改进版：更好的风险检测处理）
      try {
        const KuroApi = (await import("./api/kuroapi.js")).default
        const kuroApi = new KuroApi()

        // 重新生成正确格式的DID（按照WutheringWavesUID的方式）
        const correctDid = await kuroApi.getDevCode()
        console.log(`🔧 重新生成DID: ${correctDid} (原DID: ${did})`)

        console.log(
          `🔍 验证token并获取角色列表: token=${token_value.substring(0, 10)}..., did=${correctDid}`,
        )

        const roleResponse = await kuroApi.getRoleList(token_value, correctDid)
        console.log(`📋 角色列表原始响应:`, JSON.stringify(roleResponse, null, 2))

        // 检查风险检测
        if (roleResponse && roleResponse.code === 270) {
          console.warn(`⚠️ 检测到库街区风险控制: ${roleResponse.msg}`)
          console.warn(`💡 建议: ${roleResponse.suggestion || "更换网络环境或稍后重试"}`)

          // 风险检测时仍保存token，但使用临时UID
          uid = `temp_${userId}_${Date.now()}`
          roleName = "风险检测-需手动绑定"

          // 每30秒重试，最多重试3次（降低触发风险检测的概率）
          this.startRetryRoleList(kuroApi, token_value, did, userId, 30000, 3)
        }
        // 检查正常响应格式
        else if (
          roleResponse &&
          roleResponse.data &&
          Array.isArray(roleResponse.data) &&
          roleResponse.data.length > 0
        ) {
          defaultRole = roleResponse.data[0]
          uid = defaultRole.roleId
          roleName = defaultRole.roleName || "未知角色"
          console.log(`✅ 成功获取角色信息: ${roleName} (${uid})`)
        } else {
          console.warn("⚠️ 获取角色列表失败，但仍会保存token信息")
          console.warn("⚠️ 响应格式异常或无角色数据")
          // 使用临时UID，后续用户可以手动绑定
          uid = `temp_${userId}_${Date.now()}`
          roleName = "获取失败-需手动绑定"
        }
      } catch (error) {
        console.error("❌ 验证token失败，但仍会保存token信息:", error)
        console.error("❌ 错误详情:", error.stack)
        // 使用临时UID，后续用户可以手动绑定
        uid = `temp_${userId}_${Date.now()}`
        roleName = "验证失败-需手动绑定"
      }

      // 无论角色信息获取是否成功，都保存token信息
      try {
        console.log(`💾 开始保存远程登录数据到数据库...`)

        const userResult = await WavesUser.createOrUpdate({
          bot_id: botId,
          user_id: userId,
          cookie: token_value,
          did: did,
          uid: uid,
          platform: "ios",
          push_switch: "off",
          sign_switch: "off",
          stamina_bg_value: "0",
          bbs_sign_switch: "off",
          bat: "", // 初始为空，后续异步获取
        })

        if (userResult) {
          console.log(`✅ 远程登录数据已保存到数据库`)
          console.log(`👤 用户: ${mobile} (${userId})`)
          console.log(`🎮 绑定角色: ${roleName} (${uid})`)
          console.log(`🔑 Token: ${token_value.substring(0, 20)}...`)
          console.log(`📱 DID: ${did}`)

          // 如果成功获取了角色信息，异步获取bat token（使用正确的DID）
          if (defaultRole) {
            try {
              this.getBatTokenAsync(defaultRole, token_value, correctDid, userId)
              console.log(`🔄 已启动bat token异步获取`)
            } catch (batError) {
              console.warn(`⚠️ 启动bat token获取失败，但不影响登录:`, batError.message)
            }
          }

          // 如果有真实UID，尝试自动刷新面板数据
          if (defaultRole && !uid.startsWith("temp_")) {
            try {
              this.autoRefreshPanel(userId, uid)
              console.log(`🔄 已启动自动刷新面板`)
            } catch (refreshError) {
              console.warn(`⚠️ 自动刷新面板失败，但不影响登录:`, refreshError.message)
            }
          } else {
            console.log(`💡 使用临时UID，请用户手动绑定正确的UID`)
          }
        } else {
          console.error(`❌ 保存远程登录信息到数据库失败`)
        }
      } catch (dbError) {
        console.error(`❌ 数据库保存失败:`, dbError)
        console.error(`❌ 数据库错误详情:`, dbError.stack)
      }
    } catch (error) {
      console.error(`❌ 处理远程登录成功时发生错误:`, error)
    }
  }

  /**
   * 启动角色列表重试机制
   */
  async startRetryRoleList(kuroApi, token, did, userId, interval = 1000, maxRetries = 5) {
    let retryCount = 0

    const retryFunction = async () => {
      retryCount++
      console.log(`🔄 第${retryCount}次重试获取角色列表...`)

      try {
        const retryResponse = await kuroApi.getRoleList(token, did)

        if (retryResponse && retryResponse.code === 270) {
          // 仍然是风险检测，继续重试
          if (retryCount < maxRetries) {
            console.warn(
              `⚠️ 第${retryCount}次重试仍遇到风险检测，${interval / 1000}秒后继续重试...`,
            )
            setTimeout(retryFunction, interval)
          } else {
            console.warn(`⚠️ 已达到最大重试次数(${maxRetries})，停止重试`)
            console.warn(`💡 建议：1. 更换代理IP 2. 等待更长时间后手动重试 3. 检查网络环境`)
          }
          return
        }

        if (
          retryResponse &&
          retryResponse.data &&
          Array.isArray(retryResponse.data) &&
          retryResponse.data.length > 0
        ) {
          const retryRole = retryResponse.data[0]
          console.log(`✅ 重试成功，获取到角色: ${retryRole.roleName} (${retryRole.roleId})`)

          // 更新数据库中的UID
          try {
            const { WavesUser } = await import("./db/database.js")
            await WavesUser.update(
              { uid: retryRole.roleId },
              {
                where: {
                  user_id: userId,
                },
              },
            )
            console.log(`✅ 数据库UID已更新: ${retryRole.roleId}`)

            // 获取bat token（使用正确的DID）
            const KuroApi = (await import("./api/kuroapi.js")).default
            const kuroApi = new KuroApi()
            const correctDid = await kuroApi.getDevCode()
            this.getBatTokenAsync(retryRole, token, correctDid, userId)
          } catch (updateError) {
            console.error(`❌ 更新数据库UID失败:`, updateError)
          }
        } else {
          console.warn(`⚠️ 第${retryCount}次重试获取角色列表失败，响应格式异常`)
          if (retryCount < maxRetries) {
            setTimeout(retryFunction, interval)
          }
        }
      } catch (retryError) {
        console.warn(`⚠️ 第${retryCount}次重试获取角色信息失败:`, retryError.message)
        if (retryCount < maxRetries) {
          setTimeout(retryFunction, interval)
        }
      }
    }

    // 开始第一次重试
    setTimeout(retryFunction, interval)
  }

  /**
   * 异步获取bat token
   */
  async getBatTokenAsync(role, token, did, userId) {
    try {
      console.log(`🔑 异步获取bat token: 角色=${role.roleName} (${role.roleId})`)
      const KuroApi = (await import("./api/kuroapi.js")).default
      const kuroApi = new KuroApi()
      const bat = await kuroApi.getRequestToken(role.roleId, token, did)

      if (bat) {
        console.log(`🔑 bat token获取成功: ${bat.substring(0, 10)}...`)

        // 更新数据库中的bat token
        const { WavesUser } = await import("./db/database.js")
        await WavesUser.update(
          { bat: bat },
          {
            where: {
              user_id: userId,
              uid: role.roleId,
            },
          },
        )
        console.log(`🔑 bat token已更新到数据库`)
      } else {
        console.warn(`⚠️ bat token获取失败`)
      }
    } catch (error) {
      console.error(`❌ 获取bat token失败:`, error)
    }
  }

  /**
   * 自动刷新面板数据（检查刷新间隔）
   */
  async autoRefreshPanel(userId, roleId) {
    try {
      console.log(`🔄 自动刷新面板数据: 用户=${userId}, 角色=${roleId}`)

      // 检查刷新间隔
      const refreshInterval = config.getRefreshInterval()

      if (refreshInterval > 0) {
        // 有刷新间隔限制，检查是否在冷却期
        const lastRefreshKey = `lastRefresh_${userId}`
        const lastRefreshTime = global[lastRefreshKey] || 0
        const now = Date.now()
        const timeDiff = (now - lastRefreshTime) / 1000

        if (timeDiff < refreshInterval) {
          const remainingTime = Math.ceil(refreshInterval - timeDiff)
          console.log(`⏰ 用户 ${userId} 刷新面板冷却中，剩余 ${remainingTime}s`)
          return
        }
      }

      // 动态导入 Refresh 类
      const { Refresh } = await import("../apps/refresh.js")
      const refreshInstance = new Refresh()

      // 创建模拟事件对象
      const mockEvent = {
        user_id: userId,
        bot: { uin: "default" },
        self_id: "default",
        msg: "刷新面板",
        reply: msg => {
          console.log(`📢 自动刷新消息: ${msg}`)
        },
      }

      // 执行刷新
      await refreshInstance.refreshAllPanel(mockEvent)

      // 更新最后刷新时间
      global[`lastRefresh_${userId}`] = Date.now()
      console.log(`✅ 自动刷新面板完成`)
    } catch (error) {
      console.error(`❌ 自动刷新面板失败:`, error)
    }
  }

  /**
   * 清理资源
   */
  cleanup(token) {
    // 清理轮询定时器
    const pollInterval = this.pollIntervals.get(token)
    if (pollInterval) {
      clearInterval(pollInterval)
      this.pollIntervals.delete(token)
    }

    // 清理登录数据
    this.pendingLogins.delete(token)

    console.log(`🧹 已清理登录token: ${token}`)
  }

  /**
   * 清理过期的登录
   */
  cleanupExpired() {
    const now = Date.now()
    for (const [token, loginData] of this.pendingLogins.entries()) {
      if (now > loginData.expireTime) {
        this.cleanup(token)
      }
    }
  }
}

export default RemoteLoginClient
