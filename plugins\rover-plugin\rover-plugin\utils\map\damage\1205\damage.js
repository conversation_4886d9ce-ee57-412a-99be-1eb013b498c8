import { WavesDamageCalculator } from "../../../wavesDamageCalculator.js"
import { weaponDamage, echoDamage, phaseDamage, checkCharId, DAMAGE_TYPE } from "../damage.js"
import characterData from "./data.json" assert { type: "json" }

/**
 * 长离伤害计算器
 * 角色ID: 1205
 * 基于WutheringWavesUID的damage_1205.py实现
 */
export class ChangliDamageDetail {
  constructor() {
    this.calculator = new WavesDamageCalculator()
    this.characterId = "1205"
    this.characterName = "长离"
    this.characterData = characterData
  }

  /**
   * 计算伤害详情
   * @param {Object} attr - 伤害属性对象
   * @param {Array|string} damageFunc - 伤害函数类型
   * @param {boolean} isGroup - 是否组队
   * @returns {Object} 伤害计算结果
   */
  async damageDetail(attr, damageFunc, isGroup = false) {
    // 设置角色属性
    attr.char_attr = "pyro" // 热熔属性
    
    // 应用武器效果
    weaponDamage(attr, attr.weaponData, damageFunc, isGroup)
    
    // 应用声骸效果
    echoDamage(attr, attr.echoData, damageFunc, isGroup)
    
    // 应用套装效果
    phaseDamage(attr, attr.echoData, damageFunc, isGroup)
    
    // 应用长离专用buff
    this.doBuff(attr, damageFunc, isGroup)
    
    // 计算技能倍率
    const skillResults = await this.calculateSkillMultipliers(attr, damageFunc)
    
    return {
      characterId: this.characterId,
      characterName: this.characterName,
      damageFunc,
      isGroup,
      skillResults,
      totalDamage: this.calculateTotalDamage(skillResults),
      effects: [...attr.effect]
    }
  }

  /**
   * 应用长离专用buff
   * @param {Object} attr - 伤害属性对象
   * @param {Array|string} damageFunc - 伤害函数类型
   * @param {boolean} isGroup - 是否组队
   */
  doBuff(attr, damageFunc, isGroup) {
    const chain = attr.chainLevel || 0
    const roleName = this.characterName

    // 固有技能：潜谋 - 每拥有1层【离火】，热熔伤害加成提升5%
    if (damageFunc.includes("skill") || damageFunc.includes("hit")) {
      const lihuoStacks = 4 // 假设满层离火
      this.calculator.addDmgBonus(attr, lihuoStacks * 0.05, "潜谋", `每层【离火】热熔伤害加成+5%，当前${lihuoStacks}层`)
    }

    // 固有技能：散势 - 热熔伤害加成提升20%，忽视目标15%防御
    if (damageFunc.includes("hit") || damageFunc.includes("liberation")) {
      this.calculator.addDmgBonus(attr, 0.2, "散势", "热熔伤害加成+20%")
      this.calculator.addDefenseReduction(attr, 0.15, "散势", "忽视目标15%防御")
    }

    // 焰羽状态：施放焚身以火时攻击提升25%
    if (damageFunc.includes("hit") && damageFunc.includes("a2")) {
      this.calculator.addAtkPercent(attr, 0.25, "焰羽状态", "施放焚身以火时攻击+25%")
    }

    // 共鸣链效果
    if (chain >= 1 && (damageFunc.includes("skill") || damageFunc.includes("hit"))) {
      this.calculator.addDmgBonus(attr, 0.1, `${roleName}-一链`, "施放共鸣技能或重击焚身以火伤害+10%")
    }

    if (chain >= 2) {
      this.calculator.addCritRate(attr, 0.25, `${roleName}-二链`, "获得【离火】时暴击+25%")
    }

    if (chain >= 3 && damageFunc.includes("liberation")) {
      this.calculator.addDmgBonus(attr, 0.8, `${roleName}-三链`, "共鸣解放伤害+80%")
    }

    if (chain >= 4 && isGroup) {
      this.calculator.addAtkPercent(attr, 0.2, `${roleName}-四链`, "队伍攻击+20%")
    }

    if (chain >= 5 && damageFunc.includes("hit") && damageFunc.includes("a2")) {
      this.calculator.addSkillRatio(attr, 0.5, `${roleName}-五链`, "重击焚身以火倍率+50%")
      this.calculator.addDmgBonus(attr, 0.5, `${roleName}-五链`, "重击焚身以火伤害+50%")
    }

    if (chain >= 6) {
      this.calculator.addDefenseReduction(attr, 0.4, `${roleName}-六链`, "忽视目标40%防御")
    }
  }

  /**
   * 计算技能倍率
   * @param {Object} attr - 伤害属性对象
   * @param {Array|string} damageFunc - 伤害函数类型
   * @returns {Object} 技能倍率结果
   */
  async calculateSkillMultipliers(attr, damageFunc) {
    const skillLevels = attr.skillLevels || {}
    const results = {}

    // 根据damageFunc计算对应技能的倍率
    if (damageFunc.includes("attack")) {
      results.attack = this.calculateAttackMultiplier(skillLevels.a || 1)
    }

    if (damageFunc.includes("skill")) {
      results.skill = this.calculateSkillMultiplier(skillLevels.e || 1)
    }

    if (damageFunc.includes("liberation")) {
      results.liberation = this.calculateLiberationMultiplier(skillLevels.r || 1)
    }

    if (damageFunc.includes("outro")) {
      results.outro = this.calculateOutroMultiplier(skillLevels.q || 1)
    }

    if (damageFunc.includes("hit") && damageFunc.includes("a2")) {
      results.hit = this.calculateHitMultiplier(skillLevels.a2 || 1)
    }

    return results
  }

  /**
   * 计算普攻倍率
   * @param {number} level - 技能等级
   * @returns {Object} 普攻倍率结果
   */
  calculateAttackMultiplier(level) {
    const talentData = this.characterData.talentData.a
    const index = level - 1

    return {
      name: "常态攻击: 衔火洞明",
      level,
      multipliers: {
        "普攻第一段": talentData["普攻第一段伤害"][index],
        "普攻第二段": talentData["普攻第二段伤害"][index],
        "普攻第三段": talentData["普攻第三段伤害"][index],
        "普攻第四段": talentData["普攻第四段伤害"][index],
        "重击": talentData["重击"][index],
        "空中重击": talentData["空中重击"][index],
        "闪避反击": talentData["闪避反击"][index]
      }
    }
  }

  /**
   * 计算共鸣技能倍率
   * @param {number} level - 技能等级
   * @returns {Object} 共鸣技能倍率结果
   */
  calculateSkillMultiplier(level) {
    const talentData = this.characterData.talentData.e
    const index = level - 1

    return {
      name: "共鸣技能: 赫羽三相",
      level,
      multipliers: {
        "心眼·劫": talentData["心眼·劫伤害"][index],
        "心眼·征": talentData["心眼·征伤害"][index],
        "心眼·冲": talentData["心眼·冲伤害"][index]
      }
    }
  }

  /**
   * 计算共鸣解放倍率
   * @param {number} level - 技能等级
   * @returns {Object} 共鸣解放倍率结果
   */
  calculateLiberationMultiplier(level) {
    const talentData = this.characterData.talentData.r
    const index = level - 1

    return {
      name: "共鸣解放: 离火照丹心",
      level,
      multipliers: {
        "技能伤害": talentData["技能伤害"][index]
      }
    }
  }

  /**
   * 计算变奏技能倍率
   * @param {number} level - 技能等级
   * @returns {Object} 变奏技能倍率结果
   */
  calculateOutroMultiplier(level) {
    const talentData = this.characterData.talentData.q
    const index = level - 1

    return {
      name: "变奏技能: 天道持枢",
      level,
      multipliers: {
        "技能伤害": talentData["技能伤害"][index]
      }
    }
  }

  /**
   * 计算共鸣回路倍率
   * @param {number} level - 技能等级
   * @returns {Object} 共鸣回路倍率结果
   */
  calculateHitMultiplier(level) {
    const talentData = this.characterData.talentData.a2
    const index = level - 1

    return {
      name: "共鸣回路: 焚身以火",
      level,
      multipliers: {
        "焚身以火": talentData["焚身以火伤害"][index]
      }
    }
  }

  /**
   * 计算总伤害
   * @param {Object} skillResults - 技能倍率结果
   * @returns {Object} 总伤害结果
   */
  calculateTotalDamage(skillResults) {
    // 这里可以根据需要计算总伤害
    // 例如：连招总伤害、单技能最高伤害等
    return {
      maxSingleHit: 0, // 最高单次伤害
      totalCombo: 0,   // 连招总伤害
      dps: 0          // 每秒伤害
    }
  }

  /**
   * 获取角色数据
   * @returns {Object} 角色数据
   */
  getCharacterData() {
    return this.characterData
  }
}

// 导出伤害计算器实例
export const damageDetail = ChangliDamageDetail

export default ChangliDamageDetail
