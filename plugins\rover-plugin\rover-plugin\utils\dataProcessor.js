import { imageMapper } from "./imageManager.js"
import { resourceDownloader } from "./resourceDownloader.js"

/**
 * 角色数据处理器
 * 处理rawdata.json中的角色数据，使用统一的资源下载器确保图片可用
 */
export class DataProcessor {
  constructor() {}

  /**
   * 处理角色数据
   * @param {Object} rawData - 原始角色数据
   * @param {string} characterId - 角色ID (如1205)
   * @returns {Promise<Object>} - 处理后的角色数据
   */
  async processCharacterData(rawData, characterId) {
    console.log(`🔄 开始处理角色数据: ${characterId}`)

    // 每次都处理新的rawData，但检查本地图片是否已存在
    const processedData = JSON.parse(JSON.stringify(rawData)) // 深拷贝

    // 不在这里下载资源，资源应该在刷新面板时已经下载
    // 这里只处理数据，将URL转换为本地路径

    // 处理声骸属性图标 - 更新为本地路径
    if (processedData.equipPhantomAddPropList) {
      for (const prop of processedData.equipPhantomAddPropList) {
        if (prop.iconUrl) {
          const localPath = imageMapper.checkLocalImageExists(prop.iconUrl)
          if (localPath) {
            prop.iconUrl = localPath
          }
        }
      }
    }

    // 处理声骸属性列表图标 - 更新为本地路径
    if (processedData.equipPhantomAttributeList) {
      for (const attr of processedData.equipPhantomAttributeList) {
        if (attr.iconUrl) {
          const localPath = imageMapper.checkLocalImageExists(attr.iconUrl)
          if (localPath) {
            attr.iconUrl = localPath
          }
        }
      }
    }

    // 处理声骸数据 - 检查本地文件是否已有
    if (processedData.phantomData?.equipPhantomList) {
      for (const phantom of processedData.phantomData.equipPhantomList) {
        // 跳过无效的声骸数据
        if (!phantom || !phantom.phantomProp) {
          continue
        }

        // 声骸图标、套装图标、主副属性图标已通过统一资源下载器处理
        // 这里不需要重复下载，资源下载器已经处理了所有相关资源
      }
    }

    // 技能图标、共鸣链图标、武器图标、武器属性图标已通过统一资源下载器处理
    // 这里不需要重复下载，资源下载器已经处理了所有相关资源

    console.log(`✅ 角色数据处理完成: ${characterId}`)
    return processedData
  }
}

export const dataProcessor = new DataProcessor()
