import plugin from "../../../lib/plugins/plugin.js"
import User from "../components/User.js"
import config from "../components/config.js"
import wutheringWavesAPI from "../components/WutheringWavesAPI.js"
import { WavesUser, WavesBind } from "../components/db/database.js"
import { ERROR_MESSAGES, SUCCESS_MESSAGES, REGEX_PATTERNS } from "../utils/constants.js"

export class Bind extends plugin {
  constructor() {
    super({
      name: "鸣潮-绑定管理",
      event: "message",
      priority: 1008,
      rule: [
        {
          reg: config.generateCommandRegex("绑定uid\\s*(.+)?"),
          fnc: "bindUid",
        },
        {
          reg: config.generateCommandRegex("我的绑定"),
          fnc: "myBind",
        },
        {
          reg: config.generateCommandRegex("查看"),
          fnc: "viewUids",
        },
        {
          reg: config.generateCommandRegex("切换\\s*(.+)"),
          fnc: "switchUid",
        },
        {
          reg: config.generateCommandRegex("删除\\s*(.+)"),
          fnc: "deleteUid",
        },
        {
          reg: config.generateCommandRegex("删除全部uid"),
          fnc: "deleteAllUids",
        },
        {
          reg: config.generateCommandRegex("删除token\\s*(.+)?"),
          fnc: "deleteToken",
        },
        {
          reg: config.generateCommandRegex("添加token\\s*(.+)"),
          fnc: "addToken",
        },
        {
          reg: config.generateCommandRegex("获取token"),
          fnc: "getToken",
        },
      ],
    })
  }

  /**
   * 绑定UID
   */
  async bindUid(e) {
    const uid = e.msg.match(/\S+$/)?.[0] || ""

    if (!uid) {
      await e.reply(
        `❌ 请提供要绑定的UID\n使用方法：${config.getCommandExample("绑定uid 你的UID")}`,
      )
      return false
    }

    // 验证UID格式（鸣潮UID通常是9位数字）
    if (!REGEX_PATTERNS.UID.test(uid)) {
      await e.reply("❌ UID格式错误，鸣潮UID应为9位数字")
      return false
    }

    const user = User.fromEvent(e)
    if (!user) {
      await e.reply(ERROR_MESSAGES.USER_NOT_FOUND)
      return false
    }

    const success = await user.bindUid(uid)
    if (success) {
      await e.reply(
        `✅ UID绑定成功！\n已绑定UID：${uid}\n\n💡 提示：还需要添加token才能查询面板\n使用：${config.getCommandExample("添加token token,did")}`,
      )
    } else {
      await e.reply("❌ UID绑定失败，请稍后重试")
    }

    return true
  }

  /**
   * 查看我的绑定信息
   */
  async myBind(e) {
    const user = User.fromEvent(e)
    if (!user) {
      await e.reply("❌ 获取用户信息失败")
      return false
    }

    const userInfo = await user.getUserInfo()

    let message = "📋 我的绑定信息\n"
    message += `用户ID：${userInfo.userId}\n`

    // 显示登录状态
    if (userInfo.isLoggedIn) {
      message += `✅ 已登录库街区\n`
      message += `用户名：${userInfo.userName}\n`
      message += `角色数量：${userInfo.roleCount}\n`
    } else {
      message += `❌ 未登录库街区\n`
    }

    if (userInfo.uid) {
      message += `UID：${userInfo.uid}\n`
      if (userInfo.serverId) {
        message += `服务器：${userInfo.serverId}\n`
      }
      message += `绑定时间：${userInfo.bindTime ? new Date(userInfo.bindTime).toLocaleString() : "未知"}\n`
    } else {
      message += "UID：未绑定\n"
    }

    if (userInfo.hasToken) {
      message += "token：已绑定\n"
      message += `token绑定时间：${userInfo.tokenBindTime ? new Date(userInfo.tokenBindTime).toLocaleString() : "未知"}\n`
    } else {
      message += "token：未绑定\n"
    }

    // 提供使用建议
    if (!userInfo.isLoggedIn) {
      message += "\n💡 推荐使用方式：\n"
      message += `• ${config.getCommandExample("登录")} 网页登录\n`
      message += "\n或手动绑定：\n"
      message += `• ${config.getCommandExample("绑定uid 你的UID")}\n`
      message += `• ${config.getCommandExample("添加token token,did")}\n`
    } else if (!userInfo.uid) {
      message += `\n💡 请使用 ${config.getCommandExample("我的角色")} 查看角色列表`
    }

    await e.reply(message)
    return true
  }

  /**
   * 添加token（类似WutheringWavesUID的格式）
   */
  async addToken(e) {
    const text = e.msg.split(/\s+/).slice(1).join(" ").trim()

    if (!text) {
      await e.reply(
        `❌ 请提供token和did\n使用方法：${config.getCommandExample("添加token token,did")}\n\n💡 获取方法：\n1. 打开库洛游戏通行证APP\n2. 登录后进入鸣潮游戏\n3. 在开发者工具中查找请求头的token和did字段`,
      )
      return false
    }

    // 解析token和did
    let token = ""
    let did = ""

    // 支持多种分隔符
    for (const separator of [",", "，", " "]) {
      const parts = text.split(separator)
      if (parts.length === 2) {
        token = parts[0].trim()
        did = parts[1].trim()
        break
      }
    }

    if (!token || !did) {
      await e.reply(
        `❌ 格式错误\n使用方法：${config.getCommandExample("添加token token,did")}\n请用逗号分隔token和did`,
      )
      return false
    }

    // 验证did格式（32位、36位或40位）
    if (![32, 36, 40].includes(did.length)) {
      await e.reply("❌ did格式错误\ndid应为32位、36位或40位字符")
      return false
    }

    // 验证token格式
    if (token.length < 20) {
      await e.reply("❌ token格式可能不正确，请检查后重试")
      return false
    }

    const user = User.fromEvent(e)
    if (!user) {
      await e.reply("❌ 获取用户信息失败")
      return false
    }

    // 先尝试使用token获取用户基础信息来验证token有效性并获取UID
    await e.reply("🔄 正在验证token并获取用户信息...")

    try {
      // 使用新的WutheringWavesAPI获取角色列表
      const roleListResult = await wutheringWavesAPI.getKuroRoleList(token, did)

      // 检查是否遇到风险检测
      if (
        !roleListResult.success &&
        roleListResult.message &&
        roleListResult.message.includes("风险")
      ) {
        console.warn(`⚠️ 添加token时检测到风险控制: ${roleListResult.message}`)

        // 风险检测时仍保存token，但使用临时UID，并启动重试机制
        const userId = e.user_id.toString()
        const botId = e.bot?.uin || e.self_id || "default"
        const tempUid = `temp_${userId}_${Date.now()}`

        // 保存基础信息
        const userResult = await WavesUser.createOrUpdate({
          bot_id: botId,
          user_id: userId,
          cookie: token,
          did: did,
          uid: tempUid,
          platform: "ios",
          push_switch: "off",
          sign_switch: "off",
          stamina_bg_value: "0",
          bbs_sign_switch: "off",
          bat: "",
        })

        if (userResult) {
          await e.reply(
            `⚠️ 检测到风险控制，token已保存但角色信息获取失败\n\n💡 系统将每秒自动重试获取角色信息(最多5次)\n如果重试成功，将自动更新角色绑定\n\n您也可以稍后手动使用 ${config.getCommandExample("刷新面板")} 来验证token`,
          )

          // 启动重试机制（降低重试频率以避免风险检测）
          this.startRetryRoleListForAddToken(token, did, userId, botId, e, 30000, 3)
        } else {
          await e.reply("❌ token保存失败，请稍后重试")
        }

        return true
      }

      if (!roleListResult.success) {
        await e.reply(
          `❌ token验证失败: ${roleListResult.message}\n\n💡 可能原因：\n• token已过期\n• token格式不正确\n• 账号下没有鸣潮角色\n• 网络连接问题`,
        )
        return false
      }

      if (!roleListResult.data || roleListResult.data.length === 0) {
        await e.reply("❌ 账号下没有鸣潮角色，请先创建角色后再绑定")
        return false
      }

      // 获取第一个角色的UID（通常用户只有一个鸣潮账号）
      const firstRole = roleListResult.data[0]
      const uid = firstRole.roleId.toString()
      const serverName = firstRole.serverName || "鸣潮"
      const roleName = firstRole.roleName || "未知角色"
      const gameLevel = firstRole.level || "未知等级"
      const roleCount = roleListResult.data.length

      console.log(`✅ 通过API获取到UID: ${uid}, 角色: ${roleName}, 服务器: ${serverName}`)

      // 获取bat token
      await e.reply("🔑 正在获取访问令牌...")
      const batResult = await wutheringWavesAPI.getRequestToken(uid, token, did, firstRole.serverId)

      let batToken = ""
      if (!batResult.success) {
        console.warn(`⚠️ bat token获取失败: ${batResult.message}`)
        // 不阻止绑定流程，bat token可以后续获取
      } else {
        batToken = batResult.token || ""
        console.log(`✅ bat token获取成功: ${uid}`)
      }

      // 使用新的数据库模型保存数据
      const userId = e.user_id.toString()
      const botId = e.bot?.uin || e.self_id || "default"

      try {
        console.log(`🔄 保存token到数据库 - 用户: ${userId}, UID: ${uid}`)

        // 使用新的多账号支持添加账号
        const user = User.fromEvent(e)
        const accountData = {
          uid: uid,
          token: token,
          did: did,
          bat: batToken,
        }

        const addResult = await user.addNewAccount(accountData)

        if (!addResult.success) {
          throw new Error(addResult.error || "添加账号失败")
        }

        const bindSuccess = true // addNewAccount 内部已处理绑定

        if (addResult.success && bindSuccess) {
          console.log(`✅ 账号添加成功 - UID: ${uid}, 用户: ${userId}, Bot: ${botId}`)
          console.log(`🔍 调试信息 - ${addResult.isNew ? "新增账号" : "更新账号"}`)

          // 显示成功消息
          const batMessage = batResult.success
            ? "\n\n🔑 访问令牌已获取并保存"
            : "\n\n⚠️ 访问令牌将在首次使用时自动获取"

          await e.reply(
            `✅ token添加成功！\n\n📋 角色信息：\n• UID: ${uid}\n• 角色名: ${roleName}\n• 等级: ${gameLevel}\n• 服务器: ${serverName}\n• 角色数量: ${roleCount}${batMessage}\n\n🔄 正在自动刷新面板数据...`,
          )

          // 检查刷新间隔后自动刷新面板数据
          try {
            const refreshInterval = config.getRefreshInterval()
            if (refreshInterval > 0) {
              // 有刷新间隔限制，检查是否在冷却期
              const lastRefreshKey = `lastRefresh_${e.user_id}`
              const lastRefreshTime = global[lastRefreshKey] || 0
              const now = Date.now()
              const timeDiff = (now - lastRefreshTime) / 1000

              if (timeDiff < refreshInterval) {
                const remainingTime = Math.ceil(refreshInterval - timeDiff)
                await e.reply(`\n\n⏰ 刷新面板冷却中，请等待 ${remainingTime}s 后手动刷新面板`)
                return
              }
            }

            const { Refresh } = await import("./refresh.js")
            const refreshInstance = new Refresh()
            // 创建一个模拟的事件对象来触发刷新
            const mockEvent = {
              ...e,
              msg: config.getCommandExample("刷新面板").replace(/^[^刷新]*/, "") + "刷新面板",
            }
            await refreshInstance.refreshAllPanel(mockEvent)

            // 更新最后刷新时间
            global[`lastRefresh_${e.user_id}`] = Date.now()
          } catch (refreshError) {
            console.error("自动刷新面板失败:", refreshError)
            await e.reply(
              `\n\n💡 请手动使用 ${config.getCommandExample("刷新面板")} 来获取角色数据`,
            )
          }
        } else {
          await e.reply("❌ token保存失败，请稍后重试")
        }
      } catch (dbError) {
        console.error("数据库保存失败:", dbError)
        await e.reply(`❌ token保存失败: ${dbError.message}`)
      }
    } catch (error) {
      console.error("验证token失败:", error)

      // 如果API请求失败，仍然保存token，但提示用户
      try {
        const userId = e.user_id.toString()
        const botId = e.bot?.uin || e.self_id || "default"
        const tempUid = `temp_${userId}_${Date.now()}` // 临时UID

        const userResult = await WavesUser.createOrUpdate({
          bot_id: botId,
          user_id: userId,
          cookie: token,
          did: did,
          uid: tempUid,
          platform: "ios",
          push_switch: "off",
          sign_switch: "off",
          stamina_bg_value: "0",
          bbs_sign_switch: "off",
          bat: "",
        })

        if (userResult) {
          // 即使验证失败，也尝试获取bat token（使用临时UID）
          try {
            await e.reply("🔄 尝试获取访问令牌...")
            const KuroApiFixed = (await import("../components/KuroApiFixed.js")).default
            const kuroApiFixed = new KuroApiFixed()

            // 注意：这里使用临时UID，可能会失败，但值得尝试
            const batResult = await kuroApiFixed.getRequestToken(tempUid, token, did, userId, botId)

            if (batResult.success && batResult.token) {
              await userResult.update({ bat: batResult.token })
              console.log(`✅ bat token获取成功并保存 - 临时UID: ${tempUid}`)
            }
          } catch (batError) {
            console.warn("获取bat token失败（预期行为）:", batError.message)
          }

          await e.reply(
            `⚠️ token已保存，但无法立即验证\n\n可能原因：\n• 网络连接问题\n• API服务暂时不可用\n\n请稍后使用 ${config.getCommandExample("刷新面板")} 来验证token是否有效`,
          )
        } else {
          await e.reply("❌ token保存失败，请稍后重试")
        }
      } catch (saveError) {
        console.error("保存token失败:", saveError)
        await e.reply(`❌ token保存失败: ${saveError.message}`)
      }
    }

    return true
  }

  /**
   * 获取用户绑定的token和did
   */
  async getToken(e) {
    try {
      const user = User.fromEvent(e)
      if (!user) {
        await e.reply("❌ 获取用户信息失败")
        return false
      }

      // 使用优化后的批量获取方法
      const userData = await user.getAllUserData()
      const { token, did } = userData

      if (!token) {
        await e.reply(
          `❌ 您还未绑定token\n\n🔑 请先进行登录：\n• 使用：${config.getCommandExample("登录")} 进行网页登录\n• 或使用：${config.getCommandExample("添加token token,did")} 手动绑定`,
        )
        return false
      }

      if (!did) {
        await e.reply(`❌ 缺少设备ID\n请重新进行登录：${config.getCommandExample("登录")}`)
        return false
      }

      // 按照要求的格式返回：token,did
      const tokenInfo = `${token},${did}`

      let message = "🔑 您的Token信息\n\n"
      message += `📋 格式：token,did\n`
      message += `📄 内容：${tokenInfo}\n\n`
      message += `💡 使用说明：\n`
      message += `• 此信息可用于其他工具或备份\n`
      message += `• 请妥善保管，不要泄露给他人\n`
      message += `• Token具有账号访问权限，请谨慎使用`

      await e.reply(message)
    } catch (error) {
      console.error("获取token失败:", error)
      await e.reply("❌ 获取token失败，请稍后重试")
    }

    return true
  }

  /**
   * 为添加token功能启动角色列表重试机制
   */
  async startRetryRoleListForAddToken(
    token,
    did,
    userId,
    botId,
    e,
    interval = 1000,
    maxRetries = 5,
  ) {
    let retryCount = 0

    const retryFunction = async () => {
      retryCount++
      console.log(`🔄 [AddToken] 第${retryCount}次重试获取角色列表...`)

      try {
        const roleListResult = await wutheringWavesAPI.getKuroRoleList(token, did)

        if (
          !roleListResult.success &&
          roleListResult.message &&
          roleListResult.message.includes("风险")
        ) {
          // 仍然是风险检测，继续重试
          if (retryCount < maxRetries) {
            console.warn(
              `⚠️ [AddToken] 第${retryCount}次重试仍遇到风险检测，${interval / 1000}秒后继续重试...`,
            )
            setTimeout(retryFunction, interval)
          } else {
            console.warn(`⚠️ [AddToken] 已达到最大重试次数(${maxRetries})，停止重试`)
            // 发送最终失败通知
            try {
              await e.reply(
                `⚠️ 重试${maxRetries}次后仍遇到风险检测，请稍后手动使用 ${config.getCommandExample("刷新面板")} 来验证token`,
              )
            } catch (replyError) {
              console.error("发送重试失败通知时出错:", replyError)
            }
          }
          return
        }

        if (roleListResult.success && roleListResult.data && roleListResult.data.length > 0) {
          // 重试成功，获取到角色信息
          const firstRole = roleListResult.data[0]
          const uid = firstRole.roleId.toString()
          const roleName = firstRole.roleName || "未知角色"
          const serverName = firstRole.serverName || "鸣潮"

          console.log(`✅ [AddToken] 重试成功，获取到角色: ${roleName} (${uid})`)

          try {
            // 更新数据库中的UID
            await WavesUser.update(
              { uid: uid },
              {
                where: {
                  user_id: userId,
                  bot_id: botId,
                },
              },
            )

            // 保存到绑定表
            const bindResult = await WavesBind.insertWavesUid(
              userId,
              botId,
              uid,
              e.group_id?.toString(),
            )

            console.log(`✅ [AddToken] 数据库UID已更新: ${uid}`)

            // 获取bat token
            try {
              const batResult = await wutheringWavesAPI.getRequestToken(
                uid,
                token,
                did,
                firstRole.serverId,
              )
              if (batResult.success && batResult.token) {
                await WavesUser.update(
                  { bat: batResult.token },
                  {
                    where: {
                      user_id: userId,
                      bot_id: botId,
                    },
                  },
                )
                console.log(`✅ [AddToken] bat token已更新`)
              }
            } catch (batError) {
              console.warn(`⚠️ [AddToken] bat token获取失败:`, batError.message)
            }

            // 发送成功通知
            try {
              await e.reply(
                `✅ 重试成功！角色信息已更新\n\n📋 角色信息：\n• UID: ${uid}\n• 角色名: ${roleName}\n• 服务器: ${serverName}\n\n🔄 现在可以使用 ${config.getCommandExample("刷新面板")} 来获取角色数据`,
              )
            } catch (replyError) {
              console.error("发送重试成功通知时出错:", replyError)
            }
          } catch (updateError) {
            console.error(`❌ [AddToken] 更新数据库失败:`, updateError)
          }
        } else {
          console.warn(`⚠️ [AddToken] 第${retryCount}次重试获取角色列表失败，响应格式异常`)
          if (retryCount < maxRetries) {
            setTimeout(retryFunction, interval)
          } else {
            try {
              await e.reply(
                `⚠️ 重试${maxRetries}次后仍无法获取角色信息，请稍后手动使用 ${config.getCommandExample("刷新面板")} 来验证token`,
              )
            } catch (replyError) {
              console.error("发送重试失败通知时出错:", replyError)
            }
          }
        }
      } catch (retryError) {
        console.warn(`⚠️ [AddToken] 第${retryCount}次重试获取角色信息失败:`, retryError.message)
        if (retryCount < maxRetries) {
          setTimeout(retryFunction, interval)
        } else {
          try {
            await e.reply(
              `⚠️ 重试${maxRetries}次后仍遇到错误，请稍后手动使用 ${config.getCommandExample("刷新面板")} 来验证token`,
            )
          } catch (replyError) {
            console.error("发送重试失败通知时出错:", replyError)
          }
        }
      }
    }

    // 开始第一次重试
    setTimeout(retryFunction, interval)
  }

  /**
   * 查看所有绑定的UID
   */
  async viewUids(e) {
    const user = User.fromEvent(e)
    if (!user) {
      await e.reply("❌ 获取用户信息失败")
      return false
    }

    try {
      const allUids = await user.getAllUids()
      const currentUid = await user.getUid()

      if (allUids.length === 0) {
        await e.reply(
          `❌ 您还未绑定任何UID\n请先使用：${config.getCommandExample("绑定uid 你的UID")}\n或使用：${config.getCommandExample("添加token token,did")}`,
        )
        return false
      }

      let message = `📋 您绑定的所有UID：\n\n`
      allUids.forEach((uid, index) => {
        const isCurrent = uid === currentUid
        message += `${index + 1}. ${uid}`
        if (isCurrent) {
          message += ` ✅当前`
        }
        message += `\n`
      })

      message += `\n💡 使用 ${config.getCommandExample("切换 UID")} 来切换当前UID`

      await e.reply(message)
      return true
    } catch (error) {
      console.error("查看UID失败:", error)
      await e.reply("❌ 查看UID失败")
      return false
    }
  }

  /**
   * 切换UID
   */
  async switchUid(e) {
    const uid = e.msg.match(/切换\s*(.+)/)?.[1]?.trim()

    if (!uid) {
      await e.reply(`❌ 请提供要切换的UID\n使用方法：${config.getCommandExample("切换 你的UID")}`)
      return false
    }

    // 验证UID格式
    if (!/^\d{9}$/.test(uid)) {
      await e.reply("❌ UID格式错误，鸣潮UID应为9位数字")
      return false
    }

    const user = User.fromEvent(e)
    if (!user) {
      await e.reply("❌ 获取用户信息失败")
      return false
    }

    try {
      const result = await user.switchToUid(uid)

      if (result.success) {
        await e.reply(`✅ 已切换到UID：${uid}`)
      } else {
        await e.reply(`❌ 切换失败：${result.message}`)
      }

      return true
    } catch (error) {
      console.error("切换UID失败:", error)
      await e.reply("❌ 切换UID失败")
      return false
    }
  }

  /**
   * 删除指定UID
   */
  async deleteUid(e) {
    const uid = e.msg.match(/删除\s*(.+)/)?.[1]?.trim()

    if (!uid) {
      await e.reply(`❌ 请提供要删除的UID\n使用方法：${config.getCommandExample("删除 你的UID")}`)
      return false
    }

    // 验证UID格式
    if (!/^\d{9}$/.test(uid)) {
      await e.reply("❌ UID格式错误，鸣潮UID应为9位数字")
      return false
    }

    const user = User.fromEvent(e)
    if (!user) {
      await e.reply("❌ 获取用户信息失败")
      return false
    }

    try {
      const result = await user.deleteUid(uid)

      if (result.success) {
        await e.reply(`✅ 已删除UID：${uid}`)
      } else {
        await e.reply(`❌ 删除失败：${result.message}`)
      }

      return true
    } catch (error) {
      console.error("删除UID失败:", error)
      await e.reply("❌ 删除UID失败")
      return false
    }
  }

  /**
   * 删除所有UID
   */
  async deleteAllUids(e) {
    const user = User.fromEvent(e)
    if (!user) {
      await e.reply("❌ 获取用户信息失败")
      return false
    }

    try {
      const allUids = await user.getAllUids()

      if (allUids.length === 0) {
        await e.reply("❌ 您还未绑定任何UID")
        return false
      }

      const result = await user.deleteAllUids()

      if (result.success) {
        await e.reply(`✅ 已删除所有UID（共${allUids.length}个）`)
      } else {
        await e.reply(`❌ 删除失败：${result.message}`)
      }

      return true
    } catch (error) {
      console.error("删除所有UID失败:", error)
      await e.reply("❌ 删除所有UID失败")
      return false
    }
  }

  /**
   * 删除token
   */
  async deleteToken(e) {
    const uid = e.msg.match(/删除token\s*(.+)/)?.[1]?.trim()

    const user = User.fromEvent(e)
    if (!user) {
      await e.reply("❌ 获取用户信息失败")
      return false
    }

    try {
      let result

      if (uid) {
        // 验证UID格式
        if (!/^\d{9}$/.test(uid)) {
          await e.reply("❌ UID格式错误，鸣潮UID应为9位数字")
          return false
        }

        result = await user.deleteToken(uid)
      } else {
        // 删除当前UID的token
        result = await user.deleteToken()
      }

      if (result.success) {
        await e.reply(`✅ ${result.message}`)
      } else {
        await e.reply(`❌ 删除失败：${result.message}`)
      }

      return true
    } catch (error) {
      console.error("删除token失败:", error)
      await e.reply("❌ 删除token失败")
      return false
    }
  }
}
