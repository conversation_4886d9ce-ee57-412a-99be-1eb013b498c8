/* WutheringWavesUID 风格的角色卡片样式 */

/* 容器基础样式 */
.container {
  width: 1200px;
  max-width: 100%;
}

/* 头部区域 - WutheringWavesUID 风格 */
.head-box.ww-style {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.6));
  border-radius: 16px;
  margin: 20px 0;
  overflow: hidden;
  position: relative;
}

.header-bg {
  background: linear-gradient(45deg, #1a1a2e, #16213e);
  padding: 24px;
  position: relative;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-area .main-title {
  font-size: 28px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 4px;
}

.title-area .sub-title {
  font-size: 14px;
  color: #aaa;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.uid-area {
  text-align: right;
}

.uid-label {
  font-size: 12px;
  color: #aaa;
  margin-bottom: 4px;
}

.uid-value {
  font-size: 20px;
  font-weight: bold;
  color: #fff;
}

.stats-bar {
  display: flex;
  justify-content: space-around;
  background: rgba(255, 255, 255, 0.1);
  padding: 16px;
  margin-top: 16px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #aaa;
  margin-bottom: 4px;
}

.stat-value {
  display: block;
  font-size: 16px;
  font-weight: bold;
  color: #fff;
}

/* 角色卡片网格 */
.char-cards-container.ww-style {
  margin: 20px 0;
}

.char-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  padding: 20px;
}

/* 角色卡片样式 */
.char-card {
  position: relative;
  width: 200px;
  height: 280px;
  border-radius: 16px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
}

.char-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.3);
}

/* 卡片背景 */
.card-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.rarity-bg.rarity-5 {
  background: linear-gradient(135deg, #ffd700, #ffb347);
}

.rarity-bg.rarity-4 {
  background: linear-gradient(135deg, #9370db, #ba55d3);
}

.rarity-bg.rarity-3 {
  background: linear-gradient(135deg, #4169e1, #6495ed);
}

.card-frame {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
}

/* 角色头像 */
.char-avatar {
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 180px;
  padding-top: 20px;
}

.avatar-container {
  position: relative;
  width: 120px;
  height: 120px;
}

.avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-border {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border-radius: 50%;
  border: 3px solid;
}

.avatar-border.rarity-5 {
  border-color: #ffd700;
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

.avatar-border.rarity-4 {
  border-color: #9370db;
  box-shadow: 0 0 20px rgba(147, 112, 219, 0.5);
}

.avatar-border.rarity-3 {
  border-color: #4169e1;
  box-shadow: 0 0 20px rgba(65, 105, 225, 0.5);
}

/* 星级显示 */
.star-container {
  position: relative;
  z-index: 2;
  text-align: center;
  margin: 8px 0;
}

.stars {
  font-size: 16px;
}

.stars.rarity-5 {
  color: #ffd700;
}

.stars.rarity-4 {
  color: #9370db;
}

.stars.rarity-3 {
  color: #4169e1;
}

/* 角色信息 */
.char-info {
  position: relative;
  z-index: 2;
  text-align: center;
  padding: 0 16px 16px;
}

.char-name {
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 4px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.char-level {
  font-size: 12px;
  color: #ccc;
  margin-bottom: 2px;
}

.char-cons {
  font-size: 12px;
  color: #ffd700;
  font-weight: bold;
}

/* 状态徽章 */
.status-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 3;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: bold;
  text-transform: uppercase;
}

.status-badge.new {
  background: #4caf50;
  color: #fff;
}

.status-badge.updated {
  background: #ff9800;
  color: #fff;
}

/* 底部区域 */
.footer-section.ww-style {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.6));
  border-radius: 16px;
  margin: 20px 0;
  overflow: hidden;
}

.footer-bg {
  background: linear-gradient(45deg, #1a1a2e, #16213e);
  padding: 20px;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-legend {
  display: flex;
  gap: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.legend-dot.new {
  background: #4caf50;
}

.legend-dot.updated {
  background: #ff9800;
}

.footer-info {
  text-align: right;
}

.server-name {
  font-size: 14px;
  color: #fff;
  margin-bottom: 4px;
}

.tips {
  font-size: 12px;
  color: #aaa;
}

/* 版权信息 */
.copyright.ww-copyright {
  text-align: center;
  padding: 16px;
  color: #666;
  font-size: 12px;
}
