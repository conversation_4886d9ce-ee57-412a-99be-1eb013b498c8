/**
 * 测试新版rover-plugin的API功能
 */

import { WutheringWavesAPI } from "./rover-plugin/components/WutheringWavesAPI.js"

// 测试配置
const TEST_CONFIG = {
  token: "eyJhbGciOiJIUzI1NiJ9.eyJjcmVhdGVkIjoxNzUzNjMxMjg2Njc1LCJ1c2VySWQiOjEwODE2NzMyfQ.Z9BLJ1jtanrNfl1JLvcifr8ypSXEu7513sQvy9I-A1c",
  did: "6F02FE7B671ACA64694F19FB67EBEBAD07659846",
  testUid: "100000001"
}

async function testNewVersion() {
  console.log("🧪 测试新版rover-plugin API功能...")
  console.log(`Token: ${TEST_CONFIG.token.substring(0, 30)}...`)
  console.log(`DID: ${TEST_CONFIG.did}`)

  try {
    const api = new WutheringWavesAPI()

    // 测试1: 获取角色列表
    console.log("\n📋 测试1: 获取角色列表...")
    try {
      const roleListResult = await api.getRoleList(TEST_CONFIG.token, TEST_CONFIG.did)
      console.log(`角色列表: success=${roleListResult.success}, message=${roleListResult.message}`)
      
      if (roleListResult.success && roleListResult.data) {
        console.log(`✅ 角色列表获取成功`)
        console.log(`角色数量: ${roleListResult.data.length}`)
        if (roleListResult.data.length > 0) {
          const firstRole = roleListResult.data[0]
          console.log(`第一个角色: ${firstRole.roleName} (UID: ${firstRole.roleId})`)
          
          // 测试2: 使用真实UID测试bat token获取
          console.log("\n🔑 测试2: 使用真实UID测试bat token获取...")
          const batResult = await api.getRequestToken(
            firstRole.roleId, 
            TEST_CONFIG.token, 
            TEST_CONFIG.did
          )
          
          if (batResult.success) {
            console.log(`✅ bat token获取成功: ${batResult.token.substring(0, 20)}...`)
            
            // 测试3: 使用bat token测试API调用
            console.log("\n🎮 测试3: 使用bat token测试基础数据...")
            const baseDataResult = await api.getBaseInfo(firstRole.roleId, TEST_CONFIG.token)
            console.log(`基础数据: success=${baseDataResult.success}, message=${baseDataResult.message}`)
            
            if (baseDataResult.success) {
              console.log("✅ 基础数据获取成功")
            }
            
            // 测试4: 角色数据
            console.log("\n🎮 测试4: 使用bat token测试角色数据...")
            const roleDataResult = await api.getRoleInfo(firstRole.roleId, TEST_CONFIG.token)
            console.log(`角色数据: success=${roleDataResult.success}, message=${roleDataResult.message}`)
            
            if (roleDataResult.success) {
              console.log("✅ 角色数据获取成功")
            }
            
          } else {
            console.log(`❌ bat token获取失败: ${batResult.message}`)
          }
        }
      } else {
        console.log(`❌ 角色列表获取失败: ${roleListResult.message}`)
      }
    } catch (error) {
      console.error("角色列表测试失败:", error.message)
    }

    // 测试5: 直接测试bat token获取（使用测试UID）
    console.log("\n🔑 测试5: 直接测试bat token获取（测试UID）...")
    try {
      const batResult = await api.getRequestToken(TEST_CONFIG.testUid, TEST_CONFIG.token, TEST_CONFIG.did)
      
      if (batResult.success) {
        console.log(`✅ bat token获取成功: ${batResult.token.substring(0, 20)}...`)
        console.log("🎉 新版rover-plugin工作正常！")
        
        // 测试使用bat token的API
        console.log("\n🎮 测试6: 使用bat token测试基础数据...")
        const baseDataResult = await api.getBaseInfo(TEST_CONFIG.testUid, TEST_CONFIG.token)
        console.log(`基础数据: success=${baseDataResult.success}, message=${baseDataResult.message}`)
        
      } else {
        console.log(`❌ bat token获取失败: ${batResult.message}`)
      }
    } catch (error) {
      console.error("bat token测试失败:", error.message)
    }

    // 总结
    console.log("\n📊 测试总结:")
    console.log("1. 如果新版rover-plugin也返回code=270，说明是网络环境问题")
    console.log("2. 如果新版rover-plugin成功，说明还有其他实现差异需要修正")
    console.log("3. 建议：对比成功和失败的请求差异")

  } catch (error) {
    console.error("❌ 测试失败:", error)
  }
}

// 运行测试
testNewVersion()
