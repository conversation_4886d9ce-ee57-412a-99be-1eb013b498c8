/**
 * 调试头部设置测试脚本
 */

import Kuro<PERSON>pi from "./components/api/kuroapi.js"
import { WavesUser } from "./components/db/database.js"

// 测试配置
const TEST_CONFIG = {
  token: "eyJhbGciOiJIUzI1NiJ9.**********************************************************.Z9BLJ1jtanrNfl1JLvcifr8ypSXEu7513sQvy9I-A1c",
  did: "6F02FE7B671ACA64694F19FB67EBEBAD07659846",
  testUid: "*********"
}

async function debugHeaders() {
  console.log("🔍 调试头部设置...")
  
  try {
    const api = new KuroApi()
    
    // 测试1: 检查数据库中的用户数据
    console.log("\n📋 测试1: 检查数据库中的用户数据...")
    try {
      const userAccount = await WavesUser.selectDataByCookieAndUid(TEST_CONFIG.token, TEST_CONFIG.testUid)
      if (userAccount) {
        console.log("✅ 找到用户数据:")
        console.log(`  - UID: ${userAccount.uid}`)
        console.log(`  - Platform: ${userAccount.platform}`)
        console.log(`  - DID: ${userAccount.did}`)
        console.log(`  - BAT: ${userAccount.bat ? userAccount.bat.substring(0, 20) + "..." : "无"}`)
      } else {
        console.log("❌ 数据库中未找到用户数据")
      }
    } catch (error) {
      console.error("查询用户数据失败:", error.message)
    }
    
    // 测试2: 检查通用头部
    console.log("\n📋 测试2: 检查通用头部...")
    const commonHeader = await api.getCommonHeader("ios")
    console.log("通用头部 (iOS):")
    console.log(JSON.stringify(commonHeader, null, 2))
    
    // 测试3: 检查iOS头部
    console.log("\n📋 测试3: 检查iOS头部...")
    const iosHeader = await api.getHeadersIOS()
    console.log("iOS头部:")
    console.log(JSON.stringify(iosHeader, null, 2))
    
    // 测试4: 模拟getRequestToken中的头部设置逻辑
    console.log("\n📋 测试4: 模拟getRequestToken中的头部设置逻辑...")
    let headers
    try {
      const userAccount = await WavesUser.selectDataByCookieAndUid(TEST_CONFIG.token, TEST_CONFIG.testUid)
      console.log(`用户数据查询结果: ${userAccount ? "找到" : "未找到"}`)
      
      if (userAccount && userAccount.platform === "ios") {
        console.log("使用iOS头部 (用户平台为iOS)")
        headers = await api.getHeadersIOS()
      } else {
        console.log("使用通用头部 (用户平台非iOS或未找到用户)")
        headers = await api.getCommonHeader("ios")
      }
      
      // 如果有用户数据，设置相关头部
      if (userAccount) {
        console.log("设置用户相关头部...")
        if (userAccount.did) {
          headers.did = userAccount.did
          console.log(`  - 设置DID: ${userAccount.did}`)
        }
        if (userAccount.uid) {
          headers.roleId = userAccount.uid
          console.log(`  - 设置roleId: ${userAccount.uid}`)
        }
      }
    } catch (dbError) {
      console.log("数据库查询失败，使用iOS头部")
      headers = await api.getHeadersIOS()
    }
    
    // 按照新版rover-plugin的方式更新头部
    headers.token = TEST_CONFIG.token
    headers.did = TEST_CONFIG.did
    headers["b-at"] = ""
    
    console.log("\n最终头部:")
    console.log(JSON.stringify(headers, null, 2))
    
    // 测试5: 检查User-Agent
    console.log("\n📋 测试5: User-Agent分析...")
    console.log(`当前User-Agent: ${headers["User-Agent"]}`)
    
    const expectedIOSUserAgent = "Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) KuroGameBox/2.5.0"
    const expectedDesktopUserAgent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
    
    if (headers["User-Agent"] === expectedIOSUserAgent) {
      console.log("✅ 使用了正确的iOS User-Agent")
    } else if (headers["User-Agent"] === expectedDesktopUserAgent) {
      console.log("⚠️ 使用了桌面版User-Agent")
    } else {
      console.log("❓ 使用了未知的User-Agent")
    }
    
  } catch (error) {
    console.error("❌ 调试失败:", error)
  }
}

// 运行调试
debugHeaders()
