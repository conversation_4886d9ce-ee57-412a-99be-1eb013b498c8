import plugin from "../../../lib/plugins/plugin.js"
import User from "../components/User.js"
import config from "../components/config.js"
import { CharacterCache } from "../components/cache.js"
import { renderer } from "../utils/renderer.js"
import Background from "../components/Background.js"
import { dataProcessor } from "../utils/dataProcessor.js"
import { imageMapper } from "../utils/imageManager.js"
import RoleDetail from "../model/roleDetail.js"
import { DEFAULT_CONFIG, ERROR_MESSAGES, TEMPLATE_PATHS } from "../utils/constants.js"

/**
 * 声骸列表功能
 * 完全使用 miao-plugin 的 artis-list UI
 */
export class EchoList extends plugin {
  constructor() {
    super({
      name: "鸣潮-声骸列表",
      event: "message",
      priority: 600,
      rule: [
        {
          reg: config.generateCommandRegex("声骸列表"),
          fnc: "echoList",
        },
        {
          reg: config.generateCommandRegex("声骸"),
          fnc: "echoList",
        },
      ],
    })
  }

  /**
   * 声骸列表主功能
   */
  async echoList(e) {
    try {
      await e.reply("🔄 正在生成声骸列表，请稍候...")

      // 获取用户信息
      const user = User.fromEvent(e)
      if (!user) {
        await e.reply(ERROR_MESSAGES.USER_NOT_FOUND)
        return false
      }

      // 获取用户数据
      const userData = await user.getAllUserData()
      const { uid } = userData

      if (!uid) {
        await e.reply(
          `❌ 您还未绑定UID\n请先使用：${config.getCommandExample("绑定uid 你的UID")}\n或使用：${config.getCommandExample("添加token token,did")}`,
        )
        return false
      }

      // 解析显示数量
      const match = e.msg.match(/(\d+)$/)
      const defaultCount = config.get("echoList.defaultCount", 20)
      const maxCount = config.get("echoList.maxCount", 50)
      let showCount = match ? parseInt(match[1]) : defaultCount

      // 限制最大显示数量
      if (showCount > maxCount) {
        showCount = maxCount
        await e.reply(`⚠️ 显示数量超过限制，已调整为最大值 ${maxCount}`)
      }

      // 获取角色数据
      const roleDetailList = CharacterCache.getRoleDetailList(uid)
      if (!roleDetailList || !Array.isArray(roleDetailList) || roleDetailList.length === 0) {
        await e.reply(
          `❌ 未找到角色数据\n请先使用 ${config.getCommandExample("刷新面板")} 获取角色数据`,
        )
        return false
      }

      // 渲染声骸列表
      await this.renderEchoList(e, uid, roleDetailList, showCount)
      return true
    } catch (error) {
      console.error("声骸列表生成失败:", error)
      await e.reply("❌ 声骸列表生成失败，请稍后重试")
      return false
    }
  }

  /**
   * 渲染声骸列表 - 完全使用 miao-plugin 的 artis-list 格式
   */
  async renderEchoList(e, uid, roleDetailList, showCount) {
    try {
      // 移除自动资源下载，资源应该在刷新面板时已经下载
      // 这里只处理数据，不重复下载资源

      // 收集所有声骸数据
      const allEchos = []

      for (const roleDetailData of roleDetailList) {
        // 处理角色数据
        const processedData = await dataProcessor.processCharacterData(
          roleDetailData,
          roleDetailData.role?.roleId,
        )

        // 创建角色详情模型
        const roleDetail = new RoleDetail(processedData)

        // 获取角色头像路径
        let charSide = "/character-images/default-avatar.webp"
        if (roleDetailData.role?.roleIconUrl) {
          const localPath = imageMapper.checkLocalImageExists(roleDetailData.role.roleIconUrl)
          if (localPath) {
            charSide = imageMapper.getLocalPath(roleDetailData.role.roleIconUrl)
          }
        }

        // 处理声骸数据
        if (roleDetail.equipPhantomList && roleDetail.equipPhantomList.length > 0) {
          roleDetail.equipPhantomList.forEach(phantom => {
            if (phantom && phantom.name) {
              // 转换为 miao-plugin artis-list 格式
              const echoData = this.convertToArtisFormat(phantom, charSide)
              allEchos.push(echoData)
            }
          })
        }
      }

      // 按评分排序
      allEchos.sort((a, b) => (b.mark || 0) - (a.mark || 0))

      // 限制显示数量
      const displayEchos = showCount ? allEchos.slice(0, showCount) : allEchos

      console.log(`📊 声骸统计: 总计 ${allEchos.length} 个声骸，显示前 ${displayEchos.length} 个`)

      // 获取背景图
      const background = await Background.getBackground("list")

      // 创建声骸属性标题映射（模拟 miao-plugin 的 artisKeyTitle）
      const artisKeyTitle = this.createEchoKeyTitle()

      // 渲染数据 - 遵循统一规范，兼容 miao-plugin 格式
      const renderData = {
        // 必需字段
        uid,
        game: "ww",

        // 声骸列表特有字段
        artis: displayEchos, // 使用 artis 字段名，保持与 miao-plugin 一致
        artisKeyTitle, // 声骸属性标题映射

        // 可选字段
        element: DEFAULT_CONFIG.ELEMENT, // 默认元素，用于主题色
        background: background?.text,
        saveTime: new Date().toLocaleString(),
      }

      console.log(`[声骸列表] 渲染数据准备完成:`, {
        uid: renderData.uid,
        echoCount: displayEchos.length,
        background: renderData.background,
      })

      // 渲染图片 - 使用 miao-plugin 的 artis-list 模板
      const img = await renderer.render(e, TEMPLATE_PATHS.ECHO_LIST, renderData)

      if (img) {
        await e.reply(img)
      }
      // 渲染失败时不发送文字消息
    } catch (error) {
      console.error("渲染声骸列表失败:", error)
      // 渲染失败时不发送文字消息
    }
  }

  /**
   * 将声骸数据转换为 miao-plugin artis-list 格式
   */
  convertToArtisFormat(phantom, charSide) {
    // 主属性（显示所有主属性）
    const mainProps = phantom.mainPropList || []

    // 副属性
    const subProps = phantom.subPropList || []

    // 评分和评级
    const score = phantom.score || [0, "D"]
    const mark = score[0] || 0
    const markClass = score[1] || "D"

    // 获取属性名称映射
    const keyTitleMap = this.createEchoKeyTitle()

    // 角色权重（模拟 miao-plugin 的 charWeight）
    const charWeight = {}
    subProps.forEach(prop => {
      // 使用原始属性名称作为 key，确保与 HTML 模板中的 attr.key 匹配
      const originalKey = prop.attributeName
      // 根据属性有效性设置权重
      if (prop.valid && prop.valid !== "D") {
        charWeight[originalKey] = 80 // 有效属性
      } else {
        charWeight[originalKey] = 0 // 无效属性
      }
    })

    // 调试：输出主属性信息
    if (global.debugPhantom && mainProps.length > 0) {
      console.log(
        `🔍 声骸 ${phantom.name} 主属性:`,
        mainProps.map(p => `${p.attributeName}: ${p.attributeValue}`),
      )
    }

    return {
      name: phantom.name, // 声骸名称
      side: charSide, // 角色头像路径
      img: phantom.pic, // 声骸图标路径
      mark: mark, // 评分
      markClass: markClass, // 评级
      // 主属性列表（支持多条主属性）
      mainProps: mainProps.map(mainProp => ({
        key: mainProp.attributeName, // 使用原始属性名称作为key
        value: mainProp.attributeValue,
      })),
      // 兼容性：保留第一个主属性作为 main
      main:
        mainProps.length > 0
          ? {
              key: mainProps[0].attributeName,
              value: mainProps[0].attributeValue,
            }
          : null,
      attrs: subProps.map(prop => {
        // 调试：输出未映射的属性名称
        if (!keyTitleMap[prop.attributeName] && global.debugPhantom) {
          console.log(`⚠️ 未映射的属性名称: "${prop.attributeName}"`)
        }

        return {
          key: prop.attributeName, // 使用原始属性名称作为key
          value: prop.attributeValue,
          eff: null, // 不显示满值数字标记
          upNum: 0, // 强化次数（声骸没有这个概念，设为0）
          isMax: prop.isMax, // 保留原始的 isMax 标记用于样式
        }
      }),
      charWeight: charWeight,
      // 额外的声骸特有信息
      level: phantom.level,
      cost: phantom.cost,
      quality: phantom.quality,
      fetterDetail: phantom.fetterDetail,
    }
  }

  /**
   * 创建声骸属性标题映射 - 优化为更简洁的显示名称
   */
  createEchoKeyTitle() {
    return {
      // 基础属性
      生命: "生命",
      攻击: "攻击",
      防御: "防御",
      "生命%": "生命%",
      "攻击%": "攻击%",
      "防御%": "防御%",

      // 特殊属性
      暴击: "暴击",
      暴击伤害: "暴伤",
      治疗效果加成: "治疗",
      共鸣效率: "共鸣",

      // 元素伤害加成（主属性）- 简化名称
      气动伤害加成: "气动",
      冷凝伤害加成: "冷凝",
      导电伤害加成: "导电",
      热熔伤害加成: "热熔",
      衍射伤害加成: "衍射",
      湮灭伤害加成: "湮灭",

      // 技能伤害加成（副属性）- 进一步简化
      普攻伤害加成: "普攻",
      重击伤害加成: "重击",
      共鸣技能伤害加成: "技能",
      共鸣解放伤害加成: "共解",

      // 可能的其他属性名称变体
      普通攻击伤害加成: "普攻",
      重击攻击伤害加成: "重击",
      共鸣技能攻击伤害加成: "技能",
      共鸣解放攻击伤害加成: "共解",

      // 确保所有可能的属性名称都有映射
      生命值: "生命",
      攻击力: "攻击",
      防御力: "防御",
      "生命值%": "生命%",
      "攻击力%": "攻击%",
      "防御力%": "防御%",
      暴击率: "暴击",
      "暴击伤害%": "暴伤",
    }
  }
}
