@import "../common/base.less";

body {
  width: 600px;
}

.container {
  width: 600px;
  padding: 0;
  background-size: cover;
  overflow: hidden;
}

.profile-cont {
  padding: 0 10px;
  margin-right: 5px;
}

.basic {
  position: relative;
  margin: 0 -15px 15px -10px;
}

.basic:after {
  content: "";
  display: block;
  position: absolute;
  left: 8px;
  top: 115px;
  bottom: 0;
  right: 8px;
  box-shadow: 0 0 2px 0 #fff;
  border-radius: 5px;
  z-index: 1;
}

.main-pic {
  width: 1400px;
  height: 500px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin-left: -545px;
  position: relative;
  z-index: 2;
}

.detail {
  position: absolute;
  right: 20px;
  top: 20px;
  color: #fff;
  z-index: 3;
}

.char-name {
  font-size: 50px;
  .font-NZBZ;
  text-shadow: 0 0 3px #000, 2px 2px 4px rgba(0, 0, 0, .7);
  text-align: right;
}

.char-lv {
  margin-bottom: 20px;
  text-shadow: 0 0 3px #000, 2px 2px 4px rgba(0, 0, 0, .7);
  text-align: right;

  .cons {
    margin-left: 5px;
    vertical-align: bottom;
  }
}

@i: 16px;

.char-attr {
  backdrop-filter: blur(2px);
  background: rgba(0, 0, 0, .2);
  border-radius: 8px;
  overflow: hidden;

  li {
    width: 300px;
    font-size: 17px;
    list-style: none;
    height: 32px;
    line-height: 32px;
    text-shadow: 0 0 1px rgba(0, 0, 0, .5);
    display: flex;
    padding-left: 3px;

    &:nth-child(even) {
      background: rgba(0, 0, 0, .4)
    }

    &:nth-child(odd) {
      background: rgba(50, 50, 50, .4)
    }

    .weight {
      width: 36px;

      span {
        display: block;
        width: 30px;
        border-radius: 5px;
        font-size: 12px;
        height: 18px;
        line-height: 18px;
        background: rgba(0, 0, 0, .5);
        text-align: center;
        margin: 7px auto 0;

        &.gold {
          color: #ffe699;
        }
      }
    }

    .icon {
      width: @i+10px;
      padding: 2px 5px;

      i {
        display: inline-block;
        height: @i;
        width: @i;
        background-image: url("./imgs/icon.png");
        background-size: auto @i;
      }
    }

    .title {
      width: 75px;
      text-shadow: 0 0 1px rgba(0, 0, 0, .8), 1px 1px 3px rgb(0 0 0 / 50%);
    }

    .value {
      width: 100px;
      text-align: right;
      font-weight: normal;
      padding-right: 10px;
      text-shadow: 0 0 1px rgba(0, 0, 0, .8), 1px 1px 3px rgb(0 0 0 / 50%);
    }

    .value2 {
      font-weight: normal;
      width: 70px;
      text-align: right;
      font-size: 12px;
      padding: 4px 10px 0 0;
      background: rgba(0, 0, 0, .2);

      span {
        display: block;
        height: 13px;
        line-height: 13px;
        transform-origin: right center;
      }

      .base {
        color: #eee;

        &.zero {
          color: #aaaa;
        }
      }

      .plus {
        color: #90e800;
      }
    }


  }
}

.i-hp {
  background-position: @i*-1 0;
}

.i-atk {
  background-position: @i*-2 0;
}

.i-def {
  background-position: @i*-3 0;
}

.i-mastery {
  background-position: @i*-4 0;
}


.i-cpct {
  background-position: @i*-5 0;
}

.i-cdmg {
  background-position: @i*-6 0;
}

.i-stance {
  background-position: @i*-4 0;
}

.i-recharge {
  background-position: @i*-7 0;
}

.i-dmg {
  background-position: @i*-8 0;
}

.i-heal {
  background-position: @i*-9 0;
}

.i-speed {
  background-position: @i*-10 0;
}

.i-effPct {
  background-position: @i*-11 0;
}

.i-effDef {
  background-position: @i*-12 0;
}

.detail.attr {
  li {
    &:nth-child(even) {
      background: rgba(0, 0, 0, .4)
    }

    &:nth-child(odd) {
      background: rgba(50, 50, 50, .4)
    }

    strong {
      display: inline-block;
      position: absolute;
      right: 85px;
      text-align: right;
      font-weight: normal;
    }

    span {
      position: absolute;
      right: 0;
      text-align: left;
      width: 75px;
      display: inline-block;
      color: #90e800;
      font-size: 15px;
    }
  }
}

.talent-icon {
  width: 100px;
  height: 100px;
  padding: 5px;
  display: table;
  border-radius: 50%;
  position: relative;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center center;
  z-index: 90;

  img,
  .talent-icon-img {
    width: 46%;
    height: 46%;
    position: absolute;
    top: 50%;
    left: 50%;
    margin: -22% 0 0 -23%;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }

  strong {
    background: #fff;
    width: 34px;
    height: 26px;
    line-height: 26px;
    font-size: 17px;
    text-align: center;
    border-radius: 5px;
    position: absolute;
    bottom: 2px;
    left: 50%;
    margin-left: -15px;
    color: #000;
    box-shadow: 0 0 5px 0 #000;
  }

  &.talent-plus strong {
    background: #2e353e;
    color: #ffdfa0;
    font-weight: bold;
    box-shadow: 0 0 1px 0 #d3bc8e, 1px 1px 2px 0 rgba(0, 0, 0, 0.5);
  }

  &.talent-crown:after {
    content: "";
    display: block;
    width: 28px;
    height: 28px;
    background: url("../character/imgs/crown.png") no-repeat;
    background-size: contain;
    position: absolute;
    left: 50%;
    top: 0;
    margin-left: -14px;
  }
}

.char-talents {
  display: flex;
  width: 300px;
  margin: 0 0 10px 0;
}

.char-cons {
  display: flex;
  width: 250px;
  position: absolute;
  bottom: 5px;
  left: 20px;
}

.char-cons .talent-item,
.char-talents .talent-item {
  flex: 1;
}

.char-cons .talent-icon {
  width: 50px;
  height: 50px;
  margin: 0 -5px
}

.char-cons .talent-icon.off {
  filter: grayscale(100%);
  opacity: .4;
}


.data-info {
  position: absolute;
  bottom: -10px;
  right: 15px;
  font-size: 12px;
  color: rgba(255, 255, 255, .85);
  text-align: right;
  text-shadow: 1px 1px 1px #000;
  z-index: 2;
  line-height: 20px;
  padding-right: 5px;

  .time {
    margin-left: 5px;
  }
}


/***  dmg ***/
.cont {
  border-radius: 10px;
  background: url("../common/cont/card-bg.png") top left repeat-x;
  background-size: auto 100%;
  margin: 5px 0;
  position: relative;
  box-shadow: 0 0 1px 0 #ccc, 2px 2px 4px 0 rgba(50, 50, 50, .8);
  overflow: hidden;
  color: #fff;
  font-size: 16px;
}

.dmg-cont {
  display: table;
  margin: 10px 0;
  width: 100%;
}

.dmg-created-by {
  padding-left: 5px;
  float: right;
}

.dmg-mode .not-dmg-mode {
  display: none;
}

.cont-title {
  background: rgba(0, 0, 0, .4);
  color: #d3bc8e;
  padding: 10px 20px;
  text-align: left;

  span {
    font-size: 12px;
    color: #aaa;
    margin-left: 10px;
    font-weight: normal;
  }
}

.cont-footer {
  padding: 10px 15px;
  font-size: 12px;
  background: rgba(0, 0, 0, 0.5);
  font-weight: normal;
}

.cont-table {
  display: table;
  width: 100%;
}

.dmg-cont .tr {
  display: table-row;


  &:nth-child(even) {
    background: rgba(0, 0, 0, .4);
  }

  &:nth-child(odd) {
    background: rgba(50, 50, 50, .4);
  }

  & > div {
    display: table-cell;
    box-shadow: 0 0 1px 0 #fff;

    &.value-full {
      display: table;
      width: 200%;
    }

    &.value-none {
      box-shadow: none;
    }
  }


  &.thead {
    text-align: center;

    & > div {
      color: #d3bc8e;
      background: rgba(0, 0, 0, .4);
      line-height: 40px;
      height: 40px;
      font-size: 15px;
    }
  }

  .title,
  .th {
    color: #d3bc8e;
    padding-right: 15px;
    padding-left: 5px;
    text-align: right;
    background: rgba(0, 0, 0, .4);
    min-width: 100px;
    font-size: 15px;
    white-space: nowrap;
  }
}

.profile-mode .dmg-idx {
  display: none !important;
}

.profile-mode .dmg-title {
  width: 40%;
}

.dmg-mode,
.weapon-mode {
  .dmg-idx {
    display: table-cell;
    width: 5%;
    min-width: initial;
    padding-right: 0;
    text-align: center;
  }

  .dmg-title {
    width: 31%;
    min-width: initial;
    text-align: left;
    padding-left: 10px;
    padding-right: 0;
  }
}


.dmg .value {
  text-align: center;
  color: #fff;
  display: block;
  height: 40px;
  font-size: 18px;
  line-height: 40px;
  width: 32%
}

.dmg-notice {
  font-size: 12px;
  text-align: right;
  color: #f5f5f5;
  margin-right: 15px;
}


/*** artis***/
.artis {
  display: flex;
  width: 600px;
  flex-wrap: wrap;
  margin: 10px -5px 5px;


  .item {
    width: 185px;
    border-radius: 10px;
    background: url("../common/cont/card-bg.png") top left repeat-x;
    background-size: auto 100%;
    margin: 5px;
    //height: 200px;
    position: relative;
    box-shadow: 0 0 1px 0 #ccc, 2px 2px 4px 0 rgba(50, 50, 50, .8);
    overflow: hidden;

    &.item {
      &.arti-class-ACE,
      &.arti-class-MAX {
        .head {

        }
      }
    }


    .arti-icon {
      width: 60px;
      height: 60px;
      position: absolute;
      left: 2px;
      top: 3px;


      span {
        position: absolute;
        right: 2px;
        bottom: 0;
        margin-left: 5px;
        background: rgba(0, 0, 0, .5);
        border-radius: 5px;
        height: 18px;
        line-height: 18px;
        padding: 0 3px;
        color: #fff;
        font-size: 12px;
        display: block;
      }

      .img {
        width: 50px;
        height: 50px;
        margin: 5px;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
      }
    }

  }

  .head {
    color: #fff;
    padding: 12px 0 8px 68px;

    strong {
      font-size: 15px;
      display: block;
      white-space: nowrap;
      overflow: hidden;
      //font-font: YS;
    }

    span {
      font-size: 14px;
    }

    .mark {
    }
  }
}

.mark-ACE,
.mark-MAX {
  color: #e85656;
  font-weight: bold;
}

.mark-SSS,
.mark-SS {
  color: #ffe699;
  font-weight: bold;
}

.mark-S,
.mark-A {
  color: #d699ff;
  font-weight: bold;
}

.arti-main {
  color: #fff;
  padding: 6px 15px;
}


.artis ul.detail {
  width: 100%;
  padding: 0;
  position: initial;
  display: table;
}

.artis ul.detail li,
.arti-info ul.detail li {
  padding: 0 3px;
  font-size: 14px;
  position: relative;
  width: 100%;
  display: table-row;
  line-height: 26px;
  height: 26px;
  white-space: nowrap;

  span {
    position: initial;
    display: table-cell;
    color: #fff;

    &.title {
      text-align: left;
      padding-left: 30px;
      font-size: 14px;

      i.eff {
        position: absolute;
        display: block;
        left: 3px;
        top: 4px;
        font-size: 12px;
        font-style: normal;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 5px;
        height: 18px;
        line-height: 18px;
        width: 23px;
        text-align: center;
      }

      i.up-num {
        position: absolute;
        display: block;
        left: 91px;
        top: 9px;
        height: 8px;
        width: 50px;
        background-image: url('./imgs/up-num-icon1.png');
        background-position: 0 0;
        background-repeat: no-repeat;
        background-size: auto 500%;


        &.up-5 {
          background-position: 0 -8px;
        }

        &.up-4 {
          background-position: 0 -16px;
        }

        &.up-3 {
          background-position: 0 -24px;
        }

        &.up-2 {
          background-position: 0 -32px;
        }

        &.up-1 {
          background: none !important;
        }
      }
    }

    &.val {
      text-align: right;
      padding-right: 10px;
      font-size: 14px;
    }
  }


  &.great span.title {
    color: #ffe699;

    i.up-num {
      background-image: url("./imgs/up-num-icon2.png");
      background-size: auto 500%;
    }
  }

  &.nouse span {
    color: #888;

    i.up-num {
      background-image: url("./imgs/up-num-icon0.png");
      background-size: auto 500%;
    }
  }

  &.arti-main {
    font-size: 16px;
    padding: 3px 3px;
    font-weight: bold;
  }
}


.artis .weapon .star {
  height: 20px;
  width: 100px;
  background: url("../common/item/star.png") no-repeat;
  background-size: 100px 100px;
  transform: scale(0.8);
  transform-origin: 100px 10px;
  display: inline-block;
  display: none;
}

.artis .weapon .star.star-2 {
  background-position: 0 -20px;
}

.artis .weapon .star.star-3 {
  background-position: 0 -40px;
}

.artis .weapon .star.star-4 {
  background-position: 0 -60px;
}

.artis .weapon .star.star-5 {
  background-position: 0 -80px;
}


.artis .weapon {
  height: 190px;
  overflow: visible;


  .img {
    width: 100px;
    height: 100px;
    top: -10px;
    right: -10px;
    position: absolute;
    z-index: 2;
    background-position: center;
    background-size: contain;
    background-repeat: no-repeat;
  }

  .head {
    position: relative;
    height: 90px;
    padding: 15px 0 10px 15px;
    z-index: 3;
    border-radius: 10px 10px 0 0;
    background: linear-gradient(to right, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7), rgba(25, 25, 25, 0.5), rgba(25, 25, 25, 0), rgba(25, 25, 25, 0));

    strong {
      font-size: 15px;
      margin-bottom: 3px;
    }

    & > span {
      display: block;

    }
  }

  span.info {
    font-size: 14px;
    margin-bottom: 8px;
  }

  .affix {
    color: #000;
    padding: 0 7px;
    border-radius: 4px;
    font-size: 14px;
    width: 40px;
    margin-right: 5px;
  }

  .affix-1 {
    box-shadow: 0 0 4px 0 #a3a3a3 inset;
    background: #ebebebaa;
  }

  .affix-2 {
    box-shadow: 0 0 4px 0 #51b72fbd inset;
    background: #ddffdeaa;
  }

  .affix-3 {
    box-shadow: 0 0 4px 0 #396cdecf inset;
    background: #ddebffaa;
  }

  .affix-4 {
    box-shadow: 0 0 4px 0 #c539debf inset;
    background: #ffddf0aa;
  }

  .affix-5 {
    box-shadow: 0 0 4px 0 #deaf39 inset;
    background: #fff6dd;
  }

  .weapon-attr {
    font-size: 14px;
    text-shadow: 0 0 1px #000, 1px 1px 2px rgba(0, 0, 0, 0.7);
    display: flex;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 26px;
    width: 100%;
    background: rgba(0, 0, 0, .3);
    line-height: 26px;
    text-align: center;
    padding: 0 5px 0 10px;

    div {
      width: 50%;

      span {
        color: #ffe699;
        font-weight: bold;
      }
    }
  }

  .weapon-desc-cont {
    height: 100px;
    border-radius: 0 0 10px 10px;
    background: rgba(0, 0, 0, .4);
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .weapon-desc {
    height: 100px;
    padding: 6px 5px 7px 10px;
    color: #bbb;
    display: table-cell;
    vertical-align: middle;
  }
}

.arti-mark-cont {
  display: flex;
}

.arti-stat {
  width: 185px;
  background: rgba(0, 0, 0, .4);
  box-shadow: 0 0 1px 0 #ccc, 2px 2px 4px 0 rgba(50, 50, 50, 0.8);
}

.arti-class-title {
  height: 25px;
  line-height: 25px;
  font-size: 12px;
  color: #fff;
  text-align: center;
  color: rgba(255, 255, 255, .9);
  text-shadow: 0 0 2px #000;
  background: rgba(0, 0, 0, .5);
}

.arti-stat-ret {
  height: 78px;
  padding: 12px 10px 2px;
  width: 100%;
  display: table;

  & > div {
    display: table-cell;
    text-align: center;
    color: #fff;
  }

  strong {
    display: block;
    height: 35px;
    font-size: 30px;
    line-height: 32px;
  }
}

.arti-info {
  width: 390px;

  .arti-all-attr {
    height: 78px;

    .arti-attr {
      position: relative;
      right: 0;
      top: 0;
      display: flex;
      flex-wrap: wrap;
      flex-direction: column;
      height: 90px;
      width: 100%;

      li {
        width: 33.33%;
        display: flex;

        i.eff {
          width: 28px !important;
          top: 6px !important;
          left: 5px !important;
        }

        span {

        }

        span.title {
          padding-left: 36px;
          width: 60%;
        }

        span.val {
          padding-right: 5px;
          font-size: 13px;
          width: 40%;
        }
      }
    }
  }

  .arti-notice {
    height: 25px;
    line-height: 25px;
    background: rgba(0, 0, 0, .7);
    font-size: 10px;
    padding-left: 10px;
    text-align: center;
    margin-left: 1px;
  }
}

.dmg-msg {
  font-size: 13px;
  font-weight: normal;
}

.dmg-msg .thead > div {
  text-align: left;
  padding-left: 10px;
}

.dmg-msg .th {
  text-align: left;
  padding-left: 10px;
}

.dmg-msg .tr .td {
  padding: 8px 10px;
}

.dmg-msg .info {
  font-size: 12px;
  text-align: left;
  padding-left: 10px;
  font-weight: normal;
}


.dmg-calc {

}

.dmg-calc .thead {

}

.dmg-calc .thead > div {
  line-height: initial;
}

.dmg-calc .cont-table div {
  vertical-align: middle;
  text-align: center;
  white-space: nowrap;
}

.dmg-calc .title {
  text-align: center;
  padding-right: 0;
  min-width: 70px;

}

.dmg-calc .td {
  padding: 5px 0;
}

.dmg-calc strong {
  font-weight: normal;
  display: block;
}

.dmg-calc span {
  font-size: 12px;
  color: #aaa;
}

.dmg-calc .na,
.dmg-calc .eq {
  background: rgba(50, 50, 50, .5);
}

.dmg-calc .na {
  color: #888;
}

.dmg-calc .lt {
  background: rgba(23, 112, 41, 0.5);
}

.dmg-calc .gt {
  background: rgba(112, 23, 23, 0.5);
}

.dmg-desc {
  color: #aaa;
}

.dmg-desc ul {
  padding-left: 10px;
}

.dmg-desc ul li {
  color: #aaa;
}

.dmg-desc strong {
  color: #d3bc8e;
  display: inline;
  padding: 0 3px;
  font-weight: normal;
}


.arti-stat span {
  font-size: 12px;
  line-height: 20px;
  color: #bbb;
}

.game-sr {

  .artis-weapon {
    display: none;
  }

  .char-lv {
    margin-bottom: 25px;
  }

  .char-attr {
    .icon i {
      background-image: url('./imgs/icon-sr.png');
    }
  }
}

.artis {
  .item.arti {
    overflow: visible;

    .head {
      position: relative;
      border-radius: 10px 10px 0 0;
      text-shadow: 0 0 1px #000, 1px 1px 2px rgba(0, 0, 0, 0.7);
      padding: 15px 10px 5px 15px;
      background: linear-gradient(to right, rgba(0, 0, 0, .7), rgba(0, 0, 0, .7), rgba(25, 25, 25, .3), rgba(25, 25, 25, .0), rgba(25, 25, 25, 0));
    }

    .arti-icon {
      left: auto;
      right: 0;
      top: -12px;
      width: 90px;
      height: 90px;

      .img {
        width: 100%;
        height: 100%;
        margin: 0;
        -webkit-mask: linear-gradient(45deg, #0000 0, #0005 30%, #000 50%);
      }

      span {
        top: 50px;
        right: 8px;
        background: rgba(0, 0, 0, .8);
      }
    }
  }

  ul.detail {
    backdrop-filter: blur(2px);
    border-radius: 0 0 10px 10px;
    overflow: hidden;

    li.arti-main {
      background: rgba(25, 25, 25, .5);

      .title {
        padding-left: 15px;
      }
    }
  }
}

.sr-weapon {
  margin: 10px 0;
  border-radius: 10px;
  background: url("../common/cont/card-bg.png") top left repeat-x;
  background-size: auto 150%;
  position: relative;
  box-shadow: 0 0 1px 0 #ccc, 2px 2px 4px 0 rgba(50, 50, 50, .8);
  overflow: hidden;
  display: table;
  color: #fff;
  min-height: 100px;
  width: 100%;

  .weapon-img {
    display: table-cell;
    width: 185px;
    height: 100%;
    min-height: 120px;
    background-size: 100% auto;
    background-position: 0 5%;
    box-shadow: 0 0 1px 0 #ccc, 2px 2px 4px 0 rgba(50, 50, 50, .8);
  }

  .weapon-info {
    display: table-cell;
    padding: 10px 10px 5px 15px;
  }

  .weapon-title {

    span {
      font-size: 14px;
    }

    strong {
      color: #d3bc8e;
      font-weight: normal;
    }
  }

  .weapon-attr {
    font-size: 12px;
    height: 25px;
    line-height: 25px;
    display: flex;

    .attr {
      padding-left: 22px;
      padding-right: 7px;
      margin-right: 10px;
      position: relative;
      line-height: 22px;
      height: 22px;
      font-size: 12px;

      color: #ffe699;
      display: inline-block;
      border-radius: 4px;


      background: rgba(0, 0, 0, 0.5);
      text-align: center;

      span {
        font-size: 12px;
        display: none;
      }

      &:before {
        content: "";
        display: inline-block;
        width: 22px;
        height: 22px;
        background-size: auto 100%;
        background-position: top center;
        vertical-align: middle;
        position: absolute;
        left: 0;
        top: 0;
      }
    }

    .i-hp:before {
      background-image: url('../meta-sr/public/icons/attr-hp.webp')
    }

    .i-atk:before {
      background-image: url('../meta-sr/public/icons/attr-atk.webp')
    }

    .i-def:before {
      background-image: url('../meta-sr/public/icons/attr-def.webp')
    }
  }

  .weapon-desc {
    margin-top: 7px;
    text-shadow: 0 0 1px rgba(0, 0, 0, 0.7), 1px 1px 3px rgba(0, 0, 0, 0.4);
  }
}

.weapon-desc {
  font-size: 12px;
  font-weight: normal;
  color: #fff;
  word-break: break-all;

  nobr {
    color: #ffe699;
    display: inline-block;
    border-radius: 4px;
    font-size: 12px;
    height: 16px;
    line-height: 16px;
    background: rgba(0, 0, 0, 0.5);
    text-align: center;
    padding: 0 3px;
    margin: 0 2px;
  }
}

.sr-talent {
  margin: 10px 0;
  border-radius: 10px;
  background: url("../common/cont/card-bg.png") top left repeat-x;
  background-size: auto 150%;
  position: relative;
  box-shadow: 0 0 1px 0 #ccc, 2px 2px 4px 0 rgba(50, 50, 50, .8);
  overflow: hidden;
  display: flex;
  color: #fff;
  height: 72px;
  padding: 8px 10px;


  .char-talents {
    transform-origin: center left;
    margin: 0;
    width: 260px;


    .talent-item {
      margin: 0 -3px;
    }

    .talent-icon {
      width: 60px;
      height: 60px;

      strong {
        font-size: 13px;
        width: 22px;
        height: 20px;
        line-height: 20px;
        margin-left: -11px;
        border-radius: 4px;
      }

      span {
        position: absolute;
        top: -3px;
        font-size: 12px;
        left: 0;
        right: 0;
        text-align: center;
        color: #fff;
        text-shadow: 0 0 3px #000, 1px 1px 1px #000;
      }

      &.talent-crown:after {
        width: 22px;
        height: 22px;
        margin: 2px 0 0 5px;
        background-image: url('../character/imgs/crown-sr.webp');
        display: none;
      }
    }
  }

  .char-trees {
    width: 290px;
    padding-left: 10px;
    position: relative;

    .talent-icon {
      margin: 0 -5px;

      &.off {
        filter: grayscale(100%);
        opacity: .4;
      }
    }

    &:before {
      content: "";
      display: block;
      width: 1px;
      height: 30px;
      background: rgba(255, 255, 255, .8);
      position: absolute;
      left: 0;
      top: 15px;
    }

    .talent-item {
      width: 40px;

      .talent-icon {
        margin: 0;
      }
    }

    .tree-item {
      width: 20px;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .talent-icon {
        width: 30px;
        height: 30px;
        margin: 0 -5px;
      }

      .talent-icon-img {

      }
    }
  }

}

.copyright.ad {
  font-size: 12px;
}
