import path from "path"
import { dataPath } from "../path.js"

// 用户数据保存文件
export const PLAYER_PTH = path.join(dataPath, "players")

// 游戏素材
export const RESOURCE_PATH = path.join(dataPath, "resource")
export const PHANTOM_PATH = path.join(RESOURCE_PATH, "phantom")
export const MATERIAL_PATH = path.join(RESOURCE_PATH, "material")
export const FETTER_PATH = path.join(RESOURCE_PATH, "fetter")
export const AVATAR_PATH = path.join(RESOURCE_PATH, "waves_avatar")
export const WEAPON_PATH = path.join(RESOURCE_PATH, "waves_weapon")
export const ROLE_PILE_PATH = path.join(RESOURCE_PATH, "role_pile")
export const ROLE_DETAIL_PATH = path.join(RESOURCE_PATH, "role_detail")
export const ROLE_DETAIL_SKILL_PATH = path.join(R<PERSON><PERSON>_DETAIL_PATH, "skill")
export const ROLE_DETAIL_CHAINS_PATH = path.join(ROLE_DETAIL_PATH, "chains")
export const SHARE_BG_PATH = path.join(RESOURCE_PATH, "share")

// 攻略
export const GUIDE_PATH = path.join(dataPath, "guide_new")
// 小沐XMu 攻略库
export const XMU_GUIDE_PATH = path.join(GUIDE_PATH, "XMu")
// Moealkyne 攻略库
export const MOEALKYNE_GUIDE_PATH = path.join(GUIDE_PATH, "Moealkyne")
// 金铃子攻略组 攻略库
export const JINLINGZI_GUIDE_PATH = path.join(GUIDE_PATH, "JinLingZi")
// 結星 攻略库
export const JIEXING_GUIDE_PATH = path.join(GUIDE_PATH, "JieXing")
// 小羊 攻略库
export const XIAOYANG_GUIDE_PATH = path.join(GUIDE_PATH, "XiaoYang")
// 吃我无痕 攻略库
export const WUHEN_GUIDE_PATH = path.join(GUIDE_PATH, "WuHen")

// 其他的素材
export const OTHER_PATH = path.join(dataPath, "other")
export const CALENDAR_PATH = path.join(OTHER_PATH, "calendar")
export const SLASH_PATH = path.join(OTHER_PATH, "slash")
export const CHALLENGE_PATH = path.join(OTHER_PATH, "challenge")
export const ANN_CARD_PATH = path.join(OTHER_PATH, "ann_card")

// 别名
export const ALIAS_PATH = path.join(dataPath, "alias")
export const CUSTOM_CHAR_ALIAS_PATH = path.join(ALIAS_PATH, "char_alias.json")
export const CUSTOM_SONATA_ALIAS_PATH = path.join(ALIAS_PATH, "sonata_alias.json")
export const CUSTOM_WEAPON_ALIAS_PATH = path.join(ALIAS_PATH, "weapon_alias.json")
export const CUSTOM_ECHO_ALIAS_PATH = path.join(ALIAS_PATH, "echo_alias.json")

export const EPATH_MAP = {
  "resource/avatar": AVATAR_PATH,
  "resource/weapon": WEAPON_PATH,
  "resource/role_pile": ROLE_PILE_PATH,
  "resource/role_detail/skill": ROLE_DETAIL_SKILL_PATH,
  "resource/role_detail/chains": ROLE_DETAIL_CHAINS_PATH,
  "resource/share": SHARE_BG_PATH,
  "resource/phantom": PHANTOM_PATH,
  "resource/material": MATERIAL_PATH,
  "resource/guide/XMu": XMU_GUIDE_PATH,
  "resource/guide/Moealkyne": MOEALKYNE_GUIDE_PATH,
  "resource/guide/JinLingZi": JINLINGZI_GUIDE_PATH,
  "resource/guide/JieXing": JIEXING_GUIDE_PATH,
  "resource/guide/XiaoYang": XIAOYANG_GUIDE_PATH,
  "resource/guide/WuHen": WUHEN_GUIDE_PATH,
}

function initDirectories() {
  const directories = [
    // 用户数据保存文件
    PLAYER_PTH,
    // 游戏素材
    RESOURCE_PATH,
    PHANTOM_PATH,
    MATERIAL_PATH,
    FETTER_PATH,
    AVATAR_PATH,
    WEAPON_PATH,
    ROLE_PILE_PATH,
    ROLE_DETAIL_PATH,
    ROLE_DETAIL_SKILL_PATH,
    ROLE_DETAIL_CHAINS_PATH,
    SHARE_BG_PATH,
    // 攻略
    GUIDE_PATH,
    XMU_GUIDE_PATH,
    MOEALKYNE_GUIDE_PATH,
    JINLINGZI_GUIDE_PATH,
    JIEXING_GUIDE_PATH,
    XIAOYANG_GUIDE_PATH,
    WUHEN_GUIDE_PATH,
    // 其他的素材
    OTHER_PATH,
    CALENDAR_PATH,
    SLASH_PATH,
    CHALLENGE_PATH,
    ANN_CARD_PATH,
    // 别名
    ALIAS_PATH,
  ]

  directories.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true })
    }
  })
}

initDirectories()
