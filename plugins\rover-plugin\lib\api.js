/**
 * Rover Plugin API - 供外部插件调用的基础功能接口
 *
 * 这个文件提供了rover-plugin的核心功能接口，
 * 外部插件可以通过导入这个文件来使用我们的数据库和API功能
 *
 * 导入方式：
 * import { UserBinding, KuroAPI, Database, Config } from "../rover-plugin/lib/api.js"
 */

import { WavesUser, WavesBind } from "../components/db/database.js"
import KuroApi from "../components/api/kuroapi.js"
import config from "../components/config.js"
import { User } from "../components/User.js"

/**
 * 用户绑定状态检查
 */
export class UserBinding {
  /**
   * 检查用户是否已绑定UID和token（详细信息）
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 详细的绑定状态信息
   */
  static async checkUserBinding(userId) {
    return await User.checkUserBinding(userId)
  }

  /**
   * 检查用户是否已绑定（简化版）
   * @param {string} userId - 用户ID
   * @returns {Promise<boolean>} 是否已完整绑定
   */
  static async isUserBound(userId) {
    return await User.isUserBound(userId)
  }

  /**
   * 获取用户的UID
   * @param {string} userId - 用户ID
   * @returns {Promise<string|null>} 用户的UID
   */
  static async getUserUid(userId) {
    return await WavesBind.getUidByUser(userId)
  }

  /**
   * 获取用户的token
   * @param {string} userId - 用户ID
   * @returns {Promise<string|null>} 用户的token
   */
  static async getUserToken(userId) {
    return await WavesUser.getTokenByUser(userId)
  }

  /**
   * 获取用户的DID
   * @param {string} userId - 用户ID
   * @returns {Promise<string|null>} 用户的DID
   */
  static async getUserDid(userId) {
    return await WavesUser.getDidByUser(userId)
  }

  /**
   * 获取用户的完整账号信息
   * @param {string} userId - 用户ID
   * @returns {Promise<Object|null>} 用户的主账号信息
   */
  static async getUserAccount(userId) {
    return await WavesUser.getPrimaryAccount(userId)
  }

  /**
   * 获取用户的bat token
   * @param {string} userId - 用户ID
   * @returns {Promise<string|null>} 用户的bat token
   */
  static async getUserBatToken(userId) {
    const account = await WavesUser.getPrimaryAccount(userId)
    return account ? account.bat : null
  }
}

/**
 * 库街区API调用 - 统一的API调用接口，支持代理
 */
export class KuroAPI {
  /**
   * 创建KuroApi实例
   * @returns {KuroApi} KuroApi实例
   */
  static createInstance() {
    return new KuroApi()
  }

  /**
   * 获取用户角色列表
   * @param {string} token - 用户token
   * @param {string} did - 设备ID
   * @returns {Promise<Object>} API响应
   */
  static async getRoleList(token, did) {
    const api = new KuroApi()
    return await api.getRoleList(token, did)
  }

  /**
   * 刷新用户的bat token (内部方法，自动调用)
   * @param {string} userId - 用户ID
   * @param {boolean} forceRefresh - 是否强制刷新 (可选，默认true)
   * @returns {Promise<Object>} 刷新结果
   * @private
   */
  static async _refreshUserBatToken(userId, forceRefresh = true) {
    try {
      // 获取用户账号信息
      const account = await WavesUser.getPrimaryAccount(userId)
      if (!account || !account.cookie || !account.uid || !account.did) {
        return {
          success: false,
          message: "用户账号信息不完整",
          code: "ACCOUNT_INCOMPLETE",
        }
      }

      // 刷新bat token
      const api = new KuroApi()
      const batToken = await api.getRequestToken(
        account.uid,
        account.cookie,
        account.did,
        null,
        forceRefresh, // 使用参数控制是否强制刷新
      )

      if (batToken) {
        return {
          success: true,
          message: "bat token刷新成功",
          batToken: batToken,
          code: "SUCCESS",
        }
      } else {
        return {
          success: false,
          message: "bat token刷新失败",
          code: "REFRESH_FAILED",
        }
      }
    } catch (error) {
      console.error("刷新bat token失败:", error)
      return {
        success: false,
        message: "刷新过程中发生错误: " + error.message,
        code: "ERROR",
        error: error,
      }
    }
  }

  /**
   * 获取角色基础数据
   * @param {string} wavesId - 鸣潮UID
   * @param {string} token - 用户token
   * @param {string} bat - bat token
   * @returns {Promise<Object>} API响应
   */
  static async getBaseData(wavesId, token, bat) {
    const api = new KuroApi()
    return await api.getBaseData(wavesId, token, bat)
  }

  /**
   * 获取角色数据
   * @param {string} wavesId - 鸣潮UID
   * @param {string} bat - bat token
   * @returns {Promise<Object>} API响应
   */
  static async getRoleData(wavesId, bat) {
    const api = new KuroApi()
    return await api.getRoleData(wavesId, bat)
  }

  /**
   * 获取角色详细信息
   * @param {string} wavesId - 鸣潮UID
   * @param {string} charId - 角色ID
   * @param {string} bat - bat token
   * @returns {Promise<Object>} API响应
   */
  static async getRoleDetail(wavesId, charId, bat) {
    const api = new KuroApi()
    return await api.getRoleDetail(wavesId, charId, bat)
  }

  /**
   * 为用户刷新面板数据 (供外部插件调用)
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 刷新结果
   */
  static async refreshUserPanel(userId) {
    return await this._smartApiCall(
      userId,
      async (uid, token, did, batToken, api) => {
        // 获取角色列表 (不需要bat token)
        const roleListResponse = await api.getRoleList(token, did)
        if (roleListResponse.code !== 200) return roleListResponse

        // 获取角色基础数据
        const baseDataResponse = await api.getBaseData(uid, token, batToken)
        if (baseDataResponse.code !== 200) return baseDataResponse

        // 获取角色数据
        const roleDataResponse = await api.getRoleData(uid, batToken)
        if (roleDataResponse.code !== 200) return roleDataResponse

        return {
          code: 200,
          msg: "面板数据刷新成功",
          data: {
            roleList: roleListResponse.data,
            baseData: baseDataResponse.data,
            roleData: roleDataResponse.data,
            uid: uid,
          },
        }
      },
      "刷新面板数据",
    )
  }

  /**
   * 检查API响应是否表示token过期（参考WutheringWavesUID实现）
   * @param {Object} result - API响应结果
   * @returns {boolean} 是否token过期
   * @private
   */
  static _isTokenExpired(result) {
    if (!result) return true

    // 检查响应码 - 200和10902是成功码
    if (result.code !== 200 && result.code !== 10902) {
      // 检查错误消息中的关键词
      const msg = result.msg || result.message || ""
      const expiredKeywords = ["重新登录", "登录已过期", "登录失效", "token过期", "token失效"]

      // 如果包含过期关键词，确定是token过期
      if (expiredKeywords.some(keyword => msg.includes(keyword))) {
        return true
      }

      // 其他错误码也可能需要重试
      return true
    }

    return false
  }

  /**
   * 智能API调用 - 自动处理token刷新和重试（参考WutheringWavesUID实现）
   * @param {string} userId - 用户ID
   * @param {Function} apiCall - API调用函数
   * @param {string} apiName - API名称（用于日志）
   * @returns {Promise<Object>} API响应
   * @private
   */
  static async _smartApiCall(userId, apiCall, apiName = "API") {
    try {
      // 检查用户绑定状态
      const bindingInfo = await UserBinding.checkUserBinding(userId)
      if (!bindingInfo.hasValidAccount) {
        return {
          success: false,
          message: "用户未绑定有效账号",
          code: "NOT_BOUND",
        }
      }

      const { uid, token, did } = bindingInfo
      let { batToken } = bindingInfo
      const api = new KuroApi()

      // 第一次尝试调用API
      let result = await apiCall(uid, token, did, batToken, api)

      // 检查是否需要刷新token（使用WutheringWavesUID的判定逻辑）
      if (this._isTokenExpired(result)) {
        console.log(`${apiName} 检测到token过期，尝试刷新bat token...`)

        const refreshResult = await this._refreshUserBatToken(userId, true)
        if (refreshResult.success) {
          batToken = refreshResult.batToken
          console.log(`${apiName} bat token刷新成功，重新调用API...`)
          // 使用新token重试
          result = await apiCall(uid, token, did, batToken, api)
        } else {
          console.log(`${apiName} bat token刷新失败:`, refreshResult.message)
          return {
            success: false,
            message: `Token刷新失败: ${refreshResult.message}`,
            code: "TOKEN_REFRESH_FAILED",
            data: null,
          }
        }
      }

      // 最终结果检查（200和10902都是成功码）
      const isSuccess = result && (result.code === 200 || result.code === 10902)

      return {
        success: isSuccess,
        message: result ? result.msg || result.message || "调用成功" : `${apiName}调用失败`,
        code: result ? result.code : "API_FAILED",
        data: result ? result.data : null,
        apiResponse: result,
      }
    } catch (error) {
      console.error(`${apiName} 调用失败:`, error)
      return {
        success: false,
        message: `${apiName}调用过程中发生错误: ${error.message}`,
        code: "ERROR",
        error: error,
      }
    }
  }

  /**
   * 获取用户的完整游戏数据 (供外部插件调用)
   * @param {string} userId - 用户ID
   * @param {boolean} includeDetails - 是否包含详细数据 (可选，默认false)
   * @returns {Promise<Object>} 游戏数据
   */
  static async getUserGameData(userId, includeDetails = false) {
    return await this._smartApiCall(
      userId,
      async (uid, token, did, batToken, api) => {
        // 获取基础数据
        const baseData = await api.getBaseData(uid, token, batToken)
        if (baseData.code !== 200) return baseData

        const result = { baseData: baseData.data }

        if (includeDetails) {
          // 并行获取详细数据
          const [roleData, exploreData, challengeData] = await Promise.allSettled([
            api.getRoleData(uid, batToken),
            api.getExploreData(uid, batToken),
            api.getChallengeData(uid, batToken),
          ])

          if (roleData.status === "fulfilled" && roleData.value.code === 200) {
            result.roleData = roleData.value.data
          }
          if (exploreData.status === "fulfilled" && exploreData.value.code === 200) {
            result.exploreData = exploreData.value.data
          }
          if (challengeData.status === "fulfilled" && challengeData.value.code === 200) {
            result.challengeData = challengeData.value.data
          }
        }

        return { code: 200, data: result, msg: "获取游戏数据成功" }
      },
      "获取游戏数据",
    )
  }

  // ==================== 扩展API方法 ====================

  /**
   * 获取数据坞信息
   * @param {string} wavesId - 鸣潮UID
   * @param {string} bat - bat token
   * @returns {Promise<Object>} API响应
   */
  static async getCalabashData(wavesId, bat) {
    const api = new KuroApi()
    return await api.getCalabashData(wavesId, bat)
  }

  /**
   * 获取探索度数据
   * @param {string} wavesId - 鸣潮UID
   * @param {string} bat - bat token
   * @param {string} countryCode - 国家代码，默认为"1"
   * @returns {Promise<Object>} API响应
   */
  static async getExploreData(wavesId, bat, countryCode = "1") {
    const api = new KuroApi()
    return await api.getExploreData(wavesId, bat, countryCode)
  }

  /**
   * 获取全息战略数据
   * @param {string} wavesId - 鸣潮UID
   * @param {string} bat - bat token
   * @returns {Promise<Object>} API响应
   */
  static async getChallengeData(wavesId, bat) {
    const api = new KuroApi()
    return await api.getChallengeData(wavesId, bat)
  }

  /**
   * 获取深渊索引
   * @param {string} wavesId - 鸣潮UID
   * @param {string} bat - bat token
   * @returns {Promise<Object>} API响应
   */
  static async getTowerIndex(wavesId, bat) {
    const api = new KuroApi()
    return await api.getTowerIndex(wavesId, bat)
  }

  /**
   * 获取深渊详情
   * @param {string} wavesId - 鸣潮UID
   * @param {string} bat - bat token
   * @returns {Promise<Object>} API响应
   */
  static async getTowerDetail(wavesId, bat) {
    const api = new KuroApi()
    return await api.getTowerDetail(wavesId, bat)
  }

  /**
   * 获取冥海索引
   * @param {string} wavesId - 鸣潮UID
   * @param {string} bat - bat token
   * @returns {Promise<Object>} API响应
   */
  static async getSlashIndex(wavesId, bat) {
    const api = new KuroApi()
    return await api.getSlashIndex(wavesId, bat)
  }

  /**
   * 获取冥海详情
   * @param {string} wavesId - 鸣潮UID
   * @param {string} bat - bat token
   * @returns {Promise<Object>} API响应
   */
  static async getSlashDetail(wavesId, bat) {
    const api = new KuroApi()
    return await api.getSlashDetail(wavesId, bat)
  }

  /**
   * 获取更多活动数据（浸梦海床+激斗！向着荣耀之丘）
   * @param {string} wavesId - 鸣潮UID
   * @param {string} bat - bat token
   * @returns {Promise<Object>} API响应
   */
  static async getMoreActivity(wavesId, bat) {
    const api = new KuroApi()
    return await api.getMoreActivity(wavesId, bat)
  }

  /**
   * 获取每日数据
   * @param {string} wavesId - 鸣潮UID
   * @param {string} token - 用户token
   * @returns {Promise<Object>} API响应
   */
  static async getDailyInfo(wavesId, token) {
    const api = new KuroApi()
    return await api.getDailyInfo(wavesId, token)
  }

  /**
   * 获取抽卡记录
   * @param {string} cardPoolType - 卡池类型
   * @param {string} recordId - 记录ID
   * @param {string} wavesId - 鸣潮UID
   * @returns {Promise<Object>} API响应
   */
  static async getGachaLog(cardPoolType, recordId, wavesId) {
    const api = new KuroApi()
    return await api.getGachaLog(cardPoolType, recordId, wavesId)
  }

  // ==================== 计算器相关API ====================

  /**
   * 获取在线角色列表
   * @param {string} token - 用户token
   * @returns {Promise<Object>} API响应
   */
  static async getOnlineListRole(token) {
    const api = new KuroApi()
    return await api.getOnlineListRole(token)
  }

  /**
   * 获取在线武器列表
   * @param {string} token - 用户token
   * @returns {Promise<Object>} API响应
   */
  static async getOnlineListWeapon(token) {
    const api = new KuroApi()
    return await api.getOnlineListWeapon(token)
  }

  /**
   * 获取在线声骸列表
   * @param {string} token - 用户token
   * @returns {Promise<Object>} API响应
   */
  static async getOnlineListPhantom(token) {
    const api = new KuroApi()
    return await api.getOnlineListPhantom(token)
  }

  /**
   * 获取已拥有角色
   * @param {string} wavesId - 鸣潮UID
   * @param {string} token - 用户token
   * @returns {Promise<Object>} API响应
   */
  static async getOwnedRole(wavesId, token) {
    const api = new KuroApi()
    return await api.getOwnedRole(wavesId, token)
  }

  /**
   * 获取角色培养状态
   * @param {string} wavesId - 鸣潮UID
   * @param {string} token - 用户token
   * @param {Array<string>} charIds - 角色ID列表
   * @returns {Promise<Object>} API响应
   */
  static async getRoleCultivateStatus(wavesId, token, charIds) {
    const api = new KuroApi()
    return await api.getRoleCultivateStatus(wavesId, token, charIds)
  }

  /**
   * 获取批量角色成本
   * @param {string} wavesId - 鸣潮UID
   * @param {string} token - 用户token
   * @param {Array} content - 计算内容
   * @returns {Promise<Object>} API响应
   */
  static async getBatchRoleCost(wavesId, token, content) {
    const api = new KuroApi()
    return await api.getBatchRoleCost(wavesId, token, content)
  }

  // ==================== 资源简报相关API ====================

  /**
   * 获取资源简报列表
   * @param {string} wavesId - 鸣潮UID
   * @param {string} token - 用户token
   * @returns {Promise<Object>} API响应
   */
  static async getPeriodList(wavesId, token) {
    const api = new KuroApi()
    return await api.getPeriodList(wavesId, token)
  }

  /**
   * 获取资源简报详情
   * @param {string} type - 类型 (month/week/version)
   * @param {string|number} period - 期数
   * @param {string} wavesId - 鸣潮UID
   * @param {string} token - 用户token
   * @returns {Promise<Object>} API响应
   */
  static async getPeriodDetail(type, period, wavesId, token) {
    const api = new KuroApi()
    return await api.getPeriodDetail(type, period, wavesId, token)
  }
}

/**
 * 数据库操作
 */
export class Database {
  /**
   * 获取WavesUser类的引用
   * @returns {WavesUser} WavesUser类
   */
  static get WavesUser() {
    return WavesUser
  }

  /**
   * 获取WavesBind类的引用
   * @returns {WavesBind} WavesBind类
   */
  static get WavesBind() {
    return WavesBind
  }
}

/**
 * 配置管理
 */
export class Config {
  /**
   * 获取配置管理器实例
   * @returns {ConfigManager} 配置管理器
   */
  static getInstance() {
    return config
  }

  /**
   * 获取配置值
   * @param {string} key - 配置键
   * @param {*} defaultValue - 默认值
   * @returns {*} 配置值
   */
  static get(key, defaultValue = null) {
    return config.get(key, defaultValue)
  }

  /**
   * 生成命令正则表达式
   * @param {string} command - 命令名称
   * @returns {string} 正则表达式字符串
   */
  static generateCommandRegex(command) {
    return config.generateCommandRegex(command)
  }

  /**
   * 获取命令示例
   * @param {string} command - 命令名称
   * @returns {string} 带前缀的命令示例
   */
  static getCommandExample(command) {
    return config.getCommandExample(command)
  }
}

/**
 * 统一API接口类 - 提供所有API的统一调用入口
 * 外部插件推荐使用这个类来调用所有API
 */
export class RoverAPI {
  /**
   * 检查用户绑定状态
   */
  static async checkUserBinding(userId) {
    return await UserBinding.checkUserBinding(userId)
  }

  static async isUserBound(userId) {
    return await UserBinding.isUserBound(userId)
  }

  /**
   * 获取用户基础信息
   */
  static async getUserUid(userId) {
    return await UserBinding.getUserUid(userId)
  }

  static async getUserToken(userId) {
    return await UserBinding.getUserToken(userId)
  }

  static async getUserBatToken(userId) {
    return await UserBinding.getUserBatToken(userId)
  }

  /**
   * 数据更新接口 - 供外部插件调用
   */
  static async refreshUserPanel(userId) {
    return await KuroAPI.refreshUserPanel(userId)
  }

  static async getUserGameData(userId, includeDetails = false) {
    return await KuroAPI.getUserGameData(userId, includeDetails)
  }

  /**
   * 智能API调用方法 - 自动处理token刷新
   */

  // 获取基础数据
  static async getBaseData(userId) {
    return await KuroAPI._smartApiCall(
      userId,
      async (uid, token, did, batToken, api) => {
        return await api.getBaseData(uid, token, batToken)
      },
      "获取基础数据",
    )
  }

  // 获取角色数据
  static async getRoleData(userId) {
    return await KuroAPI._smartApiCall(
      userId,
      async (uid, token, did, batToken, api) => {
        return await api.getRoleData(uid, batToken)
      },
      "获取角色数据",
    )
  }

  // 获取探索数据
  static async getExploreData(userId) {
    return await KuroAPI._smartApiCall(
      userId,
      async (uid, token, did, batToken, api) => {
        return await api.getExploreData(uid, batToken)
      },
      "获取探索数据",
    )
  }

  // 获取挑战数据
  static async getChallengeData(userId) {
    return await KuroAPI._smartApiCall(
      userId,
      async (uid, token, did, batToken, api) => {
        return await api.getChallengeData(uid, batToken)
      },
      "获取挑战数据",
    )
  }

  // 获取角色列表
  static async getRoleList(userId) {
    return await KuroAPI._smartApiCall(
      userId,
      async (uid, token, did, batToken, api) => {
        return await api.getRoleList(token, did)
      },
      "获取角色列表",
    )
  }

  /**
   * 基础游戏数据API
   */
  static async getRoleList(token, did) {
    return await KuroAPI.getRoleList(token, did)
  }

  static async getBaseData(uid, token, batToken) {
    return await KuroAPI.getBaseData(uid, token, batToken)
  }

  static async getRoleData(uid, batToken) {
    return await KuroAPI.getRoleData(uid, batToken)
  }

  static async getRoleDetail(uid, roleId, batToken) {
    return await KuroAPI.getRoleDetail(uid, roleId, batToken)
  }

  /**
   * 游戏内容数据API
   */
  static async getCalabashData(uid, batToken) {
    return await KuroAPI.getCalabashData(uid, batToken)
  }

  static async getExploreData(uid, batToken) {
    return await KuroAPI.getExploreData(uid, batToken)
  }

  static async getChallengeData(uid, batToken) {
    return await KuroAPI.getChallengeData(uid, batToken)
  }

  static async getTowerIndex(uid, batToken) {
    return await KuroAPI.getTowerIndex(uid, batToken)
  }

  static async getTowerDetail(uid, batToken) {
    return await KuroAPI.getTowerDetail(uid, batToken)
  }

  static async getSlashIndex(uid, batToken) {
    return await KuroAPI.getSlashIndex(uid, batToken)
  }

  static async getSlashDetail(uid, batToken) {
    return await KuroAPI.getSlashDetail(uid, batToken)
  }

  static async getMoreActivity(uid, batToken) {
    return await KuroAPI.getMoreActivity(uid, batToken)
  }

  static async getDailyInfo(uid, token) {
    return await KuroAPI.getDailyInfo(uid, token)
  }

  /**
   * 抽卡记录API
   */
  static async getGachaLog(cardPoolType, recordId, uid) {
    return await KuroAPI.getGachaLog(cardPoolType, recordId, uid)
  }

  /**
   * 计算器功能API
   */
  static async getOnlineListRole(token) {
    return await KuroAPI.getOnlineListRole(token)
  }

  static async getOnlineListWeapon(token) {
    return await KuroAPI.getOnlineListWeapon(token)
  }

  static async getOnlineListPhantom(token) {
    return await KuroAPI.getOnlineListPhantom(token)
  }

  static async getOwnedRole(uid, token) {
    return await KuroAPI.getOwnedRole(uid, token)
  }

  static async getRoleCultivateStatus(uid, token, charIds) {
    return await KuroAPI.getRoleCultivateStatus(uid, token, charIds)
  }

  static async getBatchRoleCost(uid, token, content) {
    return await KuroAPI.getBatchRoleCost(uid, token, content)
  }

  /**
   * 资源简报API
   */
  static async getPeriodList(uid, token) {
    return await KuroAPI.getPeriodList(uid, token)
  }

  static async getPeriodDetail(type, period, uid, token) {
    return await KuroAPI.getPeriodDetail(type, period, uid, token)
  }

  /**
   * 数据库操作API
   */
  static async getUserAccount(userId) {
    return await Database.WavesUser.getPrimaryAccount(userId)
  }

  static async getBindUid(userId) {
    return await Database.WavesBind.getUidByUser(userId)
  }

  /**
   * 高级组合API - 获取用户完整数据
   */
  static async getUserCompleteData(userId) {
    try {
      // 1. 检查绑定状态
      const bindingInfo = await this.checkUserBinding(userId)
      if (!bindingInfo.hasValidAccount) {
        return {
          success: false,
          message: "用户未绑定有效账号",
          code: "NO_BINDING",
        }
      }

      const { uid, token, batToken } = bindingInfo

      // 2. 并行获取所有数据
      const [baseData, roleData, calabashData, exploreData, towerIndex, slashIndex] =
        await Promise.allSettled([
          this.getBaseData(uid, token, batToken),
          this.getRoleData(uid, batToken),
          this.getCalabashData(uid, batToken),
          this.getExploreData(uid, batToken),
          this.getTowerIndex(uid, batToken),
          this.getSlashIndex(uid, batToken),
        ])

      return {
        success: true,
        message: "获取完整数据成功",
        code: "SUCCESS",
        data: {
          uid,
          baseData: baseData.status === "fulfilled" ? baseData.value : null,
          roleData: roleData.status === "fulfilled" ? roleData.value : null,
          calabashData: calabashData.status === "fulfilled" ? calabashData.value : null,
          exploreData: exploreData.status === "fulfilled" ? exploreData.value : null,
          towerIndex: towerIndex.status === "fulfilled" ? towerIndex.value : null,
          slashIndex: slashIndex.status === "fulfilled" ? slashIndex.value : null,
        },
      }
    } catch (error) {
      console.error("获取用户完整数据失败:", error)
      return {
        success: false,
        message: "获取数据时发生错误",
        code: "ERROR",
        error: error.message,
      }
    }
  }

  /**
   * 高级组合API - 获取计算器相关数据
   */
  static async getCalculatorData(userId) {
    try {
      // 1. 检查绑定状态
      const bindingInfo = await this.checkUserBinding(userId)
      if (!bindingInfo.hasValidAccount) {
        return {
          success: false,
          message: "用户未绑定有效账号",
          code: "NO_BINDING",
        }
      }

      const { uid, token } = bindingInfo

      // 2. 并行获取计算器数据
      const [onlineRoles, onlineWeapons, onlinePhantoms, ownedRoles] = await Promise.allSettled([
        this.getOnlineListRole(token),
        this.getOnlineListWeapon(token),
        this.getOnlineListPhantom(token),
        this.getOwnedRole(uid, token),
      ])

      return {
        success: true,
        message: "获取计算器数据成功",
        code: "SUCCESS",
        data: {
          uid,
          onlineRoles: onlineRoles.status === "fulfilled" ? onlineRoles.value : null,
          onlineWeapons: onlineWeapons.status === "fulfilled" ? onlineWeapons.value : null,
          onlinePhantoms: onlinePhantoms.status === "fulfilled" ? onlinePhantoms.value : null,
          ownedRoles: ownedRoles.status === "fulfilled" ? ownedRoles.value : null,
        },
      }
    } catch (error) {
      console.error("获取计算器数据失败:", error)
      return {
        success: false,
        message: "获取数据时发生错误",
        code: "ERROR",
        error: error.message,
      }
    }
  }

  /**
   * 高级组合API - 获取资源简报数据
   */
  static async getResourceReports(userId) {
    try {
      // 1. 检查绑定状态
      const bindingInfo = await this.checkUserBinding(userId)
      if (!bindingInfo.hasValidAccount) {
        return {
          success: false,
          message: "用户未绑定有效账号",
          code: "NO_BINDING",
        }
      }

      const { uid, token } = bindingInfo

      // 2. 获取简报列表
      const periodList = await this.getPeriodList(uid, token)
      if (periodList?.code !== 200) {
        return {
          success: false,
          message: "获取简报列表失败",
          code: "API_ERROR",
        }
      }

      // 3. 获取最新的各类简报
      const periods = periodList.data
      const latestMonth = periods.find(p => p.type === "month")
      const latestWeek = periods.find(p => p.type === "week")
      const latestVersion = periods.find(p => p.type === "version")

      const reports = {}

      if (latestMonth) {
        const monthDetail = await this.getPeriodDetail("month", latestMonth.index, uid, token)
        reports.monthDetail = monthDetail?.code === 200 ? monthDetail.data : null
      }

      if (latestWeek) {
        const weekDetail = await this.getPeriodDetail("week", latestWeek.index, uid, token)
        reports.weekDetail = weekDetail?.code === 200 ? weekDetail.data : null
      }

      if (latestVersion) {
        const versionDetail = await this.getPeriodDetail("version", latestVersion.index, uid, token)
        reports.versionDetail = versionDetail?.code === 200 ? versionDetail.data : null
      }

      return {
        success: true,
        message: "获取资源简报成功",
        code: "SUCCESS",
        data: {
          uid,
          periodList: periods,
          ...reports,
        },
      }
    } catch (error) {
      console.error("获取资源简报失败:", error)
      return {
        success: false,
        message: "获取数据时发生错误",
        code: "ERROR",
        error: error.message,
      }
    }
  }
}

// 默认导出所有API
export default {
  // 统一API接口（推荐使用）
  RoverAPI,

  // 分类API接口
  UserBinding,
  KuroAPI,
  Database,
  Config,

  // 直接导出类的引用，方便使用
  User,
  WavesUser,
  WavesBind,
  KuroApi,
  config,
}
