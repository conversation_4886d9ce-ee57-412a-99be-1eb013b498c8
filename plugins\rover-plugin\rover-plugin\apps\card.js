import plugin from "../../../lib/plugins/plugin.js"
import RoleDetail from "../model/roleDetail.js"
import { DataProcessor } from "../utils/dataProcessor.js"
import { ImageManager } from "../utils/imageManager.js"
import { aliasToCharName } from "../components/common/alias.js"
import { getIdByName, getNameById, getAllNames } from "../components/common/id2name.js"
import User from "../components/User.js"
import config from "../components/config.js"
import { CharacterCache } from "../components/cache.js"
import Background from "../components/Background.js"
import { renderer } from "../utils/renderer.js"
import { DEFAULT_CONFIG, TEMPLATE_PATHS, DISPLAY_MODES } from "../utils/constants.js"

// 创建实例
const dataProcessor = new DataProcessor()
const imageMapper = new ImageManager()

export class Card extends plugin {
  constructor() {
    super({
      name: "鸣潮-角色面板",
      event: "message",
      priority: 500,
      rule: [
        {
          reg: config.generateCommandRegex("(.+)面板"),
          fnc: "characterPanel",
        },
        {
          reg: config.generateCommandRegex("面板"),
          fnc: "defaultPanel",
        },
      ],
    })
  }

  // 查看指定角色面板
  async characterPanel(e) {
    try {
      // 使用动态正则提取角色名称，正确处理前缀
      const prefixes = config.getAllPrefixes()
      const separator = config.getCommandSeparator()
      const allowEmpty = config.isEmptyPrefixAllowed()

      let regexParts = []
      if (prefixes.length > 0) {
        const escapedPrefixes = prefixes.map(prefix => config.escapeRegex(prefix))
        regexParts.push(`(${escapedPrefixes.join("|")})${config.escapeRegex(separator)}`)
      }
      if (allowEmpty) {
        regexParts.push("")
      }

      const prefixPattern = regexParts.length > 1 ? `(${regexParts.join("|")})` : regexParts[0]
      const regex = new RegExp(`^${prefixPattern}(.+)面板$`)
      const match = e.msg.match(regex)
      const inputName = match && match[match.length - 1] ? match[match.length - 1].trim() : ""

      if (!inputName) {
        return e.reply(
          `请输入角色名称，如：${config.getCommandExample("维拉面板")} 或 ${config.getCommandExample("长离面板")}`,
        )
      }

      // 排除刷新和更新命令，这些由 refresh.js 处理
      if (inputName.includes("刷新") || inputName.includes("更新")) {
        return false // 让 refresh.js 处理
      }

      await e.reply("🔄 正在获取角色数据并处理图片，请稍候...")

      // 名字转换为charId
      const charName = aliasToCharName(inputName)
      const charId = getIdByName(charName)

      if (!charId) {
        // 获取可用角色列表
        const availableChars = getAllNames().slice(0, 10).join("、")
        return e.reply(
          `❌ 未找到角色：${inputName}\n\n可用角色（部分）：${availableChars}\n\n💡 请使用 ${config.getCommandExample("刷新面板")} 更新角色数据`,
        )
      }

      console.log(`[Rover Plugin] 角色查询: ${inputName} -> ${charName} -> ${charId}`)

      // 获取用户信息
      const user = User.fromEvent(e)
      if (!user) {
        await e.reply("❌ 获取用户信息失败")
        return false
      }

      // 优化：一次性获取所有用户数据，避免多次数据库查询
      const userData = await user.getAllUserData()
      const { uid, token, did } = userData

      if (!uid) {
        await e.reply(
          `❌ 您还未绑定UID\n请先使用：${config.getCommandExample("绑定uid 你的UID")}\n或使用：${config.getCommandExample("添加token token,did")}`,
        )
        return false
      }

      if (!token) {
        await e.reply(
          `❌ 您还未绑定token\n请先使用：${config.getCommandExample("添加token token,did")}`,
        )
        return false
      }

      if (!did) {
        await e.reply(
          `❌ 缺少设备ID\n请重新使用：${config.getCommandExample("添加token token,did")}`,
        )
        return false
      }

      // 获取角色详细数据（仅从缓存）
      const characterData = await this.getCharacterData(uid, charId)

      if (!characterData) {
        await e.reply(
          `❌ 未找到角色"${charName}"的数据\n请先使用 ${config.getCommandExample("刷新面板")} 获取角色数据`,
        )
        return false
      }

      // 生成角色面板图片
      const panelImage = await this.generateCharacterPanel(e, characterData, charName, uid)

      if (panelImage) {
        await e.reply(panelImage)
      } else {
        await e.reply("❌ 生成角色面板失败")
      }
    } catch (error) {
      console.error(`[Rover Plugin] 角色面板查询失败:`, error)
      await e.reply("❌ 角色面板查询失败，请稍后重试")
    }
  }

  // 查看所有角色面板列表
  async defaultPanel(e) {
    try {
      await e.reply("🔄 正在获取角色面板列表，请稍候...")

      // 获取用户信息
      const user = User.fromEvent(e)
      if (!user) {
        await e.reply("❌ 获取用户信息失败")
        return false
      }

      // 优化：一次性获取所有用户数据
      const userData = await user.getAllUserData()
      const { uid } = userData

      if (!uid) {
        await e.reply(
          `❌ 您还未绑定UID\n请先使用：${config.getCommandExample("绑定uid 你的UID")}\n或使用：${config.getCommandExample("添加token token,did")}`,
        )
        return false
      }

      // 获取所有角色数据
      const roleDetailList = CharacterCache.getRoleDetailList(uid)
      if (!roleDetailList || !Array.isArray(roleDetailList) || roleDetailList.length === 0) {
        await e.reply(
          `❌ 未找到角色数据\n请先使用 ${config.getCommandExample("刷新面板")} 获取角色数据`,
        )
        return false
      }

      // 渲染角色面板列表
      await this.renderCharacterList(e, uid, roleDetailList)
    } catch (error) {
      console.error(`[Rover Plugin] 角色面板列表查询失败:`, error)
      await e.reply("❌ 面板查询失败，请稍后重试")
    }
  }

  // 渲染角色列表
  async renderCharacterList(e, uid, roleDetailList) {
    try {
      // 先检查所有角色的头像是否存在
      const existingImageMap = new Map()
      roleDetailList.forEach(roleDetail => {
        const role = roleDetail.role || roleDetail
        if (role.roleIconUrl) {
          const existingPath = imageMapper.checkLocalImageExists(role.roleIconUrl)
          if (existingPath) {
            existingImageMap.set(role.roleId, {
              roleId: role.roleId,
              localPath: existingPath,
              originalUrl: role.roleIconUrl,
            })
          }
        }
      })

      // 处理角色数据，转换为模板需要的格式
      const chars = roleDetailList.map((roleDetail, index) => {
        const role = roleDetail.role || roleDetail
        const roleName = role.roleName || "未知角色"

        // 从已存在的图片中获取头像路径
        let faceImage = "/character-images/default-avatar.webp"
        const imageResult = existingImageMap.get(role.roleId)
        if (
          imageResult &&
          imageResult.localPath &&
          imageResult.localPath !== imageResult.originalUrl
        ) {
          faceImage = imageMapper.getLocalPath(imageResult.originalUrl)
        }

        return {
          id: role.roleId || index,
          name: roleName,
          abbr: roleName,
          star: role.starLevel || 5,
          level: role.level || 1,
          cons: role.chainUnlockNum || 0,
          face: faceImage,
          isNew: false, // 面板列表不显示新角色标记
          isUpdate: false,
          roleId: role.roleId,
        }
      })

      // 对角色进行排序：五星优先，然后按等级排序
      chars.sort((a, b) => {
        // 首先按星级排序（五星在前）
        if (a.star !== b.star) {
          return b.star - a.star
        }

        // 然后按等级排序（高等级在前）
        return b.level - a.level
      })

      // 获取背景图
      const background = await Background.getBackground("list")

      // 渲染数据 - 遵循统一规范
      const renderData = {
        // 必需字段
        uid,
        game: "ww",

        // 角色列表特有字段
        chars,
        servName: "库街区",
        hasNew: false,
        msg: `共 ${chars.length} 个角色`,
        groupRank: false,
        allowRank: false,
        rankCfg: { limitTxt: `使用 ${config.getCommandExample("角色名面板")} 查看详细面板` },

        // 可选字段
        element: DEFAULT_CONFIG.ELEMENT, // 默认元素，用于主题色
        background: background?.text,
        updateTime: { profile: new Date().toLocaleString() },
      }

      console.log(`[角色列表] 渲染数据准备完成:`, {
        uid: renderData.uid,
        charCount: chars.length,
        background: renderData.background,
      })

      // 渲染图片
      // 使用统一渲染工具
      const img = await renderer.render(e, TEMPLATE_PATHS.CHARACTER_LIST, renderData)

      if (img) {
        await e.reply(img)
      }
      // 渲染失败时不发送文字消息
    } catch (error) {
      console.error("渲染角色列表失败:", error)
      // 渲染失败时不发送文字消息
    }
  }

  // 获取角色数据（仅从缓存）
  async getCharacterData(uid, charId) {
    try {
      // 只从项目缓存获取角色详情列表
      const roleDetailList = CharacterCache.getRoleDetailList(uid)
      if (!roleDetailList || roleDetailList.length === 0) {
        console.log(`[Rover Plugin] 缓存为空，请先刷新面板`)
        return null
      }

      // 在角色详情列表中查找指定角色
      const targetCharId = parseInt(charId)
      const roleDetail = roleDetailList.find(role => {
        // 检查role对象内的roleId字段
        const roleIdToCheck = role.role?.roleId
        return roleIdToCheck === targetCharId || roleIdToCheck === charId
      })

      if (roleDetail) {
        console.log(
          `[Rover Plugin] 使用缓存的角色数据: ${charId} (${roleDetail.role?.roleName || "未知角色"})`,
        )
        return roleDetail
      } else {
        console.log(
          `[Rover Plugin] 缓存中未找到角色: ${charId}，可用角色: ${roleDetailList.map(r => r.role?.roleId).join(", ")}`,
        )
        return null
      }
    } catch (error) {
      console.error(`[Rover Plugin] 获取角色数据失败:`, error)
      return null
    }
  }

  // 获取当前主角色数据
  async getCurrentCharacterData(uid) {
    try {
      // 从项目缓存获取角色详情列表
      const roleDetailList = CharacterCache.getRoleDetailList(uid)

      if (roleDetailList && roleDetailList.length > 0) {
        // 返回第一个角色
        return roleDetailList[0]
      }

      return null
    } catch (error) {
      console.error(`[Rover Plugin] 获取当前角色数据失败:`, error)
      return null
    }
  }

  // 根据角色ID获取角色名称
  getCharacterNameById(charId) {
    try {
      const name = getNameById(charId)
      return name !== charId.toString() ? name : "未知角色"
    } catch (error) {
      console.error(`[Rover Plugin] 获取角色名称失败:`, error)
      return "未知角色"
    }
  }

  // 生成角色面板图片
  async generateCharacterPanel(e, characterData, charName, uid) {
    try {
      // 处理数据
      const processedData = await dataProcessor.processCharacterData(
        characterData,
        characterData.role?.roleId,
      )

      // 创建角色详情模型
      const roleDetail = new RoleDetail(processedData)

      // 映射图片路径
      const renderData = imageMapper.mapRoleDetailImages(characterData.roleId, roleDetail)

      // 添加额外的渲染数据 - 遵循统一规范
      renderData.charName = charName
      renderData.uid = uid // 用户UID
      renderData.element = processedData.element || DEFAULT_CONFIG.ELEMENT // 默认元素
      renderData.displayMode = DISPLAY_MODES.DEFAULT // 默认显示模式
      renderData.saveTime = new Date().toLocaleString()

      console.log(`[角色面板] 渲染数据准备完成:`, {
        charName: renderData.charName,
        uid: renderData.uid,
        element: renderData.element,
        displayMode: renderData.displayMode,
      })

      // 使用统一渲染工具渲染角色面板
      const img = await renderer.render(e, TEMPLATE_PATHS.CHARACTER_DETAIL, renderData)

      return img
    } catch (error) {
      console.error(`[Rover Plugin] 生成角色面板失败:`, error)
      return null
    }
  }
}
