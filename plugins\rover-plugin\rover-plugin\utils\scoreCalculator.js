import { getTotalScoreBackground } from "../components/common/calc.js"

/**
 * 统一的评分计算工具类
 * 提供所有功能模块使用的评分计算方法
 */
export class ScoreCalculator {
  constructor() {
    // 评分等级列表（与现有面板保持一致）
    this.scoreIntervals = ["C", "B", "A", "S", "SS", "SSS"]
    
    // 评分基准值（用于计算评分比例）
    this.scoreBase = 250
  }

  /**
   * 计算角色总评分
   * @param {Object} roleDetail - 角色详情对象
   * @returns {number} 总评分
   */
  calculateTotalScore(roleDetail) {
    if (!roleDetail || !roleDetail.equipPhantomList) {
      return 0
    }

    // 计算所有声骸评分的总和
    return roleDetail.equipPhantomList.reduce(
      (sum, phantom) => sum + (phantom?.score?.[0] || 0),
      0
    )
  }

  /**
   * 根据总分获取评级
   * @param {number} totalScore - 总分
   * @param {Object} calcFile - 角色计算文件
   * @returns {string} 评级 (C/B/A/S/SS/SSS)
   */
  getScoreGrade(totalScore, calcFile) {
    return getTotalScoreBackground(totalScore, calcFile) || 'C'
  }

  /**
   * 计算评分比例
   * @param {number} totalScore - 总分
   * @returns {number} 评分比例 (0-1)
   */
  getScoreRatio(totalScore) {
    return Math.max(0, Math.min(1, totalScore / this.scoreBase))
  }

  /**
   * 获取评级对应的数值索引
   * @param {string} grade - 评级
   * @returns {number} 索引值 (0-5)
   */
  getGradeIndex(grade) {
    const index = this.scoreIntervals.indexOf(grade)
    return index >= 0 ? index : 0
  }

  /**
   * 根据索引获取评级
   * @param {number} index - 索引值 (0-5)
   * @returns {string} 评级
   */
  getGradeByIndex(index) {
    return this.scoreIntervals[Math.max(0, Math.min(5, index))] || 'C'
  }

  /**
   * 获取评级的颜色样式类名
   * @param {string} grade - 评级
   * @returns {string} CSS类名
   */
  getGradeColorClass(grade) {
    const gradeColorMap = {
      'SSS': 'grade-sss',
      'SS': 'grade-ss', 
      'S': 'grade-s',
      'A': 'grade-a',
      'B': 'grade-b',
      'C': 'grade-c'
    }
    return gradeColorMap[grade] || 'grade-c'
  }

  /**
   * 获取评级的背景样式类名
   * @param {string} grade - 评级
   * @returns {string} CSS类名
   */
  getGradeBackgroundClass(grade) {
    const gradeBackgroundMap = {
      'SSS': 'bg-sss',
      'SS': 'bg-ss',
      'S': 'bg-s', 
      'A': 'bg-a',
      'B': 'bg-b',
      'C': 'bg-c'
    }
    return gradeBackgroundMap[grade] || 'bg-c'
  }

  /**
   * 格式化分数显示
   * @param {number} score - 分数
   * @param {number} decimals - 小数位数，默认2位
   * @returns {string} 格式化后的分数
   */
  formatScore(score, decimals = 2) {
    if (typeof score !== 'number' || isNaN(score)) {
      return '0.00'
    }
    return score.toFixed(decimals)
  }

  /**
   * 计算等级分布统计
   * @param {Array} scoreList - 分数列表，每个元素包含 {totalScore, calcFile}
   * @returns {Object} 等级分布统计
   */
  calculateGradeDistribution(scoreList) {
    const distribution = {
      SSS: 0, SS: 0, S: 0, A: 0, B: 0, C: 0
    }

    scoreList.forEach(item => {
      const grade = this.getScoreGrade(item.totalScore, item.calcFile)
      if (distribution.hasOwnProperty(grade)) {
        distribution[grade]++
      }
    })

    return distribution
  }

  /**
   * 计算平均分
   * @param {Array} scores - 分数数组
   * @returns {number} 平均分
   */
  calculateAverageScore(scores) {
    if (!scores || scores.length === 0) {
      return 0
    }
    
    const validScores = scores.filter(score => typeof score === 'number' && !isNaN(score))
    if (validScores.length === 0) {
      return 0
    }
    
    const sum = validScores.reduce((total, score) => total + score, 0)
    return Math.round((sum / validScores.length) * 100) / 100
  }

  /**
   * 获取评级描述文本
   * @param {string} grade - 评级
   * @returns {string} 描述文本
   */
  getGradeDescription(grade) {
    const descriptions = {
      'SSS': '完美',
      'SS': '优秀',
      'S': '良好',
      'A': '中等',
      'B': '一般',
      'C': '较差'
    }
    return descriptions[grade] || '未知'
  }

  /**
   * 判断是否为高分
   * @param {string} grade - 评级
   * @returns {boolean} 是否为高分 (S级及以上)
   */
  isHighScore(grade) {
    return ['SSS', 'SS', 'S'].includes(grade)
  }

  /**
   * 获取所有可用的评级列表
   * @returns {Array} 评级列表
   */
  getAllGrades() {
    return [...this.scoreIntervals]
  }

  /**
   * 验证评级是否有效
   * @param {string} grade - 评级
   * @returns {boolean} 是否有效
   */
  isValidGrade(grade) {
    return this.scoreIntervals.includes(grade)
  }
}

// 创建单例实例
export const scoreCalculator = new ScoreCalculator()

// 导出便捷方法
export const {
  calculateTotalScore,
  getScoreGrade,
  getScoreRatio,
  getGradeIndex,
  getGradeByIndex,
  getGradeColorClass,
  getGradeBackgroundClass,
  formatScore,
  calculateGradeDistribution,
  calculateAverageScore,
  getGradeDescription,
  isHighScore,
  getAllGrades,
  isValidGrade
} = scoreCalculator
