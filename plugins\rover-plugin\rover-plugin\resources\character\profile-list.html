<!DOCTYPE html>
<html lang="zh-cn">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width">
  <link rel="shortcut icon" href="#"/>
  <link rel="preload" href="{{_res_path}}/common/font/HYWH-65W.woff" as="font" type="font/woff">
  <link rel="preload" href="{{_res_path}}/common/font/NZBZ.woff" as="font" type="font/woff">
  <link rel="preload" href="{{_res_path}}/common/font/tttgbnumber.woff" as="font" type="font/woff">
  <link rel="stylesheet" type="text/css" href="{{_res_path}}/common/common.css"/>
  <link rel="stylesheet" type="text/css" href="{{_res_path}}/character/profile-detail.css"/>
  <link rel="stylesheet" type="text/css" href="{{_res_path}}/character/profile-list.css"/>
  {{if background}}{{@background}}{{/if}}
  <title>鸣潮角色列表</title>
</head>
{{set elemCls = {火:'pyro',冰:'cryo',风:'anemo',雷:'electro',量子:'quantum',虚数:'geo',物理:'sr', }[element||elem] || element || elem || 'hydro' }}
<body class="elem-{{elemCls}} {{displayMode || mode || `default`}}-mode {{bodyClass}}" {{sys.scale}}>
<div class="container elem-bg" id="container">
{{set demo = chars[0]?.abbr || "长离" }}
<div class="profile-cont game-{{game}}">
  <div class="head-box">
    <div class="title">#鸣潮面板列表
      <div class="label">UID:{{uid}}</div>
    </div>
    {{if msg}}
    <div class="label">{{msg}}</div>
    {{/if}}
    <div class="label">你可以使用<span>#mc{{demo}}面板</span>、<span>#鸣潮{{demo}}面板</span>命令来查看面板信息了</div>
  </div>
  <div class="cont group-rank-tip {{groupRank?'has-rank':'no-rank'}}">
    <div class="cont-title">
      {{if !allowRank}}
      <span> <i class="group-rank-icon dmg-icon"></i>本面板暂未参与排名，参与要求：{{rankCfg.limitTxt}} </span>
      {{else}}
      <span>
      <i class="group-rank-icon dmg-icon"></i>综合练度排名
      <i class="group-rank-icon mark-icon"></i>声骸评分排名
    </span>
      <span class="rank-time">
      排名：本群内 {{rankCfg.time}} 后，通过 #鸣潮刷新面板 命令查看过的角色数据
    </span>
      {{/if}}
    </div>
  </div>
  <div class="cont {{groupRank?'has-rank':'no-rank'}}">
    <div class="char-list">
      {{each chars char}}
      <div class="char-item {{char.isUpdate&&hasNew?'new-char update-char':char.isNew&&hasNew?'new-char':''}}">
        <div class="item-icon char-icon star{{char.star}}">
       <span class="img"
             style="background-image:url('{{_res_path}}{{char.face}}')"></span>
        </div>
        <span class="name">{{char.abbr}}<span class="cons cons-{{char.cons}}">{{char.cons}}</span></span>
        {{if char.groupRank}}
        {{set gr = char.groupRank}}
        {{set rank = gr.rank >= (rankCfg.number || 15) ? 10:(gr.rank <=3 ? gr.rank : 4)}}
        <div class="group-rank rank-{{rank}} rank-type-{{gr.rankType}}">
          <span>{{gr.rank}}</span>
        </div>
        {{/if}}
      </div>
      {{/each}}
    </div>
    {{if hasNew}}
    <div class="cont-footer" data-list-2>
      <span class="new-tip">本次获取角色</span>
      <span class="new-tip update-tip">本次更新角色</span>
    {{else}}
    <div class="cont-footer" data-list-1>
      <span>{{if updateTime.profile }} 更新时间：{{updateTime.profile }} {{/if}}</span>
    {{/if}}
      <span class="serv">
      当前更新服务：{{servName}}
      </span>
    </div>
  </div>
</div>
  <div class="copyright">{{@copyright || sys?.copyright}}</div>
</div>
</body>
</html>
