# 🌊 Rover Plugin - 鸣潮插件

基于WutheringWavesUID的完整API实现，为 Yunzai-Bot 开发的高性能鸣潮游戏插件。

## ✨ 功能特性

- 🎮 **角色面板查询** - 查看角色详细属性、武器、声骸等信息
- 🌐 **网页端登录** - 支持网页端登录，更安全便捷的登录方式
- 🔐 **库街区登录** - 支持验证码登录库街区，自动获取角色信息
- 👤 **多角色管理** - 支持多角色绑定和切换，便捷管理多个游戏账号
- 🖼️ **图片资源管理** - 自动下载和缓存游戏资源图片
- 📊 **数据处理** - 完整的角色数据模型和处理逻辑
- 🎨 **美观界面** - 使用HTML/CSS模板生成精美的角色面板
- 🔄 **API集成** - 基于库街区官方API，提供稳定的数据获取
- ⚡ **高性能缓存** - 永久缓存机制，显著提升响应速度（30个角色3.2秒完成）
- 🌐 **代理支持** - 支持代理访问，解决网络问题
- 🔧 **并发控制** - 智能并发管理，稳定可靠
- 🏆 **排行榜功能** - 全服和群内排行榜
- 🎁 **兑换码查询** - 自动获取最新可用兑换码

## 🚀 安装方法

### 系统要求
- Node.js >= 16.0.0
- Yunzai-Bot v3.0+
- SQLite3 数据库支持

### 安装步骤

1. **克隆插件**
   ```bash
   # 进入Yunzai-Bot插件目录
   cd Yunzai-Bot/plugins

   # 克隆插件
   git clone https://用户名:令牌@gitcode.com/m0_69204072/rover-plugin.git
   ```

2. **安装依赖**
   ```bash
   # 进入插件目录
   cd rover-plugin

   # 安装依赖
   pnpm install

   # 安装网页登录功能（可选）
   node install-web-login.js
   ```




## 使用说明

> **📝 命令前缀说明**
> 插件支持可配置的命令前缀，默认支持：`mc`、`鸣潮`、`ww`
> 可在 `config/command.yaml` 中自定义前缀，以下示例使用默认前缀

### 网页登录（最推荐）

最安全便捷的登录方式，登录成功后自动保存到数据库：

```
#鸣潮登录                   # 获取登录链接，登录后自动完成绑定
```

**本地模式配置：**
- 插件自动启动本地登录服务器
- 默认端口：19075
- 默认地址：http://127.0.0.1:19075/login

**内网穿透配置：**
如果需要通过公网访问（如使用ngrok等内网穿透工具），请在配置文件中设置：

```yaml
webLogin:
  local:
    loginUrl: "https://your-tunnel-domain.com"  # 你的公网地址
```

支持的URL格式：
- `https://domain.com`
- `http://domain.com:port`
- `https://subdomain.ngrok.io`

**远程模式配置：**
- 使用外部登录服务器
- 需要在配置文件中设置远程URL



### 验证码登录（推荐）



### Token绑定（推荐）

类似WutheringWavesUID的使用方式：

```
#mc添加token token,did    # 添加token和设备码
#mc刷新面板               # 获取所有角色信息
#mc角色名面板             # 查看指定角色（如：ww长离面板）
```

### 绑定管理（手动）

也可以手动绑定UID和token：

```
#mc绑定uid 你的UID        # 绑定鸣潮UID（9位数字）
#mc绑定token 你的token    # 绑定访问token
#mc我的绑定               # 查看绑定信息
#mc解绑                   # 解除所有绑定
```

### UID管理

支持多UID绑定和管理：

```
#mc查看                   # 查看所有绑定的UID
#mc切换 123456789         # 切换到指定UID（后续操作使用此UID）
#mc删除 123456789         # 删除指定UID及其数据
#mc删除全部uid            # 删除所有绑定的UID
#mc删除token              # 删除当前UID的token
#mc删除token 123456789    # 删除指定UID的token
```

### 功能命令

```
#mc刷新面板               # 获取所有角色信息（必须先执行）
#mc角色名面板             # 查看指定角色面板（如：ww长离面板）
#mc面板                   # 查看当前角色面板
#mc面板 1205             # 查看指定角色面板
#mc长离权重               # 查看指定角色权重分析
#mc兑换码                 # 获取最新可用兑换码
#mc帮助                   # 查看帮助信息
```

### 获取Token方法（手动绑定时需要）

### 命令前缀配置

插件支持灵活的命令前缀配置，可在 `config/command.yaml` 中自定义：

```yaml
command:
  # 默认前缀列表
  defaultPrefixes:
    - "mc"
    - "鸣潮"
    - "ww"

  # 自定义前缀列表 (可添加多个)
  customPrefixes:
    - "rover"
    - "鸣"

  # 是否禁用默认前缀 (true: 仅使用自定义前缀)
  disableDefaultPrefixes: false

  # 是否允许空前缀 (true: 无需前缀即可触发)
  allowEmptyPrefix: false
```

**配置说明：**
- `customPrefixes`：自定义前缀，优先级最高
- `disableDefaultPrefixes`：设为 `true` 可禁用默认前缀，仅使用自定义前缀
- `allowEmptyPrefix`：设为 `true` 可允许无前缀命令（如直接发送"面板"）

## 项目结构

```
rover-plugin/
├── apps/                # 功能模块
│   ├── card.js         # 角色面板功能
│   ├── bind.js         # 绑定管理功能
│   ├── login.js        # 登录管理功能
│   ├── refresh.js      # 面板刷新功能
│   ├── charPanel.js    # 角色面板查询功能
│   ├── weightQuery.js  # 权重查询功能
│   ├── redeemCode.js   # 兑换码查询功能
│   └── help.js         # 帮助功能
├── components/          # 核心组件
│   ├── User.js         # 用户数据管理
│   ├── kuroapi.js      # 库街区API接口
│   ├── constant.js     # 常量配置
│   └── path.js         # 路径管理
├── model/              # 数据模型
│   └── roleDetail.js   # 角色详情模型
├── utils/              # 工具类
│   ├── dataProcessor.js    # 数据处理器
│   ├── imageDownloader.js  # 图片下载工具
│   ├── imageMapper.js      # 图片路径映射
│   └── network.js          # 网络工具
├── resources/          # 资源文件
│   └── character/      # 角色相关资源
└── data/               # 数据存储目录
    └── users/          # 用户数据存储
```

## 技术特点

- **ES模块化设计** - 使用现代JavaScript模块系统
- **库街区API集成** - 基于官方API文档实现完整的接口调用
- **多角色管理** - 支持一个用户绑定多个游戏角色
- **用户数据管理** - 基于YAML的用户数据持久化存储
- **验证码登录** - 安全便捷的库街区登录方式
- **图片处理** - 自动下载和缓存游戏资源图片
- **模板渲染** - 使用HTML/CSS模板生成美观的角色面板

## 注意事项

- Token具有时效性，过期后需要重新绑定
- 请勿将token分享给他人
- 建议定期备份用户数据文件

## 更新日志

### v2.1.0
- 🎉 新增WutheringWavesUID风格功能
- ✨ 支持 ww添加token token,did 绑定方式
- ✨ 新增 ww刷新面板 获取所有角色信息
- ✨ 支持 ww角色名面板 查询具体角色
- 🖼️ 角色列表图片展示功能
- 📝 完善角色名匹配和别名支持

### v2.0.0
- 🎉 重大更新：集成库街区官方API
- ✨ 新增验证码登录功能
- ✨ 支持多角色管理和切换
- ✨ 自动获取用户绑定的所有角色
- 🔄 优化数据获取流程，提供更稳定的服务
- 📝 完善帮助文档和使用说明
- 🛡️ 提升安全性，支持官方登录方式

### v1.1.0
- ✨ 新增用户绑定系统
- ✨ 支持个性化UID和token管理
- ✨ 添加帮助文档和命令
- 🐛 修复硬编码数据问题
- 📝 完善错误提示信息

### v1.0.0
- 🎉 初始版本发布
- ✨ 基础角色面板查询功能

## 贡献

欢迎提交Issue和Pull Request来改进这个插件！

## 许可证

MIT License
