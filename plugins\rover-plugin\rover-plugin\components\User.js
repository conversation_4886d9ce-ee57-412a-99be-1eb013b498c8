import { WavesUser, WavesBind } from "./db/database.js"

// 用户数据管理类
export class User {
  constructor(userId, botId = "default") {
    this.userId = userId
    this.botId = botId
    this._userDataCache = null // 缓存用户数据，避免重复查询
    this._cacheExpiry = 0 // 缓存过期时间
    this._cacheTTL = 30000 // 缓存30秒
  }

  // 获取用户数据（带缓存）
  async _getUserData(forceRefresh = false) {
    const now = Date.now()

    // 如果缓存有效且不强制刷新，返回缓存数据
    if (!forceRefresh && this._userDataCache && now < this._cacheExpiry) {
      return this._userDataCache
    }

    try {
      // 优先使用 user_id 查询，兼容迁移数据（bot_id 可能不匹配）
      const [bindRecord, userRecord] = await Promise.all([
        this._getBindRecordByUserId().catch(() => null),
        this._getUserRecordByUserId()
          .then(accounts => accounts[0] || null)
          .catch(() => null),
      ])

      // 缓存数据
      this._userDataCache = {
        bindRecord,
        userRecord,
        uid: bindRecord || userRecord?.uid || null,
        token: userRecord?.cookie || null,
        did: userRecord?.did || null,
        batToken: userRecord?.bat || null,
      }
      this._cacheExpiry = now + this._cacheTTL

      return this._userDataCache
    } catch (error) {
      console.error("获取用户数据失败:", error)
      return {
        bindRecord: null,
        userRecord: null,
        uid: null,
        token: null,
        did: null,
        batToken: null,
      }
    }
  }

  // 优先使用 user_id 查询绑定记录（兼容迁移数据）
  async _getBindRecordByUserId() {
    try {
      // 先尝试精确匹配 user_id 和 bot_id
      let bindRecord = await WavesBind.getUidByUser(this.userId, this.botId)

      if (!bindRecord) {
        // 如果没有找到，尝试只用 user_id 查询（兼容迁移数据）
        const allBinds = await WavesBind.findAll()
        const userBind = allBinds.find(bind => bind.user_id === this.userId)
        bindRecord = userBind ? userBind.uid : null

        if (bindRecord) {
          console.log(`🔄 使用 user_id 查询到绑定记录: ${this.userId} -> ${bindRecord}`)
        }
      }

      return bindRecord
    } catch (error) {
      console.error("查询绑定记录失败:", error)
      return null
    }
  }

  // 优先使用 user_id 查询用户记录（兼容迁移数据）
  async _getUserRecordByUserId() {
    try {
      // 先尝试精确匹配 user_id 和 bot_id
      let accounts = await WavesUser.getUserAccounts(this.userId, this.botId)

      if (!accounts || accounts.length === 0) {
        // 如果没有找到，尝试只用 user_id 查询（兼容迁移数据）
        const allUsers = await WavesUser.findAll()
        accounts = allUsers.filter(user => user.user_id === this.userId)

        if (accounts.length > 0) {
          console.log(`🔄 使用 user_id 查询到用户记录: ${this.userId} -> ${accounts.length} 个账号`)
        }
      }

      return accounts
    } catch (error) {
      console.error("查询用户记录失败:", error)
      return []
    }
  }

  // 获取用户当前绑定的UID（优化：使用缓存）
  async getUid() {
    const userData = await this._getUserData()
    return userData.uid
  }

  // 获取用户token（优化：使用缓存）
  async getToken() {
    const userData = await this._getUserData()
    return userData.token
  }

  // 获取用户DID（优化：使用缓存）
  async getDid() {
    const userData = await this._getUserData()
    return userData.did
  }

  // 获取用户bat token（优化：使用缓存）
  async getBatToken() {
    const userData = await this._getUserData()
    return userData.batToken
  }

  // 批量获取用户所有数据（新增：一次性获取所有数据）
  async getAllUserData() {
    return await this._getUserData()
  }

  // 清除缓存（在数据更新后调用）
  _clearCache() {
    this._userDataCache = null
    this._cacheExpiry = 0
  }

  // 绑定UID
  async bindUid(uid) {
    try {
      // 1. 创建或更新 WavesUser 记录
      const userData = {
        bot_id: this.botId,
        user_id: this.userId,
        uid: uid,
        cookie: "",
        did: "",
        platform: "ios",
        push_switch: "off",
        sign_switch: "off",
        stamina_bg_value: "0",
        bbs_sign_switch: "off",
        bat: "",
        status: "",
      }

      await WavesUser.createOrUpdate(userData)

      // 2. 更新或创建 WavesBind 记录
      const result = await WavesBind.insertWavesUid(this.userId, this.botId, uid)

      // 清除缓存，确保下次获取最新数据
      this._clearCache()

      return result === 0 || result === -2 // 0=成功, -2=已绑定
    } catch (error) {
      console.error("绑定UID失败:", error)
      return false
    }
  }

  // 绑定token
  async bindToken(token) {
    try {
      // 获取现有用户数据或创建默认数据
      const accounts = await WavesUser.getUserAccounts(this.userId, this.botId)
      const existingAccount = accounts[0]

      const userData = {
        bot_id: this.botId,
        user_id: this.userId,
        uid: existingAccount?.uid || "",
        cookie: token,
        did: existingAccount?.did || "",
        platform: "ios",
        push_switch: "off",
        sign_switch: "off",
        stamina_bg_value: "0",
        bbs_sign_switch: "off",
        bat: existingAccount?.bat || "",
        status: "",
      }

      await WavesUser.createOrUpdate(userData)

      // 清除缓存
      this._clearCache()

      return true
    } catch (error) {
      console.error("绑定Token失败:", error)
      return false
    }
  }

  // 绑定DID
  async bindDid(did) {
    try {
      // 获取现有用户数据或创建默认数据
      const accounts = await WavesUser.getUserAccounts(this.userId, this.botId)
      const existingAccount = accounts[0]

      const userData = {
        bot_id: this.botId,
        user_id: this.userId,
        uid: existingAccount?.uid || "",
        cookie: existingAccount?.cookie || "",
        did: did,
        platform: "ios",
        push_switch: "off",
        sign_switch: "off",
        stamina_bg_value: "0",
        bbs_sign_switch: "off",
        bat: existingAccount?.bat || "",
        status: "",
      }

      await WavesUser.createOrUpdate(userData)

      // 清除缓存
      this._clearCache()

      return true
    } catch (error) {
      console.error("绑定DID失败:", error)
      return false
    }
  }

  // 更新bat token
  async updateBatToken(batToken) {
    try {
      // 获取现有用户数据
      const accounts = await WavesUser.getUserAccounts(this.userId, this.botId)
      const existingAccount = accounts[0]

      if (existingAccount) {
        const userData = {
          ...existingAccount.dataValues,
          bat: batToken,
        }

        await WavesUser.createOrUpdate(userData)
        // 清除缓存
        this._clearCache()
        return true
      }

      return false
    } catch (error) {
      console.error("更新Bat Token失败:", error)
      return false
    }
  }

  // 检查是否有UID
  async hasUid() {
    const uid = await this.getUid()
    return !!uid
  }

  // 检查是否有token
  async hasToken() {
    const token = await this.getToken()
    return !!token
  }

  // 解绑所有信息
  async unbind() {
    try {
      // 删除 WavesUser 记录
      await WavesUser.destroy({
        where: {
          user_id: this.userId,
        },
      })

      // 删除 WavesBind 记录
      await WavesBind.destroy({
        where: {
          user_id: this.userId,
        },
      })

      // 清除缓存
      this._clearCache()

      return true
    } catch (error) {
      console.error("解绑失败:", error)
      return false
    }
  }

  // 获取用户信息（优化：使用缓存）
  async getUserInfo() {
    try {
      const userData = await this._getUserData()

      return {
        userId: this.userId,
        uid: userData.uid,
        serverId: userData.bindRecord?.server_id || null,
        hasToken: !!userData.token,
        isLoggedIn: !!(userData.token && userData.uid),
        userName: userData.userRecord?.user_name || null,
        roleCount: 1, // 简化处理
        bindTime: userData.bindRecord?.created_at || null,
        tokenBindTime: userData.userRecord?.updated_at || null,
      }
    } catch (error) {
      console.error("获取用户信息失败:", error)
      return {
        userId: this.userId,
        uid: null,
        serverId: null,
        hasToken: false,
        isLoggedIn: false,
        userName: null,
        roleCount: 0,
        bindTime: null,
        tokenBindTime: null,
      }
    }
  }

  // 保存登录信息（修复版：确保相同用户更新而不是创建新记录）
  async saveLoginInfo(loginInfo) {
    try {
      const { token, did, bat, roles } = loginInfo

      // 验证必需的参数
      if (!token) {
        console.error("❌ 保存登录信息失败: token为空")
        return false
      }

      if (!did) {
        console.error("❌ 保存登录信息失败: did为空")
        return false
      }

      console.log(`💾 保存登录信息: 用户=${this.userId}, Bot=${this.botId}`)
      console.log(`💾 Token: ${token.substring(0, 20)}...`)
      console.log(`💾 DID: ${did}`)
      console.log(`💾 角色数量: ${roles ? roles.length : 0}`)

      // 准备要保存的数据
      const userData = {
        bot_id: this.botId,
        user_id: this.userId,
        uid: roles && roles.length > 0 ? roles[0].roleId : "",
        cookie: token,
        did: did,
        platform: "ios",
        push_switch: "off",
        sign_switch: "off",
        stamina_bg_value: "0",
        bbs_sign_switch: "off",
        bat: bat || "",
        status: "", // 重置状态为有效
      }

      // 注意：user_name 字段在当前数据库表中不存在，暂时不保存用户名

      // 使用 createOrUpdate 方法处理用户数据
      await WavesUser.createOrUpdate(userData)
      console.log(`✅ 用户登录信息保存成功`)

      // 清除缓存
      this._clearCache()

      console.log(`✅ 登录信息保存完成`)
      return true
    } catch (error) {
      console.error("❌ 保存登录信息失败:", error)
      console.error("❌ 错误详情:", error.stack)
      return false
    }
  }

  // 获取登录信息
  getLoginInfo() {
    // 这里应该从数据库获取，暂时返回null
    return null
  }

  // 绑定服务器ID
  bindServerId(_serverId) {
    // 暂时简化处理，参数前加下划线表示未使用
    return true
  }

  // 获取用户绑定的所有UID列表（兼容迁移数据）
  async getAllUids() {
    try {
      // 先尝试精确匹配
      let uids = await WavesBind.getAllUidsByUser(this.userId, this.botId)

      if (!uids || uids.length === 0) {
        // 如果没有找到，尝试只用 user_id 查询（兼容迁移数据）
        const allBinds = await WavesBind.findAll()
        const userBind = allBinds.find(bind => bind.user_id === this.userId)

        if (userBind && userBind.uid) {
          uids = userBind.uid.split("_").filter(Boolean)
          console.log(`🔄 使用 user_id 查询到UID列表: ${this.userId} -> [${uids.join(", ")}]`)
        } else {
          uids = []
        }
      }

      return uids
    } catch (error) {
      console.error("获取所有UID失败:", error)
      return []
    }
  }

  // 获取用户的所有账号信息（兼容迁移数据）
  async getAllAccounts() {
    try {
      // 先尝试精确匹配
      let accounts = await WavesUser.getUserAccounts(this.userId, this.botId)

      if (!accounts || accounts.length === 0) {
        // 如果没有找到，尝试只用 user_id 查询（兼容迁移数据）
        const allUsers = await WavesUser.findAll()
        accounts = allUsers.filter(user => user.user_id === this.userId)

        if (accounts.length > 0) {
          console.log(`🔄 使用 user_id 查询到账号信息: ${this.userId} -> ${accounts.length} 个账号`)
        }
      }

      return accounts
    } catch (error) {
      console.error("获取所有账号失败:", error)
      return []
    }
  }

  // 获取主账号信息
  async getPrimaryAccount() {
    try {
      return await WavesUser.getPrimaryAccount(this.userId, this.botId)
    } catch (error) {
      console.error("获取主账号失败:", error)
      return null
    }
  }

  // 切换到指定UID（使用新的多账号支持）
  async switchToUid(targetUid) {
    try {
      const success = await WavesBind.setDefaultUid(this.userId, this.botId, targetUid)

      if (success) {
        // 清除缓存
        this._clearCache()
        return { success: true, message: `已切换到UID: ${targetUid}` }
      } else {
        return { success: false, message: "指定的UID不存在或切换失败" }
      }
    } catch (error) {
      console.error("切换UID失败:", error)
      return { success: false, message: "切换失败" }
    }
  }

  // 删除指定UID
  async deleteUid(targetUid) {
    try {
      const allUids = await this.getAllUids()

      if (!allUids.includes(targetUid)) {
        return { success: false, message: "该UID未绑定" }
      }

      // 使用新的多账号支持方法删除账号
      const result = await this.removeAccountByUid(targetUid)

      if (!result.success) {
        return { success: false, message: result.error || "删除失败" }
      }

      // 清除缓存
      this._clearCache()

      return { success: true, message: "删除成功" }
    } catch (error) {
      console.error("删除UID失败:", error)
      return { success: false, message: "删除失败" }
    }
  }

  // 删除所有UID
  async deleteAllUids() {
    try {
      // 获取所有UID并逐个删除
      const allUids = await this.getAllUids()

      for (const uid of allUids) {
        await this.removeAccountByUid(uid)
      }

      // 清除缓存
      this._clearCache()

      return { success: true, message: "删除所有UID成功" }
    } catch (error) {
      console.error("删除所有UID失败:", error)
      return { success: false, message: "删除失败" }
    }
  }

  // 删除指定UID的token（如果不指定UID则删除当前UID的token）
  async deleteToken(targetUid = null) {
    try {
      let uidToDelete = targetUid

      if (!uidToDelete) {
        // 如果没有指定UID，使用当前绑定的UID
        uidToDelete = await this.getUid()
        if (!uidToDelete) {
          return { success: false, message: "当前未绑定任何UID" }
        }
      } else {
        // 如果指定了UID，检查是否已绑定
        const allUids = await this.getAllUids()
        if (!allUids.includes(uidToDelete)) {
          return { success: false, message: "指定的UID未绑定" }
        }
      }

      // 删除对应的WavesUser记录（包含token信息）
      const deletedCount = await WavesUser.destroy({
        where: {
          user_id: this.userId,
          uid: uidToDelete,
        },
      })

      if (deletedCount > 0) {
        // 清除缓存
        this._clearCache()
        return { success: true, message: `已删除UID ${uidToDelete} 的token` }
      } else {
        return { success: false, message: "未找到对应的token记录" }
      }
    } catch (error) {
      console.error("删除token失败:", error)
      return { success: false, message: "删除token失败" }
    }
  }

  // 清除缓存
  _clearCache() {
    this._cachedUserData = null
    this._cachedUid = null
    this._cachedToken = null
    this._cachedDid = null
  }

  // 添加新账号（改进版：支持多账号和数据一致性）
  async addNewAccount(accountData) {
    try {
      const { uid, token, did, bat = "" } = accountData

      // 验证必需参数
      if (!uid || !token || !did) {
        return { success: false, error: "缺少必需的账号信息" }
      }

      // 创建或更新用户记录
      const userData = {
        user_id: this.userId,
        bot_id: this.botId,
        uid: uid,
        cookie: token,
        did: did,
        bat: bat,
        status: "",
      }

      const user = await WavesUser.createOrUpdate(userData)

      // 添加UID绑定
      const bindResult = await WavesBind.insertWavesUid(this.userId, this.botId, uid)

      // 清除缓存
      this._clearCache()

      if (bindResult === 0) {
        return {
          success: true,
          message: "账号添加成功",
          user: user,
          isNew: true,
        }
      } else if (bindResult === -2) {
        return {
          success: true,
          message: "账号已存在，信息已更新",
          user: user,
          isNew: false,
        }
      } else {
        return { success: false, error: "绑定UID失败" }
      }
    } catch (error) {
      console.error("添加账号失败:", error)
      return { success: false, error: error.message }
    }
  }

  // 删除账号（改进版：使用事务确保数据一致性）
  async removeAccountByUid(uid) {
    try {
      const result = await WavesUser.removeAccount(this.userId, this.botId, uid)

      if (result.success) {
        // 清除缓存
        this._clearCache()
      }

      return result
    } catch (error) {
      console.error("删除账号失败:", error)
      return { success: false, error: error.message }
    }
  }

  // 获取账号统计信息
  async getAccountStats() {
    try {
      const accounts = await this.getAllAccounts()
      const uids = await this.getAllUids()

      return {
        totalAccounts: accounts.length,
        totalUids: uids.length,
        accounts: accounts.map(account => ({
          uid: account.uid,
          status: account.status || "正常",
          createdAt: account.created_at,
          updatedAt: account.updated_at,
        })),
        uids: uids,
      }
    } catch (error) {
      console.error("获取账号统计失败:", error)
      return {
        totalAccounts: 0,
        totalUids: 0,
        accounts: [],
        uids: [],
      }
    }
  }

  // 静态方法：从事件创建User实例
  static fromEvent(e) {
    if (!e || !e.user_id) {
      return null
    }

    const botId = e.bot?.uin || e.self_id || "default"
    return new User(e.user_id.toString(), botId.toString())
  }
}

export default User
