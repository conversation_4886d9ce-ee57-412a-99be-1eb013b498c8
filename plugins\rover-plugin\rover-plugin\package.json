{"name": "rover-plugin", "type": "module", "version": "2.2.0", "description": "鸣潮游戏插件，支持库街区登录和角色面板查询，基于WutheringWavesUID的完整API实现", "main": "index.js", "keywords": ["yunzai", "wuthering-waves", "kuro", "gaming"], "author": "tyql688", "license": "MIT", "repository": {"type": "git", "url": "https://gitcode.com/m0_69204072/rover-plugin.git"}, "bugs": {"url": "https://gitcode.com/m0_69204072/rover-plugin/issues"}, "homepage": "https://gitcode.com/m0_69204072/rover-plugin#readme", "dependencies": {"axios": "^1.10.0", "express": "^4.18.2", "jsdom": "^26.1.0", "ky": "^1.8.1", "socks-proxy-agent": "^8.0.5", "undici": "^7.11.0"}}