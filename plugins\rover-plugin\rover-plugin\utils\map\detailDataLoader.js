import fs from "fs"
import path from "path"
import { pluginPath } from "../../components/path.js"

/**
 * 详细数据加载器
 * 用于加载 detail_json 目录中的游戏详细数据
 */
export class DetailDataLoader {
  constructor() {
    this.detailJsonPath = path.join(pluginPath, "utils/map/detail_json")
    this.cache = new Map()
  }

  /**
   * 获取角色详细数据
   * @param {number|string} charId - 角色ID
   * @returns {Object|null} 角色详细数据
   */
  getCharacterDetail(charId) {
    const cacheKey = `char_${charId}`
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)
    }

    try {
      const filePath = path.join(this.detailJsonPath, "char", `${charId}.json`)
      
      if (!fs.existsSync(filePath)) {
        console.warn(`角色详细数据文件不存在: ${charId}`)
        return null
      }

      const data = JSON.parse(fs.readFileSync(filePath, "utf8"))
      this.cache.set(cacheKey, data)
      return data
    } catch (error) {
      console.error(`读取角色详细数据失败: ${charId}`, error)
      return null
    }
  }

  /**
   * 获取声骸详细数据
   * @param {number|string} echoId - 声骸ID
   * @returns {Object|null} 声骸详细数据
   */
  getEchoDetail(echoId) {
    const cacheKey = `echo_${echoId}`
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)
    }

    try {
      const filePath = path.join(this.detailJsonPath, "echo", `${echoId}.json`)
      
      if (!fs.existsSync(filePath)) {
        console.warn(`声骸详细数据文件不存在: ${echoId}`)
        return null
      }

      const data = JSON.parse(fs.readFileSync(filePath, "utf8"))
      this.cache.set(cacheKey, data)
      return data
    } catch (error) {
      console.error(`读取声骸详细数据失败: ${echoId}`, error)
      return null
    }
  }

  /**
   * 获取武器详细数据
   * @param {number|string} weaponId - 武器ID
   * @returns {Object|null} 武器详细数据
   */
  getWeaponDetail(weaponId) {
    const cacheKey = `weapon_${weaponId}`
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)
    }

    try {
      const filePath = path.join(this.detailJsonPath, "weapon", `${weaponId}.json`)
      
      if (!fs.existsSync(filePath)) {
        console.warn(`武器详细数据文件不存在: ${weaponId}`)
        return null
      }

      const data = JSON.parse(fs.readFileSync(filePath, "utf8"))
      this.cache.set(cacheKey, data)
      return data
    } catch (error) {
      console.error(`读取武器详细数据失败: ${weaponId}`, error)
      return null
    }
  }

  /**
   * 获取套装详细数据
   * @param {string} sonataName - 套装名称
   * @returns {Object|null} 套装详细数据
   */
  getSonataDetail(sonataName) {
    const cacheKey = `sonata_${sonataName}`
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)
    }

    try {
      const filePath = path.join(this.detailJsonPath, "sonata", `${sonataName}.json`)
      
      if (!fs.existsSync(filePath)) {
        console.warn(`套装详细数据文件不存在: ${sonataName}`)
        return null
      }

      const data = JSON.parse(fs.readFileSync(filePath, "utf8"))
      this.cache.set(cacheKey, data)
      return data
    } catch (error) {
      console.error(`读取套装详细数据失败: ${sonataName}`, error)
      return null
    }
  }

  /**
   * 获取材料详细数据
   * @param {number|string} materialId - 材料ID
   * @returns {Object|null} 材料详细数据
   */
  getMaterialDetail(materialId) {
    const cacheKey = `material_${materialId}`
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)
    }

    try {
      const filePath = path.join(this.detailJsonPath, "material", `${materialId}.json`)
      
      if (!fs.existsSync(filePath)) {
        console.warn(`材料详细数据文件不存在: ${materialId}`)
        return null
      }

      const data = JSON.parse(fs.readFileSync(filePath, "utf8"))
      this.cache.set(cacheKey, data)
      return data
    } catch (error) {
      console.error(`读取材料详细数据失败: ${materialId}`, error)
      return null
    }
  }

  /**
   * 获取所有可用的角色ID列表
   * @returns {Array} 角色ID列表
   */
  getAvailableCharacterIds() {
    try {
      const charDir = path.join(this.detailJsonPath, "char")
      const files = fs.readdirSync(charDir)
      
      return files
        .filter(file => file.endsWith(".json"))
        .map(file => file.replace(".json", ""))
        .map(id => parseInt(id))
        .sort((a, b) => a - b)
    } catch (error) {
      console.error("获取角色ID列表失败:", error)
      return []
    }
  }

  /**
   * 获取所有可用的声骸ID列表
   * @returns {Array} 声骸ID列表
   */
  getAvailableEchoIds() {
    try {
      const echoDir = path.join(this.detailJsonPath, "echo")
      const files = fs.readdirSync(echoDir)
      
      return files
        .filter(file => file.endsWith(".json"))
        .map(file => file.replace(".json", ""))
        .sort()
    } catch (error) {
      console.error("获取声骸ID列表失败:", error)
      return []
    }
  }

  /**
   * 获取所有可用的套装名称列表
   * @returns {Array} 套装名称列表
   */
  getAvailableSonataNames() {
    try {
      const sonataDir = path.join(this.detailJsonPath, "sonata")
      const files = fs.readdirSync(sonataDir)
      
      return files
        .filter(file => file.endsWith(".json"))
        .map(file => file.replace(".json", ""))
        .sort()
    } catch (error) {
      console.error("获取套装名称列表失败:", error)
      return []
    }
  }

  /**
   * 清理缓存
   */
  clearCache() {
    this.cache.clear()
    console.log("详细数据缓存已清理")
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计
   */
  getCacheStats() {
    const stats = {
      totalCached: this.cache.size,
      characters: 0,
      echoes: 0,
      weapons: 0,
      sonatas: 0,
      materials: 0
    }

    for (const key of this.cache.keys()) {
      if (key.startsWith("char_")) stats.characters++
      else if (key.startsWith("echo_")) stats.echoes++
      else if (key.startsWith("weapon_")) stats.weapons++
      else if (key.startsWith("sonata_")) stats.sonatas++
      else if (key.startsWith("material_")) stats.materials++
    }

    return stats
  }

  /**
   * 批量预加载数据
   * @param {Object} options - 预加载选项
   * @param {Array} options.characterIds - 要预加载的角色ID列表
   * @param {Array} options.echoIds - 要预加载的声骸ID列表
   * @param {Array} options.weaponIds - 要预加载的武器ID列表
   */
  preloadData(options = {}) {
    const { characterIds = [], echoIds = [], weaponIds = [] } = options
    
    console.log("开始预加载详细数据...")
    
    let loaded = 0
    
    // 预加载角色数据
    for (const charId of characterIds) {
      if (this.getCharacterDetail(charId)) {
        loaded++
      }
    }
    
    // 预加载声骸数据
    for (const echoId of echoIds) {
      if (this.getEchoDetail(echoId)) {
        loaded++
      }
    }
    
    // 预加载武器数据
    for (const weaponId of weaponIds) {
      if (this.getWeaponDetail(weaponId)) {
        loaded++
      }
    }
    
    console.log(`预加载完成，共加载 ${loaded} 个数据文件`)
  }
}

// 创建单例实例
export const detailDataLoader = new DetailDataLoader()
export default detailDataLoader
