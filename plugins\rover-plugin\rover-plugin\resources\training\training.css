/* 基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Microsoft YaHei", "PingFang SC", "Helvetica Neue", Aria<PERSON>, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
  line-height: 1.6;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
}

/* 头部样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 10px;
  color: white;
}

.title h1 {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 5px;
}

.uid {
  font-size: 14px;
  opacity: 0.9;
}

.summary {
  display: flex;
  gap: 20px;
}

.summary-item {
  text-align: center;
}

.summary-item .label {
  font-size: 12px;
  opacity: 0.8;
  margin-bottom: 5px;
}

.summary-item .value {
  font-size: 18px;
  font-weight: bold;
}

/* 等级统计样式 */
.grade-stats {
  margin-bottom: 30px;
}

.grade-stats h2 {
  font-size: 20px;
  margin-bottom: 15px;
  color: #333;
}

.grade-list {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.grade-item {
  padding: 10px 15px;
  border-radius: 8px;
  text-align: center;
  min-width: 60px;
  color: white;
  font-weight: bold;
}

.grade-item .grade-name {
  font-size: 14px;
  margin-bottom: 5px;
}

.grade-item .grade-count {
  font-size: 18px;
}

/* 等级颜色 */
.grade-sss { background: linear-gradient(135deg, #ff6b6b, #ee5a24); }
.grade-ss { background: linear-gradient(135deg, #feca57, #ff9ff3); }
.grade-s { background: linear-gradient(135deg, #48dbfb, #0abde3); }
.grade-a { background: linear-gradient(135deg, #1dd1a1, #10ac84); }
.grade-b { background: linear-gradient(135deg, #ffeaa7, #fdcb6e); }
.grade-c { background: linear-gradient(135deg, #fd79a8, #e84393); }
.grade-d { background: linear-gradient(135deg, #a29bfe, #6c5ce7); }

/* 角色列表样式 */
.character-list {
  margin-bottom: 30px;
}

.character-list h2 {
  font-size: 20px;
  margin-bottom: 15px;
  color: #333;
}

.list-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.character-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 10px;
  background: white;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.character-item:hover {
  transform: translateY(-2px);
}

.rank {
  font-size: 18px;
  font-weight: bold;
  margin-right: 15px;
  min-width: 30px;
  text-align: center;
}

.medal-gold { color: #ffd700; }
.medal-silver { color: #c0c0c0; }
.medal-bronze { color: #cd7f32; }
.medal-normal { color: #666; }

.character-avatar {
  position: relative;
  margin-right: 15px;
}

.character-avatar img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 3px solid #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.character-avatar.large img {
  width: 80px;
  height: 80px;
}

.star-level {
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: #ffd700;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: bold;
}

.character-info {
  flex: 1;
  margin-right: 15px;
}

.character-info .name {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 5px;
}

.character-info .details {
  font-size: 12px;
  color: #666;
}

.character-info .details span {
  margin-right: 10px;
  padding: 2px 6px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.weapon-info {
  margin-right: 15px;
  text-align: center;
}

.weapon-info img {
  width: 40px;
  height: 40px;
  border-radius: 5px;
  margin-bottom: 5px;
}

.weapon-level {
  font-size: 10px;
  color: #666;
}

.score-info {
  text-align: center;
}

.score-info .score {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 5px;
}

.score-info .grade {
  padding: 4px 8px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: bold;
  color: white;
}

/* 角色详情页面样式 */
.character-detail .header {
  flex-direction: column;
  align-items: flex-start;
}

.character-main {
  display: flex;
  align-items: center;
  width: 100%;
  margin-bottom: 15px;
}

.character-main .character-info {
  margin-left: 20px;
  flex: 1;
}

.character-main h1 {
  font-size: 32px;
  margin-bottom: 10px;
}

.basic-info {
  margin-bottom: 15px;
}

.basic-info span {
  margin-right: 15px;
  padding: 5px 10px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  font-size: 14px;
}

.total-score {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  padding: 10px 20px;
  border-radius: 20px;
  color: white;
  font-weight: bold;
}

.total-score .score {
  font-size: 24px;
}

.total-score .grade {
  font-size: 16px;
}

/* 武器、技能、声骸等区域样式 */
.weapon-section,
.skills-section,
.phantoms-section,
.sets-section,
.attributes-section {
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.weapon-section h2,
.skills-section h2,
.phantoms-section h2,
.sets-section h2,
.attributes-section h2 {
  font-size: 18px;
  margin-bottom: 15px;
  color: #333;
  border-bottom: 2px solid #4facfe;
  padding-bottom: 5px;
}

.weapon-detail {
  display: flex;
  align-items: center;
  gap: 15px;
}

.weapon-detail img {
  width: 60px;
  height: 60px;
  border-radius: 8px;
}

.weapon-name {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 5px;
}

.weapon-stats span {
  margin-right: 10px;
  padding: 2px 6px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  font-size: 12px;
}

.skills-list {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.skill-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.05);
}

.skill-item img {
  width: 40px;
  height: 40px;
  border-radius: 5px;
}

.skill-name {
  font-size: 14px;
  font-weight: bold;
}

.skill-level {
  font-size: 12px;
  color: #666;
}

.skill-level.max-level {
  color: #ff6b6b;
  font-weight: bold;
}

/* 底部样式 */
.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30px;
  padding: 15px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  font-size: 12px;
  color: #666;
}

.watermark {
  font-weight: bold;
  color: #4facfe;
}

/* 声骸相关样式 */
.phantom-summary {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(79, 172, 254, 0.1);
  border-radius: 8px;
}

.phantom-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.phantom-item {
  padding: 15px;
  border-radius: 10px;
  background: white;
  border-left: 4px solid #ddd;
}

.phantom-item.grade-sss { border-left-color: #ff6b6b; }
.phantom-item.grade-ss { border-left-color: #feca57; }
.phantom-item.grade-s { border-left-color: #48dbfb; }
.phantom-item.grade-a { border-left-color: #1dd1a1; }
.phantom-item.grade-b { border-left-color: #ffeaa7; }
.phantom-item.grade-c { border-left-color: #fd79a8; }
.phantom-item.grade-d { border-left-color: #a29bfe; }

.phantom-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.phantom-header img {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  margin-right: 15px;
}

.phantom-info {
  flex: 1;
}

.phantom-name {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 5px;
}

.phantom-stats span {
  margin-right: 10px;
  padding: 2px 6px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  font-size: 12px;
}

.cost-1 { background: rgba(76, 175, 80, 0.2) !important; }
.cost-3 { background: rgba(33, 150, 243, 0.2) !important; }
.cost-4 { background: rgba(156, 39, 176, 0.2) !important; }

.phantom-score {
  text-align: center;
}

.phantom-score .score {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 5px;
}

.phantom-score .grade {
  padding: 4px 8px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: bold;
  color: white;
}

.phantom-props {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.main-props h4,
.sub-props h4 {
  font-size: 14px;
  margin-bottom: 10px;
  color: #666;
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
}

.prop-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 5px;
  border-radius: 5px;
  margin-bottom: 5px;
}

.prop-item img {
  width: 20px;
  height: 20px;
}

.prop-name {
  flex: 1;
  font-size: 12px;
}

.prop-value {
  font-weight: bold;
  font-size: 12px;
}

.prop-item.valid {
  background: rgba(76, 175, 80, 0.1);
  border-left: 3px solid #4caf50;
}

.prop-item.invalid {
  background: rgba(158, 158, 158, 0.1);
  border-left: 3px solid #9e9e9e;
}

.prop-item.max-value {
  background: rgba(255, 193, 7, 0.2);
  border-left: 3px solid #ffc107;
}

/* 套装样式 */
.sets-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.set-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 10px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
}

.set-item img {
  width: 40px;
  height: 40px;
  border-radius: 5px;
}

.set-name {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 2px;
}

.set-count {
  font-size: 12px;
  color: #4facfe;
  font-weight: bold;
  margin-bottom: 2px;
}

.set-description {
  font-size: 11px;
  color: #666;
  line-height: 1.4;
}

/* 属性统计样式 */
.attributes-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.attribute-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.05);
}

.attribute-item img {
  width: 30px;
  height: 30px;
}

.attribute-name {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.attribute-value {
  font-size: 14px;
  font-weight: bold;
}

.phantom-value {
  font-size: 10px;
  color: #4facfe;
}

.attribute-item.valid {
  border-left: 3px solid #4caf50;
}

.attribute-item.invalid {
  border-left: 3px solid #9e9e9e;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
  }

  .summary {
    margin-top: 15px;
  }

  .character-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .phantom-props {
    grid-template-columns: 1fr;
  }

  .attributes-list {
    grid-template-columns: 1fr;
  }
}
