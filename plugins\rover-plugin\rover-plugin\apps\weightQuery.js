import plugin from "../../../lib/plugins/plugin.js"
import RoleDetail from "../model/roleDetail.js"
import { DataProcessor } from "../utils/dataProcessor.js"
import { ImageManager } from "../utils/imageManager.js"
import { aliasToCharName } from "../components/common/alias.js"
import { getIdByName } from "../components/common/id2name.js"
import User from "../components/User.js"
import config from "../components/config.js"
import { CharacterCache } from "../components/cache.js"

import { renderer } from "../utils/renderer.js"
import { ScoreCalculator } from "../utils/scoreCalculator.js"
import { getCalcFile } from "../components/common/calc.js"
import { ERROR_MESSAGES, TEMPLATE_PATHS, DISPLAY_MODES, SCORE_GRADES } from "../utils/constants.js"

// 创建实例
const dataProcessor = new DataProcessor()
const imageMapper = new ImageManager()
const scoreCalculator = new ScoreCalculator()

export class WeightQuery extends plugin {
  constructor() {
    super({
      name: "鸣潮-权重查询",
      event: "message",
      priority: 500,
      rule: [
        {
          reg: config.generateCommandRegex("(.+)权重"),
          fnc: "characterWeight",
        },
      ],
    })
  }

  // 查看指定角色权重
  async characterWeight(e) {
    try {
      // 使用动态正则提取角色名称
      const prefixes = config.getAllPrefixes()
      const separator = config.getCommandSeparator()
      const allowEmpty = config.isEmptyPrefixAllowed()

      let regexParts = []
      if (prefixes.length > 0) {
        const escapedPrefixes = prefixes.map(prefix => config.escapeRegex(prefix))
        regexParts.push(`(${escapedPrefixes.join("|")})${config.escapeRegex(separator)}`)
      }
      if (allowEmpty) {
        regexParts.push("")
      }

      const regexPattern = `^(${regexParts.join("|")})(.+)权重$`
      const regex = new RegExp(regexPattern)
      const match = e.msg.match(regex)

      if (!match) {
        await e.reply("❌ 无法解析角色名称")
        return true
      }

      // 提取角色名称（最后一个捕获组）
      const inputName = match[match.length - 1]?.trim()
      if (!inputName) {
        await e.reply("❌ 请指定要查询的角色名称")
        return true
      }

      // 转换别名为标准角色名
      const characterName = aliasToCharName(inputName)
      if (!characterName) {
        await e.reply(`❌ 未找到角色"${inputName}"，请检查角色名称是否正确`)
        return true
      }

      return await this.generateWeightPanel(e, characterName)
    } catch (error) {
      logger.error("[鸣潮权重查询] 角色权重查询失败:", error)
      await e.reply("❌ 权重查询失败，请稍后再试")
      return true
    }
  }

  // 生成权重面板
  async generateWeightPanel(e, characterName) {
    try {
      // 获取用户信息
      const user = User.fromEvent(e)
      if (!user) {
        await e.reply(ERROR_MESSAGES.USER_NOT_FOUND)
        return true
      }

      // 获取用户数据
      const userData = await user.getAllUserData()
      const { uid } = userData

      if (!uid) {
        await e.reply(
          `❌ 您还未绑定UID\n请先使用：${config.getCommandExample("绑定uid 你的UID")}\n或使用：${config.getCommandExample("添加token token,did")}`,
        )
        return true
      }

      // 获取角色ID
      const characterId = getIdByName(characterName)
      if (!characterId) {
        await e.reply(`❌ 未找到角色"${characterName}"的ID`)
        return true
      }

      // 获取角色数据
      const roleDetailList = CharacterCache.getRoleDetailList(uid)
      if (!roleDetailList || roleDetailList.length === 0) {
        await e.reply(
          `❌ 未找到角色数据，请先使用 ${config.getCommandExample("刷新面板")} 获取角色数据`,
        )
        return true
      }

      // 查找指定角色（使用与card.js相同的逻辑）
      const targetCharId = parseInt(characterId)
      const foundRoleDetail = roleDetailList.find(role => {
        // 检查role对象内的roleId字段
        const roleIdToCheck = role.role?.roleId
        return roleIdToCheck === targetCharId || roleIdToCheck === characterId
      })

      if (!foundRoleDetail) {
        console.log(
          `[Rover Plugin] 缓存中未找到角色: ${characterId}，可用角色: ${roleDetailList.map(r => r.role?.roleId).join(", ")}`,
        )
        await e.reply(`❌ 未找到角色"${characterName}"的数据，请先刷新面板`)
        return true
      }

      console.log(
        `[Rover Plugin] 使用缓存的角色数据: ${characterId} (${foundRoleDetail.role?.roleName || "未知角色"})`,
      )

      // 生成权重面板图片（使用与card.js相同的模式）
      const panelImage = await this.generateWeightPanelImage(e, foundRoleDetail, characterName, uid)

      if (panelImage) {
        await e.reply(panelImage)
      } else {
        await e.reply("❌ 生成权重面板失败")
      }

      return true
    } catch (error) {
      logger.error("[鸣潮权重查询] 生成权重面板失败:", error)
      await e.reply("❌ 权重面板生成失败，请稍后再试")
      return true
    }
  }

  // 生成权重面板图片（参考card.js的generateCharacterPanel）
  async generateWeightPanelImage(e, characterData, charName, uid) {
    try {
      // 处理数据
      const processedData = await dataProcessor.processCharacterData(
        characterData,
        characterData.role?.roleId,
      )

      // 创建角色详情模型
      const roleDetail = new RoleDetail(processedData)

      // 获取权重配置文件
      const calcFile = getCalcFile(characterData.role?.roleId)

      // 计算权重数据
      const weightData = await this.calculateWeights(processedData, roleDetail)

      // 映射图片路径
      const renderData = imageMapper.mapRoleDetailImages(characterData.role?.roleId, roleDetail)

      // 添加权重查询特有的渲染数据
      renderData.charName = charName
      renderData.uid = uid
      renderData.element = processedData.element
      renderData.displayMode = DISPLAY_MODES.ARTIS // 使用artis模式显示评分
      renderData.saveTime = new Date().toLocaleString()
      renderData.weights = weightData // 确保权重数据传递给模板

      console.log(`[权重查询] 传递给模板的权重数据:`, weightData)
      console.log(`[权重查询] 声骸数据:`, renderData.data.equipPhantomList)

      // 确保数据结构与profile-detail模板兼容 - 使用合并而不是覆盖
      Object.assign(renderData.data, {
        totalScore: renderData.data.totalScore || weightData[0]?.totalScore || 0,
        totalScoreBackground:
          renderData.data.totalScoreBackground || weightData[0]?.scoreGrade || SCORE_GRADES.C,
      })

      // 为声骸副属性计算具体分数
      if (
        renderData.data.equipPhantomList &&
        renderData.data.equipPhantomList.length > 0 &&
        calcFile
      ) {
        renderData.data.equipPhantomList = renderData.data.equipPhantomList.map(phantom => {
          // 为副属性计算分数
          if (phantom.subPropList && phantom.subPropList.length > 0) {
            phantom.subPropList = phantom.subPropList.map(subProp => {
              const attributeName = subProp.attributeName || subProp.name
              const attributeValue = subProp.attributeValue || subProp.value

              // 从权重配置中获取权重值
              let weight = 0
              if (calcFile.sub_props && calcFile.sub_props[attributeName]) {
                weight = calcFile.sub_props[attributeName]
              }

              // 计算权重得分
              let value = attributeValue
              if (String(value).includes("%")) {
                value = parseFloat(value.replace("%", ""))
              } else {
                value = parseFloat(value)
              }
              const score = isNaN(value) ? 0 : value * weight

              return {
                ...subProp,
                score: score,
              }
            })
          }
          return phantom
        })

        console.log(
          `[权重查询] 声骸副属性分数计算完成:`,
          renderData.data.equipPhantomList[0]?.subPropList,
        )
      }

      // 添加评分标准数据 - 按照WutheringWavesUID的格式
      if (calcFile && calcFile.total_grade) {
        const gradeStandards = calcFile.total_grade
        renderData.data.sssScore = (gradeStandards[4] * 250).toFixed(2)
        renderData.data.ssScore = (gradeStandards[3] * 250).toFixed(2)
        renderData.data.sScore = (gradeStandards[2] * 250).toFixed(2)
        renderData.data.aScore = (gradeStandards[1] * 250).toFixed(2)
        renderData.data.bScore = (gradeStandards[0] * 250).toFixed(2)
      }

      // 使用现有的角色面板模板渲染权重面板
      const img = await renderer.render(e, TEMPLATE_PATHS.CHARACTER_DETAIL, renderData)

      return img
    } catch (error) {
      console.error(`[Rover Plugin] 生成权重面板失败:`, error)
      return null
    }
  }

  // 计算角色权重
  async calculateWeights(characterData, roleDetail) {
    try {
      // 获取角色权重配置
      const calcFile = getCalcFile(characterData.role?.roleId)
      console.log(`[权重查询] 角色权重配置:`, calcFile)

      // 使用项目中已有的评分计算器
      const totalScore = scoreCalculator.calculateTotalScore(roleDetail)
      const scoreGrade = scoreCalculator.getScoreGrade(totalScore, calcFile)
      const scoreRatio = scoreCalculator.getScoreRatio(totalScore)

      // 构建权重分析结果
      const weights = []

      // 声骸评分分析
      const scoreAnalysis = {
        name: "声骸评分分析",
        description: "基于声骸属性的综合评分",
        totalScore: scoreCalculator.formatScore(totalScore, 1),
        scoreGrade: scoreGrade,
        percentage: (scoreRatio * 100).toFixed(1),
        gradeDescription: scoreCalculator.getGradeDescription(scoreGrade),
        details: [],
      }

      // 分析声骸属性权重
      if (characterData.equipPhantomAddPropList && calcFile) {
        console.log(`[权重查询] 声骸属性列表:`, characterData.equipPhantomAddPropList)

        // 导入评分计算函数
        const { getPhantomColorBackground } = await import("../components/common/calc.js")

        characterData.equipPhantomAddPropList.forEach(prop => {
          const attributeName = prop.attributeName || prop.name
          const attributeValue = prop.attributeValue || prop.value

          // 使用现有的评级计算方法
          const grade = getPhantomColorBackground(attributeName, calcFile) || "C"

          // 从权重配置中获取权重值
          let weight = 0
          if (calcFile.sub_props && calcFile.sub_props[attributeName]) {
            weight = calcFile.sub_props[attributeName]
          }

          // 计算权重得分
          let value = attributeValue
          if (String(value).includes("%")) {
            value = parseFloat(value.replace("%", ""))
          } else {
            value = parseFloat(value)
          }
          const weightScore = isNaN(value) ? 0 : scoreCalculator.formatScore(value * weight, 1)

          scoreAnalysis.details.push({
            name: attributeName,
            value: attributeValue,
            grade: grade,
            score: weightScore,
            weight: weight,
            type: "声骸属性",
            isHighValue: weight >= 1.0, // 标记高权重属性
          })
        })
      }

      weights.push(scoreAnalysis)

      // 角色属性分析
      if (characterData.roleAttributeList && characterData.roleAttributeList.length > 0) {
        const attributeAnalysis = {
          name: "角色属性分析",
          description: "角色基础属性数值",
          details: [],
        }

        console.log(`[权重查询] 角色属性列表:`, characterData.roleAttributeList)
        characterData.roleAttributeList.forEach(attr => {
          // 显示所有属性，不过滤C级
          attributeAnalysis.details.push({
            name: attr.attributeName || attr.name,
            value: attr.attributeValue || attr.value,
            grade: attr.valid || "C",
            type: "角色属性",
          })
        })

        if (attributeAnalysis.details.length > 0) {
          weights.push(attributeAnalysis)
        }
      }

      // 声骸详细分析
      if (characterData.equipPhantomList && characterData.equipPhantomList.length > 0) {
        const phantomAnalysis = {
          name: "声骸详细分析",
          description: "各个声骸的评分详情",
          details: [],
        }

        console.log(`[权重查询] 声骸列表:`, characterData.equipPhantomList)
        characterData.equipPhantomList.forEach((phantom, index) => {
          // 使用现有的评分系统
          const score = phantom.score && phantom.score[0] ? phantom.score[0] : 0
          const grade = phantom.score && phantom.score[1] ? phantom.score[1] : "C"

          // 使用scoreCalculator格式化分数
          const formattedScore = scoreCalculator.formatScore(score, 1)
          const gradeDescription = scoreCalculator.getGradeDescription(grade)
          const isHighScore = scoreCalculator.isHighScore(grade)

          phantomAnalysis.details.push({
            name: `${phantom.name || `声骸${index + 1}`}`,
            value: formattedScore,
            grade: grade,
            gradeDescription: gradeDescription,
            isHighScore: isHighScore,
            type: "声骸评分",
            position: `位置${phantom.cost || index + 1}`, // 声骸位置信息
          })
        })

        // 计算声骸平均分
        const scores = characterData.equipPhantomList.map(p => p.score?.[0] || 0)
        const averageScore = scoreCalculator.calculateAverageScore(scores)
        phantomAnalysis.averageScore = scoreCalculator.formatScore(averageScore, 1)

        weights.push(phantomAnalysis)
      }

      // 添加权重配置说明 - WutheringWavesUID信息内容，miao-plugin样式
      if (calcFile) {
        const weightConfig = {
          name: "权重配置说明",
          description: `${calcFile.name || "通用配置"}的权重设置`,
          details: [],
        }

        // 按照WutheringWavesUID的weight_list顺序显示所有属性
        const weightList = [
          "生命",
          "生命%",
          "攻击",
          "攻击%",
          "防御",
          "防御%",
          "共鸣效率",
          "暴击",
          "暴击伤害",
          "属性伤害加成",
          "治疗效果加成",
          "普攻伤害加成",
          "重击伤害加成",
          "共鸣技能伤害加成",
          "共鸣解放伤害加成",
        ]

        weightList.forEach(propName => {
          // 获取各种权重值
          const c4Weight = calcFile.main_props?.["4"]?.[propName] || 0
          const c3Weight = calcFile.main_props?.["3"]?.[propName] || 0
          const c1Weight = calcFile.main_props?.["1"]?.[propName] || 0
          let subWeight = calcFile.sub_props?.[propName] || 0

          // 处理技能伤害加成的特殊情况
          if (
            propName.includes("伤害加成") &&
            propName !== "属性伤害加成" &&
            propName !== "治疗效果加成"
          ) {
            const skillWeights = calcFile.skill_weight || [0, 0, 0, 0]
            const baseSkillWeight = calcFile.sub_props?.["技能伤害加成"] || 0

            if (propName === "普攻伤害加成") {
              subWeight = baseSkillWeight * skillWeights[0]
            } else if (propName === "重击伤害加成") {
              subWeight = baseSkillWeight * skillWeights[1]
            } else if (propName === "共鸣技能伤害加成") {
              subWeight = baseSkillWeight * skillWeights[2]
            } else if (propName === "共鸣解放伤害加成") {
              subWeight = baseSkillWeight * skillWeights[3]
            }
          }

          // 只显示有权重值的属性（任一权重大于0）
          if (c4Weight > 0 || c3Weight > 0 || c1Weight > 0 || subWeight > 0) {
            weightConfig.details.push({
              name: propName,
              c4Weight: c4Weight > 0 ? c4Weight.toFixed(3) : "-",
              c3Weight: c3Weight > 0 ? c3Weight.toFixed(3) : "-",
              c1Weight: c1Weight > 0 ? c1Weight.toFixed(3) : "-",
              weight: subWeight > 0 ? subWeight.toFixed(3) : "-",
              type: "权重配置",
            })
          }
        })

        // 显示技能权重
        if (calcFile.skill_weight) {
          const skillNames = ["普攻", "重击", "共鸣技能", "共鸣解放"]
          calcFile.skill_weight.forEach((weight, index) => {
            if (weight > 0) {
              weightConfig.details.push({
                name: `${skillNames[index]}权重`,
                value: `${weight}`,
                grade: weight >= 0.7 ? "S" : weight >= 0.5 ? "A" : "B",
                type: "技能权重",
              })
            }
          })
        }

        if (weightConfig.details.length > 0) {
          weights.push(weightConfig)
        }
      }

      console.log(`[权重查询] 最终权重数据:`, JSON.stringify(weights, null, 2))

      return weights.length > 0
        ? weights
        : [
            {
              name: "基础分析",
              description: "角色基础数据分析",
              totalScore: totalScore.toFixed(1),
              scoreGrade: scoreGrade,
              details: [
                {
                  name: "总评分",
                  value: totalScore.toFixed(1),
                  type: "综合评分",
                },
              ],
            },
          ]
    } catch (error) {
      logger.error("[权重查询] 权重计算失败:", error)
      return {
        error: "权重计算失败，请稍后再试",
      }
    }
  }
}
