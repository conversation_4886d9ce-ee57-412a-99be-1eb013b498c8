import fs from "fs"
import path from "path"
import { dataPath } from "./path.js"

// 缓存目录
const cacheDir = path.join(dataPath, "cache")
const playerDir = path.join(cacheDir, "players")
// 面板数据目录 保存到 data/rover/players/mc/
const panelDir = path.join(dataPath, "rover", "players", "mc")

// 确保缓存目录存在
if (!fs.existsSync(cacheDir)) {
  fs.mkdirSync(cacheDir, { recursive: true })
}

if (!fs.existsSync(playerDir)) {
  fs.mkdirSync(playerDir, { recursive: true })
}

// 确保面板数据目录存在
if (!fs.existsSync(panelDir)) {
  fs.mkdirSync(panelDir, { recursive: true })
}

/**
 * 角色数据缓存管理
 * 参考WutheringWavesUID的缓存机制
 */
export class CharacterCache {
  /**
   * 保存完整角色数据到缓存
   * @param {string} uid - 用户UID
   * @param {Object} characterData - 完整角色数据
   * @returns {boolean} 是否保存成功
   */
  static async setCharacterData(uid, characterData) {
    try {
      // 保存到面板数据目录 data/players/mc/{uid}.json
      const panelFilePath = path.join(panelDir, `${uid}.json`)
      const data = JSON.stringify(characterData, null, 2)
      fs.writeFileSync(panelFilePath, data, "utf8")
      console.log(`面板数据保存成功: ${panelFilePath}`)

      // 同时保存到原来的缓存目录（兼容性）
      const uidDir = path.join(playerDir, uid)
      if (!fs.existsSync(uidDir)) {
        fs.mkdirSync(uidDir, { recursive: true })
      }
      const legacyFilePath = path.join(uidDir, "characterData.json")
      fs.writeFileSync(legacyFilePath, data, "utf8")
      console.log(`完整角色数据缓存保存成功: ${uid}`)

      return true
    } catch (error) {
      console.error(`保存完整角色数据缓存失败 [${uid}]:`, error)
      return false
    }
  }

  /**
   * 从面板数据目录获取角色数据
   * @param {string} uid - 用户UID
   * @returns {Object|null} 角色数据
   */
  static async getPanelData(uid) {
    try {
      const panelFilePath = path.join(panelDir, `${uid}.json`)
      if (fs.existsSync(panelFilePath)) {
        const data = fs.readFileSync(panelFilePath, "utf8")
        return JSON.parse(data)
      }
      return null
    } catch (error) {
      console.error(`读取面板数据失败 [${uid}]:`, error)
      return null
    }
  }

  /**
   * 获取完整角色数据
   * @param {string} uid - 用户UID
   * @returns {Object|null} 角色数据
   */
  static async getCharacterData(uid) {
    try {
      const filePath = path.join(playerDir, uid, "characterData.json")
      if (!fs.existsSync(filePath)) {
        return null
      }

      const data = fs.readFileSync(filePath, "utf8")
      return JSON.parse(data)
    } catch (error) {
      console.error(`获取完整角色数据缓存失败 [${uid}]:`, error)
      return null
    }
  }

  /**
   * 保存角色数据到缓存
   * @param {string} uid - 用户UID
   * @param {Array} roleDetailList - 角色详情列表
   * @returns {boolean} 是否保存成功
   */
  static async saveRoleDetailList(uid, roleDetailList) {
    try {
      // 保存到新的面板数据位置 data/players/mc/{uid}.json
      const panelFilePath = path.join(panelDir, `${uid}.json`)
      const data = JSON.stringify(roleDetailList, null, 2)
      fs.writeFileSync(panelFilePath, data, "utf8")
      console.log(`面板数据保存成功: ${panelFilePath}`)

      // 同时保存到原来的缓存位置（兼容性）
      const uidDir = path.join(playerDir, uid)
      if (!fs.existsSync(uidDir)) {
        fs.mkdirSync(uidDir, { recursive: true })
      }
      const legacyFilePath = path.join(uidDir, "rawData.json")
      fs.writeFileSync(legacyFilePath, data, "utf8")
      console.log(`角色数据缓存保存成功: ${uid}`)

      return true
    } catch (error) {
      console.error(`保存角色数据缓存失败 [${uid}]:`, error)
      return false
    }
  }

  /**
   * 保存原始JSON数据（参考WutheringWavesUID格式）
   * @param {string} uid - 用户UID
   * @param {Object} rawData - 原始数据
   * @returns {boolean} 是否保存成功
   */
  static async saveRawCharacterData(uid, rawData) {
    try {
      const uidDir = path.join(playerDir, uid)
      if (!fs.existsSync(uidDir)) {
        fs.mkdirSync(uidDir, { recursive: true })
      }

      // 保存完整的原始JSON数据（按WutheringWavesUID格式）
      const filePath = path.join(uidDir, "fullRawData.json")
      const data = JSON.stringify(rawData, null, 2)

      fs.writeFileSync(filePath, data, "utf8")
      console.log(`原始JSON数据保存成功: ${uid}`)
      return true
    } catch (error) {
      console.error(`保存原始JSON数据失败 [${uid}]:`, error)
      return false
    }
  }

  /**
   * 从缓存获取角色数据列表
   * @param {string} uid - 用户UID
   * @returns {Array|null} 角色详情列表
   */
  static getRoleDetailList(uid) {
    try {
      // 优先从新的面板数据位置读取
      const panelFilePath = path.join(panelDir, `${uid}.json`)
      if (fs.existsSync(panelFilePath)) {
        const data = fs.readFileSync(panelFilePath, "utf8")
        const roleDetailList = JSON.parse(data)
        return Array.isArray(roleDetailList) ? roleDetailList : null
      }

      // 如果新位置没有，从旧位置读取（兼容性）
      const legacyFilePath = path.join(playerDir, uid, "rawData.json")
      if (!fs.existsSync(legacyFilePath)) {
        return null
      }

      const data = fs.readFileSync(legacyFilePath, "utf8")
      const roleDetailList = JSON.parse(data)

      return Array.isArray(roleDetailList) ? roleDetailList : null
    } catch (error) {
      console.error(`获取角色数据缓存失败 [${uid}]:`, error)
      // 如果文件损坏，删除它
      try {
        const panelFilePath = path.join(panelDir, `${uid}.json`)
        if (fs.existsSync(panelFilePath)) {
          fs.unlinkSync(panelFilePath)
        }
        const legacyFilePath = path.join(playerDir, uid, "rawData.json")
        if (fs.existsSync(legacyFilePath)) {
          fs.unlinkSync(legacyFilePath)
        }
      } catch (deleteError) {
        console.error(`删除损坏缓存文件失败:`, deleteError)
      }
      return null
    }
  }

  /**
   * 获取角色数据映射（按角色名）
   * @param {string} uid - 用户UID
   * @returns {Object|null} 角色数据映射
   */
  static getRoleDetailMap(uid) {
    const roleDetailList = this.getRoleDetailList(uid)
    if (!roleDetailList) {
      return null
    }

    const roleMap = {}
    roleDetailList.forEach(roleDetail => {
      if (roleDetail.role && roleDetail.role.roleName) {
        roleMap[roleDetail.role.roleName] = roleDetail
      }
    })

    return roleMap
  }

  /**
   * 获取角色数据映射（按角色ID）
   * @param {string} uid - 用户UID
   * @returns {Object|null} 角色数据映射
   */
  static getRoleDetailMapById(uid) {
    const roleDetailList = this.getRoleDetailList(uid)
    if (!roleDetailList) {
      return null
    }

    const roleMap = {}
    roleDetailList.forEach(roleDetail => {
      if (roleDetail.role && roleDetail.role.roleId) {
        roleMap[roleDetail.role.roleId.toString()] = roleDetail
      }
    })

    return roleMap
  }

  /**
   * 获取指定角色的数据
   * @param {string} uid - 用户UID
   * @param {string} roleName - 角色名
   * @returns {Object|null} 角色数据
   */
  static async getRoleDetail(uid, roleName) {
    const roleMap = await this.getRoleDetailMap(uid)
    if (!roleMap) {
      return null
    }

    return roleMap[roleName] || null
  }

  /**
   * 获取指定角色的数据（按ID）
   * @param {string} uid - 用户UID
   * @param {string} roleId - 角色ID
   * @returns {Object|null} 角色数据
   */
  static async getRoleDetailById(uid, roleId) {
    const roleMap = await this.getRoleDetailMapById(uid)
    if (!roleMap) {
      return null
    }

    return roleMap[roleId.toString()] || null
  }

  /**
   * 检查缓存是否存在
   * @param {string} uid - 用户UID
   * @returns {boolean} 缓存是否存在
   */
  static hasCacheData(uid) {
    const filePath = path.join(playerDir, uid, "rawData.json")
    return fs.existsSync(filePath)
  }

  /**
   * 获取缓存文件的修改时间
   * @param {string} uid - 用户UID
   * @returns {Date|null} 修改时间
   */
  static getCacheTime(uid) {
    try {
      const filePath = path.join(playerDir, uid, "rawData.json")
      if (!fs.existsSync(filePath)) {
        return null
      }

      const stats = fs.statSync(filePath)
      return stats.mtime
    } catch (error) {
      console.error(`获取缓存时间失败 [${uid}]:`, error)
      return null
    }
  }

  /**
   * 删除用户缓存
   * @param {string} uid - 用户UID
   * @returns {boolean} 是否删除成功
   */
  static deleteCacheData(uid) {
    try {
      const uidDir = path.join(playerDir, uid)
      if (fs.existsSync(uidDir)) {
        fs.rmSync(uidDir, { recursive: true, force: true })
        console.log(`删除用户缓存成功: ${uid}`)
      }
      return true
    } catch (error) {
      console.error(`删除用户缓存失败 [${uid}]:`, error)
      return false
    }
  }

  /**
   * 清理指定用户的缓存
   * @param {Array} uids - 要清理的UID列表
   * @returns {number} 清理的缓存数量
   */
  static cleanUserCache(uids = []) {
    try {
      let cleanedCount = 0

      for (const uid of uids) {
        if (this.deleteCacheData(uid)) {
          cleanedCount++
        }
      }

      console.log(`清理用户缓存完成，共清理 ${cleanedCount} 个`)
      return cleanedCount
    } catch (error) {
      console.error("清理用户缓存失败:", error)
      return 0
    }
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计
   */
  static getCacheStats() {
    try {
      const playerDirs = fs.readdirSync(playerDir)
      let totalSize = 0
      let validCount = 0

      for (const uid of playerDirs) {
        const filePath = path.join(playerDir, uid, "rawData.json")
        if (fs.existsSync(filePath)) {
          const stats = fs.statSync(filePath)
          totalSize += stats.size
          validCount++
        }
      }

      return {
        totalUsers: playerDirs.length,
        validCount,
        totalSize: Math.round(totalSize / 1024), // KB
      }
    } catch (error) {
      console.error("获取缓存统计失败:", error)
      return {
        totalUsers: 0,
        validCount: 0,
        totalSize: 0,
      }
    }
  }

  /**
   * 获取所有已缓存的UID列表
   * @returns {Array} UID列表
   */
  static getAllCachedUids() {
    try {
      const playerDirs = fs.readdirSync(playerDir)
      return playerDirs.filter(uid => {
        const filePath = path.join(playerDir, uid, "rawData.json")
        return fs.existsSync(filePath)
      })
    } catch (error) {
      console.error("获取已缓存UID列表失败:", error)
      return []
    }
  }

  /**
   * 批量保存多个用户的角色数据
   * @param {Object} userDataMap - 用户数据映射 {uid: roleDetailList}
   * @returns {Object} 保存结果统计
   */
  static async batchSaveRoleDetailList(userDataMap) {
    const results = {
      success: 0,
      failed: 0,
      total: Object.keys(userDataMap).length,
    }

    for (const [uid, roleDetailList] of Object.entries(userDataMap)) {
      try {
        const success = await this.saveRoleDetailList(uid, roleDetailList)
        if (success) {
          results.success++
        } else {
          results.failed++
        }
      } catch (error) {
        console.error(`批量保存用户 ${uid} 数据失败:`, error)
        results.failed++
      }
    }

    console.log(
      `批量保存完成: 成功 ${results.success}，失败 ${results.failed}，总计 ${results.total}`,
    )
    return results
  }

  /**
   * 验证缓存数据完整性
   * @param {string} uid - 用户UID
   * @returns {boolean} 数据是否完整
   */
  static validateCacheData(uid) {
    try {
      const roleDetailList = this.getRoleDetailList(uid)
      if (!roleDetailList || !Array.isArray(roleDetailList)) {
        return false
      }

      // 检查每个角色数据的基本结构
      for (const roleDetail of roleDetailList) {
        if (!roleDetail.role || !roleDetail.role.roleId || !roleDetail.role.roleName) {
          return false
        }
      }

      return true
    } catch (error) {
      console.error(`验证缓存数据失败 [${uid}]:`, error)
      return false
    }
  }

  /**
   * 修复损坏的缓存数据
   * @param {string} uid - 用户UID
   * @returns {boolean} 是否修复成功
   */
  static async repairCacheData(uid) {
    try {
      const isValid = await this.validateCacheData(uid)
      if (!isValid) {
        console.log(`检测到用户 ${uid} 的缓存数据损坏，正在删除...`)
        return this.deleteCacheData(uid)
      }
      return true
    } catch (error) {
      console.error(`修复缓存数据失败 [${uid}]:`, error)
      return false
    }
  }
}

export default CharacterCache
