{{set ds = $data[0] || false }}
{{set {_res_path, icon,lvs,type,game,minLv,maxLv} = $data[1]}}

<div class="talent-line">
  <div class="talent-icon">
    <img src="{{_res_path}}{{icon}}"/>
  </div>
  <div class="talent-info">
    <div class="talent-name">{{ds.name}}</div>
    <div class="talent-desc">
      {{each ds.desc d}}
      {{ if d[0] === "<" || game==='sr' }}
      {{@d}}
      {{else if d!=""}}
      <p>{{d}}</p>
      {{/if}}
      {{/each}}
    </div>
  </div>

  {{if ds.tables && ds.tables.length > 0}}
  <div class="talent-common-info">
    {{each ds.tables tr}}
    {{if tr.isSame}}
    <div>
      <strong>{{tr.name}}{{if tr.unit}}({{tr.unit}}){{/if}}</strong>
      <span>{{tr.values[0]}}</span>
    </div>
    {{/if}}
    {{/each}}
  </div>
  <table class="talent-table cont-table">
    <tr class="tr">
      <td class="th"></td>
      {{each lvs lv idx}}
      {{if idx+1 >= minLv && idx+1 <= maxLv }}
      <td class="th lv">{{lv}}</td>
      {{/if}}
      {{/each}}
    </tr>
    {{each ds.tables tr}}
    {{if !tr.isSame}}
    <tr class="tr">
      <td class="th talent-name">
        {{tr.name}}
        {{if tr.unit}}
        <span class="unit">({{tr.unit}})</span>
        {{/if}}
      </td>

      {{each tr.values v idx}}
      {{if idx+1 >= minLv && idx+1 <= maxLv }}
      <td class="td">{{v}}</td>
      {{/if}}
      {{/each}}
      {{/if}}
    </tr>
    {{/each}}
  </table>
  {{/if}}
</div>
