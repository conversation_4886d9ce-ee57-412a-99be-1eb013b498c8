import ky from "ky"
import fs from "fs"
import path from "path"
import { JSD<PERSON> } from "jsdom"
import { EPATH_MAP } from "./resourcePath.js"
import { pluginName } from "../path.js"

let globalTag = ""
let globalUrl = ""

const setGlobalTag = tag => (globalTag = tag)
const setGlobalUrl = url => (globalUrl = url)

// 节点列表
const URL_LIB = {
  "[CNJS]": "http://cn-js-nj-1.lcf.icu:13214",
  "[TW]": "http://tw-taipei-1.lcf.icu:20532",
  "[SG]": "http://sg-1.lcf.icu:12588",
  "[US]": "http://us-lax-2.lcf.icu:12588",
  "[Chuncheon]": "https://kr.qxqx.cf",
  "[Seoul]": "https://kr-s.qxqx.cf",
  "[Singapore]": "https://sg.qxqx.cf",
  "[MiniGG]": "http://file.minigg.cn/sayu-bot",
  "[Lulu]": "http://lulush.microgg.cn",
  "[TakeyaYuki]": "https://gscore.focalors.com",
}

/**
 * 并发检测最快的URL
 * @param {Object} urls - URL字典，默认使用 URL_LIB
 * @returns {Promise<{tag: string, url: string, time: number}>}
 */
async function findLowestLatencyUrl(urls = URL_LIB) {
  const results = await Promise.allSettled(
    Object.entries(urls).map(async ([tag, url]) => {
      const start = Date.now()
      await ky.head(url, { timeout: 5000, retry: 0 })
      return { tag, url, time: Date.now() - start }
    }),
  )

  return results
    .filter(r => r.status === "fulfilled")
    .map(r => r.value)
    .reduce((fastest, current) => (current.time < fastest.time ? current : fastest), {
      time: Infinity,
    })
}

/**
 * 检测最快节点
 * @returns {Promise<{tag: string, url: string}>}
 */
async function detectAndSetFastestUrl() {
  if (globalTag && globalUrl) {
    return { tag: globalTag, url: globalUrl }
  }
  const { tag, url } = await findLowestLatencyUrl()
  if (tag && url) {
    setGlobalTag(tag)
    setGlobalUrl(url)
  }
  console.log(`[${pluginName}] 检测最快节点完成: ${tag} ${url}`)
  return { tag, url }
}

/**
 * 解析文件列表
 * @param {string} htmlString - HTML字符串
 * @returns {Array<{href: string, name: string, size: number, isDirectory: boolean}>}
 */
function parseFileList(htmlString) {
  const dom = new JSDOM(htmlString)
  const pre = dom.window.document.querySelector("pre")
  if (!pre) return []
  return Array.from(pre.querySelectorAll("a"))
    .filter(link => link.getAttribute("href") !== "../")
    .map(link => {
      const href = link.getAttribute("href")
      const decoded = decodeURIComponent(href)
      const isDirectory = href.endsWith("/")
      let size = -1
      if (!isDirectory) {
        let node = link.nextSibling
        while (node && node.nodeType !== 3) node = node.nextSibling
        if (node) {
          const m = node.textContent.match(/(\d+)\s*$/)
          if (m) size = parseInt(m[1], 10)
        }
      }
      return {
        href,
        name: isDirectory ? decoded.replace(/\/$/, "") : decoded,
        size,
        isDirectory,
      }
    })
}

/**
 * 获取文件大小
 * @param {string} filePath - 文件路径
 * @returns {number | null}
 */
function getFileSize(filePath) {
  try {
    return fs.statSync(filePath).size
  } catch {
    return null
  }
}

/**
 * 获取目标路径
 * @param {string} endpoint - 端点
 * @param {Object} EPATH_MAP - 路径映射
 * @returns {string | null}
 */
function getTargetPath(endpoint, EPATH_MAP) {
  const norm = endpoint.replace(/\/+$/, "")
  if (EPATH_MAP[norm]) return EPATH_MAP[norm]
  const parts = norm.split("/")
  for (let i = parts.length - 1; i > 0; i--) {
    const parent = parts.slice(0, i).join("/")
    if (EPATH_MAP[parent]) return path.join(EPATH_MAP[parent], ...parts.slice(i))
  }
  return null
}

/**
 * 下载文件
 * @param {string} url - 文件URL
 * @param {string} filePath - 文件路径
 * @param {string} fileName - 文件名
 */
async function downloadFile(url, filePath, fileName) {
  try {
    const res = await ky.get(url)
    const buf = await res.arrayBuffer()
    fs.mkdirSync(path.dirname(filePath), { recursive: true })
    fs.writeFileSync(filePath, Buffer.from(buf))
    console.log(`[${pluginName}] ✅ ${globalTag} ${fileName} 资源下载完成`)
  } catch (e) {
    console.error(`[${pluginName}] ❌ ${globalTag} ${fileName} 资源下载失败: ${e.message}`)
  }
}

/**
 * 批量下载
 * @param {Array<Promise<void>>} tasks - 下载任务列表
 * @param {number} batchSize - 批量大小
 */
async function downloadInBatches(tasks, batchSize = 5) {
  for (let i = 0; i < tasks.length; i += batchSize) {
    await Promise.all(tasks.slice(i, i + batchSize))
  }
}

/**
 * 下载指定路径的文件
 * @param {string} baseUrl - 基础URL
 * @param {string} endpoint - 端点
 * @param {Object} EPATH_MAP - 路径映射
 */
async function downloadAtagFile(baseUrl, endpoint, EPATH_MAP) {
  let url = `${baseUrl}/WutheringWavesUID/${endpoint}`
  if (!url.endsWith("/")) url += "/"
  const targetPath = getTargetPath(endpoint, EPATH_MAP)
  if (!targetPath) return
  fs.mkdirSync(targetPath, { recursive: true })
  try {
    const html = await ky.get(url).text()
    const items = parseFileList(html)
    const downloadTasks = []
    for (const item of items) {
      const fileUrl = `${url}${item.href}`
      const filePath = path.join(targetPath, item.name)
      if (item.isDirectory) {
        const nextEndpoint = endpoint.endsWith("/")
          ? `${endpoint}${item.href}`
          : `${endpoint}/${item.href}`
        await downloadAtagFile(baseUrl, nextEndpoint, EPATH_MAP)
        continue
      }
      if (fs.existsSync(filePath) && fs.statSync(filePath).isDirectory()) continue
      const localSize = getFileSize(filePath)
      if (!fs.existsSync(filePath) || localSize === 0 || localSize !== item.size) {
        downloadTasks.push(downloadFile(fileUrl, filePath, item.name))
      }
    }
    if (downloadTasks.length > 0) await downloadInBatches(downloadTasks)
  } catch (e) {
    console.error(`[${pluginName}] ❌ 处理 ${endpoint} 失败: ${e.message}`)
  }
}

/**
 * 下载所有文件
 */
async function downloadAllFile() {
  await detectAndSetFastestUrl()

  for (const key of Object.keys(EPATH_MAP)) {
    await downloadAtagFile(globalUrl, key, EPATH_MAP)
  }
  console.log(`[${pluginName}] ✅ 资源下载完成`)
}

export { downloadAllFile }
