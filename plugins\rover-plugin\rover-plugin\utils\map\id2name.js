/**
 * 角色ID和名称映射管理
 * 统一管理角色ID到名称的映射关系
 * 基于现有的 id2name.json 和 components/common/id2name.js
 */

import id2nameData from "./id2name.json" assert { type: "json" }

/**
 * 根据角色ID获取角色名称
 * @param {number|string} id - 角色ID
 * @returns {string|null} 角色名称
 */
export function getNameById(id) {
  const idStr = id.toString()
  return id2nameData[idStr] || null
}

/**
 * 根据角色名称获取角色ID
 * @param {string} name - 角色名称
 * @returns {number|null} 角色ID
 */
export function getIdByName(name) {
  for (const [id, charName] of Object.entries(id2nameData)) {
    if (charName === name) {
      return parseInt(id)
    }
  }
  return null
}

/**
 * 获取所有角色映射
 * @returns {Object} 完整的ID到名称映射
 */
export function getAllMappings() {
  return { ...id2nameData }
}

/**
 * 获取所有角色ID列表
 * @returns {Array} 角色ID列表
 */
export function getAllIds() {
  return Object.keys(id2nameData).map(id => parseInt(id))
}

/**
 * 获取所有角色名称列表
 * @returns {Array} 角色名称列表
 */
export function getAllNames() {
  return Object.values(id2nameData)
}

/**
 * 检查角色ID是否存在
 * @param {number|string} id - 角色ID
 * @returns {boolean} 是否存在
 */
export function hasId(id) {
  const idStr = id.toString()
  return idStr in id2nameData
}

/**
 * 检查角色名称是否存在
 * @param {string} name - 角色名称
 * @returns {boolean} 是否存在
 */
export function hasName(name) {
  return Object.values(id2nameData).includes(name)
}

/**
 * 获取角色信息
 * @param {number|string} id - 角色ID
 * @returns {Object|null} 角色信息 {id, name}
 */
export function getCharacterInfo(id) {
  const name = getNameById(id)
  if (!name) return null
  
  return {
    id: parseInt(id),
    name
  }
}

/**
 * 按星级分组的角色映射
 * 基于角色ID范围推断星级
 */
export const charactersByRarity = {
  // 5星角色 (主要角色)
  5: [
    { id: 1205, name: "长离" },
    { id: 1203, name: "安可" },
    { id: 1204, name: "凌阳" },
    { id: 1102, name: "散华" },
    { id: 1301, name: "卡卡罗" },
    { id: 1302, name: "吟霖" },
    { id: 1402, name: "秧秧" },
    { id: 1403, name: "秋水" },
    { id: 1404, name: "椿" },
    { id: 1405, name: "渊武" },
    { id: 1502, name: "漂泊者·衍射" },
    { id: 1503, name: "维里奈" },
    { id: 1504, name: "灯灯" },
    { id: 1505, name: "守岸人" },
    { id: 1506, name: "菲比" },
    { id: 1601, name: "桃祈" },
    { id: 1603, name: "椿" }
  ],
  
  // 4星角色
  4: [
    { id: 1103, name: "白芷" },
    { id: 1104, name: "凌阳" },
    { id: 1105, name: "折枝" },
    { id: 1106, name: "釉瑚" },
    { id: 1107, name: "珂莱塔" },
    { id: 1201, name: "折枝" },
    { id: 1202, name: "炽霞" },
    { id: 1207, name: "露帕" },
    { id: 1303, name: "渊武" },
    { id: 1304, name: "今汐" },
    { id: 1401, name: "卡卡罗" },
    { id: 1406, name: "漂泊者·气动" },
    { id: 1407, name: "夏空" },
    { id: 1408, name: "漂泊者·气动" },
    { id: 1409, name: "卡提希娅" },
    { id: 1507, name: "赞妮" }
  ],
  
  // 特殊角色 (漂泊者)
  special: [
    { id: 1501, name: "漂泊者·衍射" },
    { id: 1502, name: "漂泊者·衍射" },
    { id: 1604, name: "漂泊者·湮灭" },
    { id: 1605, name: "漂泊者·湮灭" },
    { id: 1406, name: "漂泊者·气动" },
    { id: 1408, name: "漂泊者·气动" }
  ]
}

/**
 * 角色属性映射
 * 基于角色名称推断属性
 */
export const characterElements = {
  "长离": "pyro",      // 热熔
  "今汐": "glacio",    // 冷凝
  "凌阳": "glacio",    // 冷凝
  "安可": "fusion",    // 聚变
  "白芷": "glacio",    // 冷凝
  "秋水": "havoc",     // 湮灭
  "散华": "havoc",     // 湮灭
  "桃祈": "havoc",     // 湮灭
  "椿": "havoc",       // 湮灭
  "渊武": "electro",   // 导电
  "灯灯": "glacio",    // 冷凝
  "炽霞": "fusion",    // 聚变
  "相里要": "electro", // 导电
  "秧秧": "electro",   // 导电
  "吟霖": "spectro",   // 衍射
  "釉瑚": "glacio",    // 冷凝
  "赞妮": "electro",   // 导电
  "折枝": "aero",      // 气动
  "守岸人": "spectro", // 衍射
  "维里奈": "spectro", // 衍射
  "夏空": "aero",      // 气动
  "卡卡罗": "electro", // 导电
  "漂泊者·湮灭": "havoc",   // 湮灭
  "漂泊者·衍射": "spectro", // 衍射
  "漂泊者·气动": "aero"     // 气动
}

/**
 * 角色武器类型映射
 */
export const characterWeapons = {
  "长离": "sword",      // 佩剑
  "今汐": "sword",      // 佩剑
  "凌阳": "broadblade", // 阔刀
  "安可": "pistols",    // 音感仪
  "白芷": "rectifier",  // 音感仪
  "秋水": "sword",      // 佩剑
  "散华": "gauntlets",  // 拳套
  "桃祈": "broadblade", // 阔刀
  "椿": "sword",        // 佩剑
  "渊武": "gauntlets",  // 拳套
  "灯灯": "rectifier",  // 音感仪
  "炽霞": "pistols",    // 音感仪
  "相里要": "gauntlets", // 拳套
  "秧秧": "pistols",    // 音感仪
  "吟霖": "rectifier",  // 音感仪
  "釉瑚": "rectifier",  // 音感仪
  "赞妮": "pistols",    // 音感仪
  "折枝": "sword",      // 佩剑
  "守岸人": "broadblade", // 阔刀
  "维里奈": "pistols",  // 音感仪
  "夏空": "pistols",    // 音感仪
  "卡卡罗": "broadblade", // 阔刀
  "漂泊者·湮灭": "sword",   // 佩剑
  "漂泊者·衍射": "sword",   // 佩剑
  "漂泊者·气动": "sword"    // 佩剑
}

/**
 * 获取角色属性
 * @param {string} name - 角色名称
 * @returns {string|null} 角色属性
 */
export function getCharacterElement(name) {
  return characterElements[name] || null
}

/**
 * 获取角色武器类型
 * @param {string} name - 角色名称
 * @returns {string|null} 武器类型
 */
export function getCharacterWeapon(name) {
  return characterWeapons[name] || null
}

/**
 * 获取角色完整信息
 * @param {number|string} id - 角色ID
 * @returns {Object|null} 角色完整信息
 */
export function getFullCharacterInfo(id) {
  const name = getNameById(id)
  if (!name) return null
  
  return {
    id: parseInt(id),
    name,
    element: getCharacterElement(name),
    weapon: getCharacterWeapon(name)
  }
}

export default {
  getNameById,
  getIdByName,
  getAllMappings,
  getAllIds,
  getAllNames,
  hasId,
  hasName,
  getCharacterInfo,
  charactersByRarity,
  characterElements,
  characterWeapons,
  getCharacterElement,
  getCharacterWeapon,
  getFullCharacterInfo
}
