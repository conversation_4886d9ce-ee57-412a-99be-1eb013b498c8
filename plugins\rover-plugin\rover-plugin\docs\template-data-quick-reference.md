# HTML 模板数据快速参考

## 基本调用格式

```javascript
const renderData = {
  // 必需字段
  uid: "123456789",
  game: "ww",

  // 功能特有字段
  // ...

  // 可选字段
  element: "hydro",
  saveTime: new Date().toLocaleString()
}

const img = await renderer.render(e, "模板路径", renderData)
```

## 可用模板列表

### 角色相关模板
- `character/profile-detail` - 角色详细面板
- `character/profile-list` - 角色列表
- `character/echo-list` - 声骸列表
- `character/ww-style` - 角色卡片
- `character/training-stat` - 练度统计

### 排行榜模板
- `rank/group-rank` - 群排行榜

### 其他模板
- `training/all-character-training` - 全角色练度

## 模板数据速查表

### 1. 角色面板 (`character/profile-detail`)

**调用**: `renderer.render(e, "character/profile-detail", renderData)`

**关键字段**:
```javascript
{
  // 基础信息
  charName: "维拉",                    // 角色名称
  uid: "123456789",                   // 用户UID
  displayMode: "default",             // 显示模式: "default" | "artis"

  // 角色数据
  data: {
    // 角色基本信息
    name: "维拉",                     // 角色名称
    level: 90,                        // 角色等级
    star: 5,                          // 角色星级
    element: "hydro",                 // 元素类型
    pile: "/images/pile/1404.png",    // 角色立绘路径
    chainUnlockNum: 6,                // 已解锁命座数量

    // 武器信息 (displayMode !== "artis" 时显示)
    weapon: {
      name: "苍鳞千嶂",               // 武器名称
      level: 90,                      // 武器等级
      resonLevel: 1,                  // 谐振等级
      pic: "/images/weapon/xxx.png",  // 武器图标路径
      mainPropList: [{                // 武器主属性列表
        attributeName: "攻击力",      // 属性名称
        attributeValue: "587",        // 属性值
        pic: "/images/attr/xxx.png"   // 属性图标路径
      }]
    },

    // 技能信息 (displayMode !== "artis" 时显示)
    skillList: [{
      type: "常态攻击",               // 技能类型
      level: 10,                      // 技能等级
      pic: "/images/skill/xxx.png"    // 技能图标路径
    }],

    // 命座信息 (displayMode !== "artis" 时显示)
    chainList: [{
      unlocked: true,                 // 是否已解锁
      pic: "/images/chain/xxx.png"    // 命座图标路径
    }],

    // 声骸信息
    equipPhantomList: [{
      name: "鸣钟之龟",               // 声骸名称
      level: 25,                      // 声骸等级
      cost: 4,                        // 声骸费用
      pic: "/images/phantom/xxx.png", // 声骸图标路径
      score: [85.2, "S"],             // 评分 [分数, 等级]

      // 主属性列表
      mainPropList: [{
        attributeName: "攻击力%",     // 属性名称
        attributeValue: "22.8%",      // 属性值
        pic: "/images/attr/xxx.png"   // 属性图标路径
      }],

      // 副属性列表
      subPropList: [{
        attributeName: "暴击",        // 属性名称
        attributeValue: "8.2%",       // 属性值
        score: 12.5,                  // 权重分数 (仅权重查询模式)
        valid: "S"                    // 评级 (仅权重查询模式)
      }]
    }],

    // 角色属性 (displayMode !== "artis" 时显示)
    roleAttributeList: [{
      attributeName: "攻击力",        // 属性名称
      attributeValue: "2847",         // 属性值
      pic: "/images/attr/xxx.png",    // 属性图标路径
      valid: "S"                      // 评级 (可选)
    }],

    // 声骸属性汇总 (displayMode !== "artis" 时显示)
    equipPhantomAddPropList: [{
      attributeName: "攻击力%",       // 属性名称
      attributeValue: "43.8%",        // 属性值
      pic: "/images/attr/xxx.png",    // 属性图标路径
      valid: "S"                      // 评级 (可选)
    }]
  },

  // 权重数据 (仅 displayMode === "artis" 时)
  weights: [{
    name: "攻击力%",                  // 属性名称
    weight: 1.0,                      // 权重值
    value: 43.8,                      // 实际数值
    score: 43.8,                      // 计算分数
    scoreGrade: "S"                   // 评级
  }]
}
```

**模板使用**:
```html
<div>{{charName}} Lv.{{data.level}}</div>
<img src="{{_res_path}}{{data.pile}}">

{{if displayMode !== "artis"}}
  <!-- 显示武器、技能、命座 -->
{{/if}}

{{each data.equipPhantomList phantom}}
  <img src="{{_res_path}}{{phantom.pic}}">
  {{each phantom.subPropList subProp}}
    <span>{{subProp.attributeName}}: {{subProp.attributeValue}}</span>
    {{if subProp.score}}<span>{{subProp.score.toFixed(1)}}</span>{{/if}}
  {{/each}}
{{/each}}
```

### 2. 角色列表 (`character/profile-list`)

**调用**: `renderer.render(e, "character/profile-list", renderData)`

**关键字段**:
```javascript
{
  // 基础信息
  uid: "123456789",                   // 用户UID
  game: "ww",                         // 游戏标识
  servName: "库街区",                 // 服务器名称

  // 角色列表
  chars: [{
    id: 1404,                         // 角色ID
    name: "维拉",                     // 角色名称
    level: 90,                        // 角色等级
    star: 5,                          // 角色星级
    element: "hydro",                 // 元素类型
    cons: 6,                          // 命座数量

    // 武器信息
    weapon: {
      name: "苍鳞千嶂",               // 武器名称
      level: 90,                      // 武器等级
      star: 5                         // 武器星级
    },

    // 图片路径
    pic: "/images/face/1404.png",     // 角色头像路径
    weaponPic: "/images/weapon/xxx.png" // 武器图标路径
  }],

  // 显示信息
  hasNew: false,                      // 是否有新角色
  msg: "共 8 个角色",                 // 统计信息
  background: "bg-01.jpg",            // 背景图片

  // 更新时间
  updateTime: {
    profile: "2024-01-01 12:00:00"    // 面板更新时间
  }
}
```

**模板使用**:
```html
<div>UID: {{uid}} - {{msg}}</div>
{{each chars char}}
  <div class="char">
    <img src="{{_res_path}}{{char.pic}}">
    <div>{{char.name}} Lv.{{char.level}}</div>
  </div>
{{/each}}
```

### 3. 声骸列表 (`character/echo-list`)

**调用**: `renderer.render(e, "character/echo-list", renderData)`

**关键字段**:
```javascript
{
  // 基础信息
  uid: "123456789",                   // 用户UID
  game: "ww",                         // 游戏标识

  // 声骸列表 (使用 artis 字段名兼容 miao-plugin)
  artis: [{
    // 声骸基本信息
    name: "鸣钟之龟",                 // 声骸名称
    level: 25,                        // 声骸等级
    cost: 4,                          // 声骸费用
    star: 5,                          // 声骸星级
    pic: "/images/phantom/xxx.png",   // 声骸图标路径
    score: [85.2, "S"],               // 评分 [分数, 等级]

    // 主属性
    mainProp: {
      name: "攻击力%",                // 主属性名称
      value: "22.8%"                  // 主属性值
    },

    // 副属性列表
    subProps: [{
      name: "暴击",                   // 副属性名称
      value: "8.2%",                  // 副属性值
      valid: "S"                      // 评级
    }],

    // 装备角色信息
    character: {
      name: "维拉",                   // 角色名称
      pic: "/images/face/1404.png"    // 角色头像路径
    }
  }],

  // 属性标题映射 (用于显示简化名称)
  artisKeyTitle: {
    "攻击力": "攻击",                 // 完整名称 -> 简化名称
    "攻击力%": "攻击%",
    "暴击": "暴击",
    "暴击伤害": "爆伤"
  },

  // 可选字段
  element: "hydro",                   // 主题元素色
  background: "bg-01.jpg",            // 背景图片
  saveTime: "2024-01-01 12:00:00"     // 保存时间
}
```

**模板使用**:
```html
{{each artis arti}}
  <div class="arti">
    <img src="{{_res_path}}{{arti.pic}}">
    <div>{{arti.name}} +{{arti.level}}</div>
    <div>{{arti.score[0]}}分 - {{arti.score[1]}}</div>
    {{each arti.subProps subProp}}
      <div>{{subProp.name}}: {{subProp.value}}</div>
    {{/each}}
  </div>
{{/each}}
```

### 4. 角色卡片 (`character/ww-style`)

**调用**: `renderer.render(e, "character/ww-style", renderData)`

**关键字段**:
```javascript
{
  // 基础信息
  uid: "123456789",                   // 用户UID
  game: "ww",                         // 游戏标识
  servName: "库街区",                 // 服务器名称

  // 角色信息 (单个角色)
  char: {
    id: 1404,                         // 角色ID
    name: "维拉",                     // 角色名称
    level: 90,                        // 角色等级
    star: 5,                          // 角色星级
    element: "hydro",                 // 元素类型
    cons: 6,                          // 命座数量
    pic: "/images/face/1404.png"      // 角色头像路径
  },

  // 可选字段
  element: "hydro",                   // 主题元素色
  background: "bg-01.jpg"             // 背景图片
}
```

### 5. 练度统计 (`character/training-stat`)

**调用**: `renderer.render(e, "character/training-stat", renderData)`

**关键字段**:
```javascript
{
  // 基础信息
  uid: "123456789",                   // 用户UID
  mode: "training",                   // 显示模式

  // 角色练度数据
  chars: [{
    // 角色基本信息
    id: 1404,                         // 角色ID
    name: "维拉",                     // 角色名称
    level: 90,                        // 角色等级
    star: 5,                          // 角色星级
    element: "hydro",                 // 元素类型
    cons: 6,                          // 命座数量
    pic: "/images/face/1404.png",     // 角色头像路径

    // 武器信息
    weapon: {
      level: 90,                      // 武器等级
      star: 5,                        // 武器星级
      name: "苍鳞千嶂"                // 武器名称 (可选)
    },

    // 技能等级
    talent: {
      a: 10,                          // 普攻等级
      e: 10,                          // 技能等级
      q: 10                           // 大招等级
    },

    // 练度评分
    avgLevel: 85.5,                   // 平均等级
    totalScore: 92.8,                 // 总分
    rank: "S"                         // 评级
  }],

  // 工具函数
  pct: function(num) {                // 百分比格式化函数
    return (num * 100).toFixed(1)
  },

  // 统计信息
  summary: {
    totalChars: 8,                    // 总角色数
    avgLevel: 85.2,                   // 平均等级
    avgScore: 88.5                    // 平均分数
  }
}
```

### 6. 排行榜 (`rank/group-rank`)

**调用**: `renderer.render(e, "rank/group-rank", renderData)`

**关键字段**:
```javascript
{
  // 群组信息
  groupId: "123456789",               // 群组ID
  groupName: "测试群",                // 群组名称
  rankType: "练度排行",               // 排行类型

  // 排行数据
  rankList: [{
    rank: 1,                          // 排名
    uid: "123456789",                 // 用户UID
    nickname: "用户名",               // 用户昵称
    level: 90,                        // 等级
    score: 95.8,                      // 分数
    characterCount: 8,                // 角色数量

    // 代表角色 (可选)
    chars: [{
      name: "维拉",                   // 角色名称
      level: 90,                      // 角色等级
      star: 5,                        // 角色星级
      pic: "/images/face/1404.png"    // 角色头像路径
    }]
  }],

  // 统计信息
  updateTime: "2024-01-01 12:00:00",  // 更新时间
  totalCount: 50,                     // 总参与人数

  // 可选字段
  background: "bg-01.jpg"             // 背景图片
}
```

### 7. 全角色练度 (`training/all-character-training`)

**调用**: `renderer.render(e, "training/all-character-training", renderData)`

**关键字段**:
```javascript
{
  // 基础信息
  uid: "123456789",                   // 用户UID

  // 角色练度数据
  chars: [{
    // 角色基本信息
    name: "维拉",                     // 角色名称
    level: 90,                        // 角色等级
    star: 5,                          // 角色星级
    element: "hydro",                 // 元素类型
    pic: "/images/face/1404.png",     // 角色头像路径

    // 武器信息
    weapon: {
      name: "苍鳞千嶂",               // 武器名称
      level: 90,                      // 武器等级
      star: 5                         // 武器星级
    },

    // 声骸信息
    phantoms: [{
      name: "鸣钟之龟",               // 声骸名称
      level: 25,                      // 声骸等级
      star: 5,                        // 声骸星级
      cost: 4                         // 声骸费用 (可选)
    }],

    // 练度评分
    totalScore: 85.2,                 // 角色总分
    rank: "S",                        // 评级

    // 详细分数 (可选)
    scores: {
      weapon: 90,                     // 武器分数
      phantom: 85,                    // 声骸分数
      talent: 88                      // 技能分数
    }
  }],

  // 总体统计
  totalScore: 680.5,                  // 总分
  avgScore: 85.1,                     // 平均分
  maxScore: 95.8,                     // 最高分 (可选)
  minScore: 72.3,                     // 最低分 (可选)

  // 时间信息
  updateTime: "2024-01-01 12:00:00",  // 更新时间

  // 可选字段
  background: "bg-01.jpg"             // 背景图片
}
```

## 字段类型说明

### 基础数据类型
```javascript
// 字符串类型
uid: "123456789"                      // 9位数字字符串
name: "维拉"                          // 角色/武器/声骸名称
element: "hydro"                      // 元素类型: hydro|pyro|cryo|electro|anemo|geo

// 数值类型
level: 90                             // 整数: 1-90
star: 5                               // 整数: 1-5
score: 85.2                           // 浮点数: 0-100
cons: 6                               // 整数: 0-6

// 布尔类型
unlocked: true                        // 是否解锁
hasNew: false                         // 是否有新内容

// 数组类型
chars: []                             // 角色列表
skillList: []                         // 技能列表
subPropList: []                       // 副属性列表

// 对象类型
weapon: {}                            // 武器信息对象
data: {}                              // 角色数据对象
```

### 路径字段格式
```javascript
// 图片路径 (相对于 resources 目录)
pic: "/images/face/1404.png"          // 角色头像
pile: "/images/pile/1404.png"         // 角色立绘
weaponPic: "/images/weapon/xxx.png"   // 武器图标

// 背景图片
background: "bg-01.jpg"               // 背景文件名
```

### 评分字段格式
```javascript
// 单一评分
score: 85.2                           // 数值分数
rank: "S"                             // 评级: SSS|SS|S|A|B|C

// 复合评分
score: [85.2, "S"]                    // [分数, 评级]
valid: "S"                            // 词条评级
```

## 系统自动字段

所有模板都会自动包含：
```javascript
{
  sys: { scale: 1 },
  copyright: "Created By TRSS-Yunzai...",
  _res_path: "../../../plugins/rover-plugin/resources/",
  _miao_path: "../../../plugins/miao-plugin/resources/",
  _plugin: "rover-plugin",
  _htmlPath: "模板路径",
  saveId: "模板名称"
}
```

## 常用模板语法

### 条件显示
```html
{{if condition}}显示内容{{/if}}
{{if condition}}内容1{{else}}内容2{{/if}}
```

### 循环
```html
{{each array item}}
  <div>{{item.name}}</div>
{{/each}}

{{each array item index}}
  <div>{{index}}: {{item.name}}</div>
{{/each}}
```

### 图片引用
```html
<img src="{{_res_path}}{{item.pic}}">
```

### 数值格式化
```html
{{item.score.toFixed(1)}}
{{item.percent}}%
```

### 设置变量
```html
{{set showWeapon = displayMode !== "artis" && data.weapon}}
{{if showWeapon}}...{{/if}}
```

## 数据验证规则

### 必需字段验证
```javascript
// 基础必需字段
if (!uid || !/^\d{9}$/.test(uid)) {
  throw new Error("UID must be 9 digits")
}
if (!game || game !== "ww") {
  throw new Error("Game must be 'ww'")
}

// 角色数据验证
if (data && data.name && !data.level) {
  throw new Error("Character level is required")
}
```

### 数据类型验证
```javascript
// 数值范围验证
if (level < 1 || level > 90) {
  throw new Error("Level must be between 1-90")
}
if (star < 1 || star > 5) {
  throw new Error("Star must be between 1-5")
}

// 枚举值验证
const validElements = ["hydro", "pyro", "cryo", "electro", "anemo", "geo"]
if (element && !validElements.includes(element)) {
  throw new Error(`Invalid element: ${element}`)
}
```

### 默认值设置
```javascript
// 使用常量设置默认值
import { DEFAULT_CONFIG, GAME_CONFIG } from "../utils/constants.js"

renderData.element = renderData.element || DEFAULT_CONFIG.ELEMENT
renderData.game = renderData.game || GAME_CONFIG.GAME_ID
renderData.servName = renderData.servName || GAME_CONFIG.DEFAULT_SERVER

// 数组字段默认值
renderData.chars = renderData.chars || []
renderData.artis = renderData.artis || []
```

### 模板端安全访问
```html
<!-- 对象安全访问 -->
{{if data && data.weapon}}
  <div>{{data.weapon.name}}</div>
{{/if}}

<!-- 数组安全访问 -->
{{if chars && chars.length > 0}}
  {{each chars char}}
    <div>{{char.name || "未知角色"}}</div>
  {{/each}}
{{else}}
  <div>暂无角色数据</div>
{{/if}}

<!-- 数值安全显示 -->
{{if score !== undefined}}
  <span>{{score.toFixed(1)}}分</span>
{{else}}
  <span>未评分</span>
{{/if}}
```

## 调试技巧

### 1. 数据输出
```html
<!-- 调试信息 -->
<!-- DEBUG: uid={{uid}}, mode={{displayMode}} -->
```

### 2. 条件调试
```html
{{if data.equipPhantomList}}
  <!-- 有数据: {{data.equipPhantomList.length}} 个声骸 -->
{{else}}
  <!-- 无声骸数据 -->
{{/if}}
```

### 3. JavaScript 日志
```javascript
console.log(`[模块] 渲染数据:`, {
  uid: renderData.uid,
  keys: Object.keys(renderData)
})
```

## 常见错误

### 1. 图片路径错误
```html
<!-- ❌ 错误 -->
<img src="{{data.pile}}">

<!-- ✅ 正确 -->
<img src="{{_res_path}}{{data.pile}}">
```

### 2. 数据访问错误
```html
<!-- ❌ 可能出错 -->
<div>{{data.weapon.name}}</div>

<!-- ✅ 安全访问 -->
{{if data.weapon}}<div>{{data.weapon.name}}</div>{{/if}}
```

### 3. 循环错误
```html
<!-- ❌ 数组可能为空 -->
{{each data.list item}}...{{/each}}

<!-- ✅ 检查后循环 -->
{{if data.list && data.list.length > 0}}
  {{each data.list item}}...{{/each}}
{{/if}}
```

## 使用常量的最佳实践

### 1. 模板路径使用常量
```javascript
// ✅ 推荐：使用常量
import { TEMPLATE_PATHS } from "../utils/constants.js"

const img = await renderer.render(e, TEMPLATE_PATHS.CHARACTER_DETAIL, renderData)
const img2 = await renderer.render(e, TEMPLATE_PATHS.ECHO_LIST, renderData)

// ❌ 不推荐：硬编码路径
const img = await renderer.render(e, "character/profile-detail", renderData)
```

### 2. 配置值使用常量
```javascript
// ✅ 推荐：使用常量
import { DEFAULT_CONFIG, DISPLAY_MODES, GAME_CONFIG } from "../utils/constants.js"

renderData.element = DEFAULT_CONFIG.ELEMENT
renderData.displayMode = DISPLAY_MODES.ARTIS
renderData.game = GAME_CONFIG.GAME_ID

// ❌ 不推荐：硬编码值
renderData.element = "hydro"
renderData.displayMode = "artis"
renderData.game = "ww"
```

### 3. 错误消息使用常量
```javascript
// ✅ 推荐：使用常量
import { ERROR_MESSAGES } from "../utils/constants.js"

if (!user) {
  await e.reply(ERROR_MESSAGES.USER_NOT_FOUND)
  return false
}

// ❌ 不推荐：硬编码消息
if (!user) {
  await e.reply("❌ 获取用户信息失败")
  return false
}
```

## 性能建议

1. **预处理数据**: 在 JavaScript 中完成复杂计算
2. **避免深层嵌套**: 减少模板中的复杂逻辑
3. **合理缓存**: 重用处理过的数据
4. **图片优化**: 确保图片路径正确且文件存在
5. **使用常量**: 避免硬编码，提高代码可维护性
