/**
 * 接口测试脚本 - 测试所有API接口功能
 * 使用真实token和did进行完整测试，不预定义UID
 */

import { RoverAPI, UserBinding, KuroAPI, Database } from "./lib/api.js"

// 测试配置 - 使用您提供的真实token和did
const TEST_CONFIG = {
  userId: "test_user_12345",
  token:
    "eyJhbGciOiJIUzI1NiJ9.eyJjcmVhdGVkIjoxNzUzNjMxMjg2Njc1LCJ1c2VySWQiOjEwODE2NzMyfQ.Z9BLJ1jtanrNfl1JLvcifr8ypSXEu7513sQvy9I-A1c",
  did: "6F02FE7B671ACA64694F19FB67EBEBAD07659846",
}

// 测试结果统计
let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: [],
}

// 测试工具函数
function logTest(name, success, message = "") {
  testResults.total++
  if (success) {
    testResults.passed++
    console.log(`✅ ${name}: 通过 ${message}`)
  } else {
    testResults.failed++
    testResults.errors.push(`${name}: ${message}`)
    console.log(`❌ ${name}: 失败 ${message}`)
  }
}

async function safeTest(name, testFn) {
  try {
    const result = await testFn()
    logTest(name, true, result || "")
    return result
  } catch (error) {
    logTest(name, false, error.message)
    return null
  }
}

async function testBasicAPIs() {
  console.log("\n🔧 === 基础API测试 ===")

  // 测试用户绑定检查
  await safeTest("RoverAPI.isUserBound", async () => {
    const result = await RoverAPI.isUserBound(TEST_CONFIG.userId)
    return `返回: ${result}`
  })

  await safeTest("RoverAPI.checkUserBinding", async () => {
    const result = await RoverAPI.checkUserBinding(TEST_CONFIG.userId)
    return `绑定状态: ${result.hasBinding}, Token: ${result.hasToken}, 账号: ${result.hasValidAccount}`
  })

  // 测试基础信息获取
  await safeTest("RoverAPI.getUserUid", async () => {
    const result = await RoverAPI.getUserUid(TEST_CONFIG.userId)
    return `UID: ${result || "无"}`
  })

  await safeTest("RoverAPI.getUserToken", async () => {
    const result = await RoverAPI.getUserToken(TEST_CONFIG.userId)
    return `Token: ${result ? "已获取" : "无"}`
  })

  await safeTest("RoverAPI.getUserBatToken", async () => {
    const result = await RoverAPI.getUserBatToken(TEST_CONFIG.userId)
    return `BatToken: ${result ? "已获取" : "无"}`
  })
}

async function testUserBindingAPIs() {
  console.log("\n👤 === UserBinding类API测试 ===")

  await safeTest("UserBinding.isUserBound", async () => {
    const result = await UserBinding.isUserBound(TEST_CONFIG.userId)
    return `绑定状态: ${result}`
  })

  await safeTest("UserBinding.checkUserBinding", async () => {
    const result = await UserBinding.checkUserBinding(TEST_CONFIG.userId)
    return `详细绑定信息获取成功`
  })

  await safeTest("UserBinding.getUserUid", async () => {
    const result = await UserBinding.getUserUid(TEST_CONFIG.userId)
    return `UID: ${result || "无"}`
  })

  await safeTest("UserBinding.getUserToken", async () => {
    const result = await UserBinding.getUserToken(TEST_CONFIG.userId)
    return `Token: ${result ? "已获取" : "无"}`
  })

  await safeTest("UserBinding.getUserBatToken", async () => {
    const result = await UserBinding.getUserBatToken(TEST_CONFIG.userId)
    return `BatToken: ${result ? "已获取" : "无"}`
  })
}

async function testKuroAPIs() {
  console.log("\n🎮 === KuroAPI类测试 ===")

  await safeTest("KuroAPI.refreshUserPanel", async () => {
    const result = await KuroAPI.refreshUserPanel(TEST_CONFIG.userId)
    return `刷新结果: ${result.success ? "成功" : "失败"} - ${result.message}`
  })

  // BatToken刷新现在由内部自动处理，不需要外部调用

  await safeTest("KuroAPI.getUserGameData (基础)", async () => {
    const result = await KuroAPI.getUserGameData(TEST_CONFIG.userId, false)
    return `基础游戏数据: ${result.success ? "成功" : "失败"} - ${result.message}`
  })

  await safeTest("KuroAPI.getUserGameData (详细)", async () => {
    const result = await KuroAPI.getUserGameData(TEST_CONFIG.userId, true)
    return `详细游戏数据: ${result.success ? "成功" : "失败"} - ${result.message}`
  })
}

async function testDatabaseAPIs() {
  console.log("\n💾 === Database类测试 ===")

  await safeTest("Database.WavesUser.getPrimaryAccount", async () => {
    const result = await Database.WavesUser.getPrimaryAccount(TEST_CONFIG.userId)
    return `主账号: ${result ? "已获取" : "无"}`
  })

  await safeTest("Database.WavesBind.getUidByUser", async () => {
    const result = await Database.WavesBind.getUidByUser(TEST_CONFIG.userId)
    return `绑定UID: ${result || "无"}`
  })
}

async function testAdvancedAPIs() {
  console.log("\n🚀 === 智能API调用测试 ===")

  // 测试智能API调用方法
  await safeTest("RoverAPI.getBaseData", async () => {
    const result = await RoverAPI.getBaseData(TEST_CONFIG.userId)
    return `基础数据: ${result.success ? "成功" : "失败"} - ${result.message}`
  })

  await safeTest("RoverAPI.getRoleData", async () => {
    const result = await RoverAPI.getRoleData(TEST_CONFIG.userId)
    return `角色数据: ${result.success ? "成功" : "失败"} - ${result.message}`
  })

  await safeTest("RoverAPI.getExploreData", async () => {
    const result = await RoverAPI.getExploreData(TEST_CONFIG.userId)
    return `探索数据: ${result.success ? "成功" : "失败"} - ${result.message}`
  })

  await safeTest("RoverAPI.getChallengeData", async () => {
    const result = await RoverAPI.getChallengeData(TEST_CONFIG.userId)
    return `挑战数据: ${result.success ? "成功" : "失败"} - ${result.message}`
  })

  await safeTest("RoverAPI.getRoleList", async () => {
    const result = await RoverAPI.getRoleList(TEST_CONFIG.userId)
    return `角色列表: ${result.success ? "成功" : "失败"} - ${result.message}`
  })

  console.log("\n🚀 === 高级组合API测试 ===")

  await safeTest("RoverAPI.getUserCompleteData", async () => {
    const result = await RoverAPI.getUserCompleteData(TEST_CONFIG.userId)
    return `完整数据: ${result.success ? "成功" : "失败"} - ${result.message}`
  })

  await safeTest("RoverAPI.getCalculatorData", async () => {
    const result = await RoverAPI.getCalculatorData(TEST_CONFIG.userId)
    return `计算器数据: ${result.success ? "成功" : "失败"} - ${result.message}`
  })

  await safeTest("RoverAPI.getResourceReports", async () => {
    const result = await RoverAPI.getResourceReports(TEST_CONFIG.userId)
    return `资源简报: ${result.success ? "成功" : "失败"} - ${result.message}`
  })
}

async function printTestSummary() {
  console.log("\n📊 === 测试结果汇总 ===")
  console.log(`总测试数: ${testResults.total}`)
  console.log(`通过: ${testResults.passed}`)
  console.log(`失败: ${testResults.failed}`)
  console.log(`成功率: ${((testResults.passed / testResults.total) * 100).toFixed(2)}%`)

  if (testResults.errors.length > 0) {
    console.log("\n❌ 失败的测试:")
    testResults.errors.forEach(error => console.log(`  - ${error}`))
  }

  console.log(
    testResults.failed === 0 ? "\n🎉 所有测试通过！" : "\n⚠️ 部分测试失败，请检查上述错误",
  )
}

// 绑定测试用户数据
async function bindTestUser() {
  console.log("\n🔗 === 绑定测试用户数据 ===")

  try {
    // 首先获取角色列表来获取UID
    console.log("正在获取角色列表...")
    const KuroApiModule = await import("./components/api/kuroapi.js")
    const KuroApi = KuroApiModule.default
    const api = new KuroApi()
    const roleListResult = await api.getRoleList(TEST_CONFIG.token, TEST_CONFIG.did)

    if (roleListResult.code === 200 && roleListResult.data && roleListResult.data.length > 0) {
      const firstRole = roleListResult.data[0]
      const uid = firstRole.roleId
      console.log(`✅ 获取到UID: ${uid}`)

      // 绑定用户数据到数据库
      const { WavesUser, WavesBind } = Database

      // 创建或更新用户记录
      await WavesUser.create({
        user_id: TEST_CONFIG.userId,
        uid: uid,
        cookie: TEST_CONFIG.token,
        did: TEST_CONFIG.did,
        platform: "ios",
        status: "正常",
      })

      // 创建或更新绑定记录
      await WavesBind.create({
        user_id: TEST_CONFIG.userId,
        uid: uid,
        is_default: 1,
      })

      console.log(`✅ 用户数据绑定成功: ${TEST_CONFIG.userId} -> ${uid}`)
      return uid
    } else {
      console.log(`❌ 获取角色列表失败:`, roleListResult)
      return null
    }
  } catch (error) {
    console.error("❌ 绑定用户数据失败:", error)
    return null
  }
}

// 主测试函数
async function runAllTests() {
  console.log("🧪 开始Rover Plugin接口完整测试...")
  console.log(`测试用户ID: ${TEST_CONFIG.userId}`)
  console.log(`使用Token: ${TEST_CONFIG.token.substring(0, 20)}...`)
  console.log(`使用DID: ${TEST_CONFIG.did}`)

  try {
    // 先绑定测试用户数据
    const uid = await bindTestUser()
    if (!uid) {
      console.log("❌ 无法绑定用户数据，跳过需要绑定的测试")
    }

    // 运行所有测试
    await testBasicAPIs()
    await testUserBindingAPIs()
    await testKuroAPIs()
    await testDatabaseAPIs()
    await testAdvancedAPIs()

    await printTestSummary()
  } catch (error) {
    console.error("❌ 测试过程中发生严重错误:", error)
  }
}

// 运行测试
runAllTests()
