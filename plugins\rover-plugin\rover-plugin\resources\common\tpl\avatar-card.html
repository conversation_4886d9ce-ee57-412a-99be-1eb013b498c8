{{set avatar = $data[0] || false }}
{{set {_res_path, cardType} = $data[1]}}
{{set weapon = avatar.weapon || {} }}
{{set talentMap = ['a','e','q'] }}

<div class="avatar-card elem-{{avatar.elem}} avatar-{{avatar.name}} card-{{cardType||'mini'}}">
  <div class="card">
    {{if avatar}}
    <div class="avatar-face item-icon star{{avatar.star==4?4:5}}">
      <div class="img mini" style="background-image:url({{_res_path}}{{avatar.face}})"></div>
      <div class="img wide avatar-{{avatar.name}}" style="background-image:url({{_res_path}}{{avatar.gacha}})"></div>
      {{if avatar.is_popularity}}
        <span class="popularity">UP!</span>
      {{/if}}
      <span class="cons cons-{{avatar.cons}} mini">{{avatar.cons}}</span>
      
      <div class="avatar-level"><span>Lv</span>{{avatar.level}} {{avatar.abbr}}</div>
    </div>
    <div class="avatar-info">
      {{set talent = avatar.talent || {} }}

      <div class="avatar-name wide">
        <strong>{{avatar.abbr}}</strong>
        <div class="lv-info">
          <span class="cons cons-{{avatar.cons}}">{{avatar.cons}}</span>
          <span class="avatar-level">Lv{{avatar.level}}</span>
        </div>
        <div class="line"></div>
      </div>
      {{if talent.a && talent.a.level }}
      <div class="avatar-talent">
        {{each talentMap k}}
        {{set t = talent[k] || {} }}
        <span class="talent-item talent-{{k}} talent-{{t.original===10?'crown':'none'}} talent-{{t.level>t.original?'plus':'none'}}">{{t.level}}</span>
        {{/each}}
      </div>
      {{else}}
      <div class="avatar-talent no-talent"><span>暂无天赋数据</span></div>
      {{/if}}
      <div class="line"></div>
      <div class="avatar-detail">
        <div class="item avatar-weapon">
          <div class="item-icon star{{weapon.star}}">
            <span class="img" style="background-image:url({{_res_path}}{{weapon.img}})"></span>
            <span class="cons cons-{{weapon.affix > 4 ? weapon.affix + 1 : weapon.affix}}">{{weapon.affix}}</span>
          </div>
        </div>
        <div class="item avatar-artis artis{{avatar.artisSet?.names?.length}}">
          <div class="artis item-icon">
            {{each avatar.artisSet?.imgs img}}
            <div class="img" style="background-image:url({{_res_path}}{{img}})"></div>
            {{/each}}
          </div>
          <div class="item-info wide"></div>
        </div>
      </div>
    </div>
    {{/if}}
  </div>
</div>