import path from "path"
import fs from "node:fs"
import lodash from "lodash"
import fetch from "node-fetch"
import { dataPath } from "./path.js"

const Background = {
  /**
   * 获取背景图
   * @param {string} cfg - 配置类型 (list/profile)
   * @returns {Object} 背景图对象
   */
  async getBackground(cfg) {
    // 默认使用随机本地背景图
    const def_background = 4 // 4:图片api/图链请求失败后使用本地随机图
    
    if (def_background == 0) return
    
    let background = { url: "", text: "" }
    let tip = false
    
    if (def_background > 2) {
      // 可以在这里添加API背景图请求
      // 目前直接使用本地背景图
      tip = true
    }
    
    if (!background.url) {
      background.url = Background.getDefBackground(def_background, cfg, tip)
    }
    
    // 生成背景CSS
    const filter = cfg === "list" ? 7 : 7 // 模糊度
    background.text = `<style>.background{position:absolute;background-image:url(${background.url});background-size:cover;width:100%;height:100%;filter:blur(${filter}px);}</style><div class="background"></div>`
    
    return background
  },

  /**
   * 获取默认背景图
   * @param {number} def_background - 背景模式
   * @param {string} cfg - 配置类型
   * @param {boolean} tip - 是否显示提示
   * @returns {string} 背景图路径
   */
  getDefBackground(def_background, cfg, tip = false) {
    if (tip) {
      console.log(`[鸣潮:背景][${cfg}] 使用本地背景图`)
    }
    
    let image = "bg-electro.webp" // 默认背景图
    
    if (def_background % 2 === 0) {
      // 随机选择背景图
      const bgPath = path.join(process.cwd(), "plugins/rover-plugin/resources/common/bg")
      
      if (fs.existsSync(bgPath)) {
        const imgs = fs.readdirSync(bgPath).filter(i => 
          !fs.statSync(path.join(bgPath, i)).isDirectory() && 
          /\.(png|jpg|webp)$/.test(path.extname(i)) &&
          i.startsWith("bg-") // 只选择bg-开头的背景图
        )
        
        if (imgs.length !== 0) {
          image = lodash.sample(imgs)
        }
      }
    }
    
    console.log(`[鸣潮:背景][${cfg}] 使用本地背景图 ${image}`)
    return `../../../../../plugins/rover-plugin/resources/common/bg/${image}`
  },

  /**
   * 创建更多鸣潮风格的背景图
   */
  async createWutheringWavesBackgrounds() {
    const bgPath = path.join(process.cwd(), "plugins/rover-plugin/resources/common/bg")
    
    // 检查是否需要创建更多背景图
    if (fs.existsSync(bgPath)) {
      const existingBgs = fs.readdirSync(bgPath).filter(i => 
        i.startsWith("bg-") && /\.(png|jpg|webp)$/.test(path.extname(i))
      )
      
      console.log(`[鸣潮:背景] 当前可用背景图: ${existingBgs.length} 张`)
      console.log(`[鸣潮:背景] 背景图列表: ${existingBgs.join(", ")}`)
    }
  }
}

export default Background
