import express from "express"
import http from "http"
import path from "path"
import fs from "fs"
import crypto from "crypto"
import { fileURLToPath } from "url"
import config from "./config.js"
import KuroApi from "./api/kuroapi.js"
import User from "./User.js"

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 网页登录服务器
export class WebLoginServer {
  constructor() {
    this.app = null
    this.server = null
    this.isRunning = false
    this.pendingLogins = new Map() // 存储待处理的登录请求
    this.tokenUserMap = new Map() // 存储token到用户ID的映射
  }

  /**
   * 启动本地登录服务器
   */
  async startLocalServer() {
    if (this.isRunning) {
      console.log("⚠️ 本地登录服务器已在运行")
      return false
    }

    try {
      const host = config.getLocalLoginHost()
      const port = config.getLocalLoginPort()

      this.app = express()

      // 中间件配置
      this.app.use(express.json())
      this.app.use(express.urlencoded({ extended: true }))
      this.app.use(express.static(path.join(__dirname, "../resources/web")))

      // 验证码测试页面
      this.app.get("/test-sms-real", (req, res) => {
        const testPagePath = path.join(__dirname, "../test-sms-real.html")
        res.sendFile(testPagePath)
      })

      // 设置路由
      this.setupRoutes()

      // 启动服务器
      this.server = http.createServer(this.app)

      await new Promise((resolve, reject) => {
        this.server.listen(port, host, err => {
          if (err) {
            reject(err)
          } else {
            this.isRunning = true
            console.log(`✅ 本地登录服务器已启动: http://${host}:${port}`)
            console.log(`📱 登录页面: http://${host}:${port}/login`)
            resolve()
          }
        })
      })

      return true
    } catch (error) {
      console.error("❌ 启动本地登录服务器失败:", error)
      return false
    }
  }

  /**
   * 停止本地登录服务器
   */
  async stopLocalServer() {
    if (!this.isRunning || !this.server) {
      return true
    }

    try {
      await new Promise(resolve => {
        this.server.close(() => {
          this.isRunning = false
          this.server = null
          this.app = null
          console.log("✅ 本地登录服务器已停止")
          resolve()
        })
      })
      return true
    } catch (error) {
      console.error("❌ 停止本地登录服务器失败:", error)
      return false
    }
  }

  /**
   * 设置路由
   */
  setupRoutes() {
    // 登录页面
    this.app.get("/login", (req, res) => {
      const loginToken = req.query.id || "default"

      // 首先检查token是否在tokenUserMap中（验证token有效性）
      const tokenData = this.tokenUserMap.get(loginToken)
      if (!tokenData) {
        // token不存在，显示过期页面
        const expiredPage = this.getExpiredPage()
        return res.send(expiredPage)
      }

      // 检查token是否过期
      if (tokenData.expireTime < Date.now()) {
        // token已过期，清理并显示过期页面
        this.tokenUserMap.delete(loginToken)
        this.pendingLogins.delete(loginToken)
        const expiredPage = this.getExpiredPage()
        return res.send(expiredPage)
      }

      // 检查登录是否已完成
      const loginData = this.pendingLogins.get(loginToken)
      if (loginData && loginData.status === "completed") {
        // 登录已完成，显示过期页面并跳转
        const expiredPage = this.getExpiredPage()
        return res.send(expiredPage)
      }

      // token有效且登录未完成，显示登录页面
      const htmlContent = this.getLoginPage(loginToken)
      res.send(htmlContent)
    })

    // 登录API - 用户自行获取验证码，直接提交登录
    this.app.post("/api/login", async (req, res) => {
      let loginKey = null
      try {
        const { mobile, code, loginId } = req.body

        if (!mobile || !code) {
          return res.json({ success: false, message: "请输入手机号和验证码" })
        }

        // 防止重复登录请求
        loginKey = `${loginId}_${mobile}_${code}`
        if (this.activeLogins && this.activeLogins.has(loginKey)) {
          return res.json({ success: false, message: "登录请求正在处理中，请勿重复提交" })
        }

        // 标记登录请求为处理中
        if (!this.activeLogins) {
          this.activeLogins = new Map()
        }
        this.activeLogins.set(loginKey, Date.now())

        // 设置超时清理
        setTimeout(() => {
          if (this.activeLogins) {
            this.activeLogins.delete(loginKey)
          }
        }, 30000) // 30秒后清理

        // 使用异步处理优化性能
        const result = await this.processLoginAsync(mobile, code, loginId)

        return res.json(result)
      } catch (error) {
        console.error("登录错误:", error)
        res.json({ success: false, message: "登录失败，请稍后重试" })
      } finally {
        // 清理登录标记
        if (this.activeLogins && loginKey) {
          this.activeLogins.delete(loginKey)
        }
      }
    })

    // 确认绑定API
    this.app.post("/api/confirm", async (req, res) => {
      try {
        const { loginId, roleIndex } = req.body

        // 根据loginId获取真实的用户ID
        const realUserId = this.getUserIdByToken(loginId)
        if (!realUserId) {
          return res.json({ success: false, message: "登录信息已过期，请重新登录" })
        }

        const loginResult = this.pendingLogins.get(loginId)
        if (!loginResult) {
          return res.json({ success: false, message: "登录信息已过期，请重新登录" })
        }

        if (roleIndex < 0 || roleIndex >= loginResult.roles.length) {
          return res.json({ success: false, message: "角色选择错误" })
        }

        const selectedRole = loginResult.roles[roleIndex]

        // 使用真实的用户ID存储绑定结果
        const bindResult = {
          userId: realUserId, // 真实的Bot用户ID
          loginResult,
          selectedRole,
          timestamp: Date.now(),
        }

        // 存储绑定结果，供Bot端查询
        this.storeBindResult(bindResult)

        // 设置登录状态为完成，而不是直接删除（用于状态检查）
        this.pendingLogins.set(loginId, {
          ...loginResult,
          status: "completed",
          result: {
            userName: loginResult.userName,
            roleName: selectedRole.roleName,
            roleId: selectedRole.roleId,
            gameLevel: selectedRole.gameLevel,
          },
        })

        // 延迟清理（给状态检查一些时间）
        setTimeout(() => {
          this.pendingLogins.delete(loginId)
          this.tokenUserMap.delete(loginId)
        }, 10000) // 10秒后清理

        // 返回成功响应
        res.json({
          success: true,
          message: "绑定成功",
          data: {
            userName: loginResult.userName,
            roleName: selectedRole.roleName,
            roleId: selectedRole.roleId,
            gameLevel: selectedRole.gameLevel,
          },
        })
      } catch (error) {
        console.error("确认绑定错误:", error)
        res.json({ success: false, message: "绑定失败，请稍后重试" })
      }
    })

    // 登录状态检查 - 与WutheringWavesUID保持一致
    this.app.get("/api/status/:loginId", (req, res) => {
      const loginId = req.params.loginId

      if (!loginId) {
        return res.json({ success: false, message: "缺少登录ID" })
      }

      // 检查登录状态
      const loginData = this.pendingLogins.get(loginId)
      const tokenData = this.tokenUserMap.get(loginId)

      // 检查token是否过期
      if (tokenData && tokenData.expireTime < Date.now()) {
        this.tokenUserMap.delete(loginId)
        this.pendingLogins.delete(loginId)
        return res.json({ success: false, message: "登录链接已过期", expired: true })
      }

      if (loginData && loginData.status === "completed") {
        res.json({
          success: true,
          status: "completed",
          message: "登录成功",
          data: loginData.result,
        })
      } else if (loginData) {
        res.json({
          success: true,
          status: "pending",
          message: "等待登录确认",
        })
      } else {
        res.json({
          success: false,
          status: "not_found",
          message: "登录会话不存在或已过期",
        })
      }
    })

    // 404页面处理
    this.app.get("/404", (req, res) => {
      const html404 = this.get404Page()
      res.status(404).send(html404)
    })

    // 远程登录结果回调接口
    this.app.post("/api/remote-login-callback", async (req, res) => {
      try {
        const { loginId, success, data, message } = req.body

        if (!loginId) {
          return res.json({ success: false, message: "缺少登录ID" })
        }

        // 检查登录ID是否有效
        const tokenData = this.tokenUserMap.get(loginId)
        if (!tokenData) {
          return res.json({ success: false, message: "登录ID无效或已过期" })
        }

        if (success && data) {
          // 登录成功，处理登录数据
          const loginResult = {
            userId: tokenData.userId,
            botId: tokenData.botId,
            token: data.token,
            did: data.did,
            userName: data.userName,
            roles: data.roles,
            loginTime: new Date().toISOString(),
            status: "completed",
          }

          // 保存登录结果
          this.pendingLogins.set(loginId, loginResult)

          // 保存到数据库
          await this.saveToDatabase(loginResult, loginId)

          // 异步获取bat token
          if (data.roles && data.roles.length > 0) {
            this.getBatTokenAsync(data.roles[0], data.token, data.did, loginId)
          }

          console.log(`✅ 远程登录成功: 用户=${data.userName}, UID=${data.roles?.[0]?.roleId}`)

          return res.json({ success: true, message: "登录结果已处理" })
        } else {
          // 登录失败
          console.log(`❌ 远程登录失败: ${message}`)
          return res.json({ success: true, message: "登录失败已记录" })
        }
      } catch (error) {
        console.error("处理远程登录回调失败:", error)
        return res.json({ success: false, message: "处理失败" })
      }
    })

    // 处理所有未匹配的路由
    this.app.use((req, res) => {
      const html404 = this.get404Page()
      res.status(404).send(html404)
    })
  }

  /**
   * 获取登录页面HTML
   */
  getLoginPage(loginToken) {
    try {
      // 读取HTML模板文件
      const templatePath = path.join(__dirname, "../resources/web/login.html")
      let htmlContent = fs.readFileSync(templatePath, "utf8")

      // 从token映射中获取用户ID
      const tokenData = this.tokenUserMap.get(loginToken)
      const displayUserId = tokenData ? tokenData.userId : loginToken

      // 替换模板变量：显示用户ID作为识别码，但使用loginToken作为实际标识
      htmlContent = htmlContent.replace(/\{\{loginId\}\}/g, displayUserId)
      htmlContent = htmlContent.replace(/\{\{loginToken\}\}/g, loginToken)

      return htmlContent
    } catch (error) {
      console.error("读取登录页面模板失败:", error)
      // 如果读取失败，返回简单的错误页面
      return this.getErrorPage("登录页面加载失败")
    }
  }

  /**
   * 异步处理登录流程 - 按照WutheringWavesUID的方式
   */
  async processLoginAsync(mobile, code, loginId) {
    try {
      const kuroApi = new KuroApi()

      console.log(`🔐 开始登录流程: 手机号=${mobile}`)

      // 预先验证登录信息
      const loginResponse = await this.validateLoginCredentials(mobile, code)
      if (!loginResponse.valid) {
        return { success: false, message: loginResponse.message || "登录信息验证失败" }
      }

      // 生成正确格式的DID - 按照WutheringWavesUID的方式
      const did = await kuroApi.getDevCode()
      console.log(`🔐 设备ID=${did}`)

      // 执行实际登录
      const actualLoginResponse = await kuroApi.sdkLogin(mobile, code, did)
      console.log(`🔐 登录响应: code=${actualLoginResponse.code}`)

      if (actualLoginResponse.code === 200 && actualLoginResponse.data) {
        const { token, userId, userName } = actualLoginResponse.data
        console.log(`✅ 登录成功: 用户=${userName}, ID=${userId}`)

        // 异步获取角色列表，不阻塞响应
        this.processRoleDataAsync(token, did, userId, userName, loginId)

        return {
          success: true,
          message: "登录成功，数据已自动保存",
          data: {
            userName,
            userId,
            loginId,
          },
          redirect: {
            url: "https://github.com/tyql688/WutheringWavesUID",
            delay: 3000,
            message: "登录成功！数据已保存，3秒后将跳转到项目主页...",
          },
        }
      } else {
        const errorMsg = actualLoginResponse.msg || "登录失败"
        console.error(`❌ 登录失败: ${errorMsg}`)
        return { success: false, message: errorMsg }
      }
    } catch (error) {
      console.error("登录处理异常:", error)
      return { success: false, message: "登录处理异常，请重试" }
    }
  }

  /**
   * 验证登录凭据（预检查）
   */
  async validateLoginCredentials(mobile, code) {
    // 基本格式验证
    if (!/^1[3-9]\d{9}$/.test(mobile)) {
      return { valid: false, message: "手机号格式不正确" }
    }

    if (!/^\d{6}$/.test(code)) {
      return { valid: false, message: "验证码格式不正确" }
    }

    return { valid: true }
  }

  /**
   * 异步处理角色数据和数据库保存
   */
  async processRoleDataAsync(token, did, userId, userName, loginId) {
    try {
      const kuroApi = new KuroApi()

      console.log(`🎮 异步获取角色列表...`)
      const roleResponse = await kuroApi.getRoleList(token, did)

      if (roleResponse.code === 200 && roleResponse.data && roleResponse.data.length > 0) {
        const loginResult = {
          token,
          userId,
          userName,
          did,
          bat: "", // 初始为空，后续异步获取
          roles: roleResponse.data,
          loginTime: new Date().toISOString(),
        }

        this.pendingLogins.set(loginId, loginResult)

        // 并行执行数据库保存和bat token获取，提高性能
        const startTime = Date.now()
        const [saveResult, batResult] = await Promise.allSettled([
          this.saveToDatabase(loginResult, loginId),
          this.getBatTokenAsync(roleResponse.data[0], token, did, loginId),
        ])

        const endTime = Date.now()
        console.log(`🚀 数据库保存和bat token获取已并行完成，耗时 ${endTime - startTime}ms`)

        // 检查结果
        if (saveResult.status === "rejected") {
          console.error("❌ 数据库保存失败:", saveResult.reason)
        }
        if (batResult.status === "rejected") {
          console.error("❌ bat token获取失败:", batResult.reason)
        }
      }
    } catch (error) {
      console.error("异步处理角色数据失败:", error)
    }
  }

  /**
   * 保存登录数据到数据库（改进版：确保基础信息必须保存）
   */
  async saveToDatabase(loginResult, loginId) {
    try {
      // 获取实际的用户ID（从token映射）
      const tokenData = this.tokenUserMap.get(loginId)
      const actualUserId = tokenData ? tokenData.userId : "unknown"
      const botId = tokenData ? tokenData.botId : "default"

      console.log(`💾 开始保存登录数据: 用户=${actualUserId}, Bot=${botId}`)

      const User = (await import("../components/User.js")).User
      const user = new User(actualUserId, botId)

      // 第一步：保存基础登录信息（token、did等），这是最重要的
      let saveSuccess = false
      try {
        saveSuccess = await user.saveLoginInfo({
          token: loginResult.token,
          did: loginResult.did,
          bat: loginResult.bat || "", // bat可能为空，后续异步获取
          userId: loginResult.userId,
          userName: loginResult.userName,
          roles: loginResult.roles,
          loginTime: loginResult.loginTime,
        })

        if (saveSuccess) {
          console.log(`✅ 基础登录信息已保存到数据库`)
          console.log(`👤 用户: ${loginResult.userName} (${loginResult.userId})`)
          console.log(`🔑 Token: ${loginResult.token.substring(0, 20)}...`)
          console.log(`📱 DID: ${loginResult.did}`)
        } else {
          console.error(`❌ 保存基础登录信息失败`)
          return // 基础信息保存失败，直接返回
        }
      } catch (error) {
        console.error(`❌ 保存基础登录信息时发生错误:`, error)
        return // 基础信息保存失败，直接返回
      }

      // 第二步：尝试绑定默认角色（如果失败不影响基础信息）
      if (loginResult.roles && loginResult.roles.length > 0) {
        try {
          const defaultRole = loginResult.roles[0]
          await user.bindUid(defaultRole.roleId)
          console.log(`🎮 绑定角色成功: ${defaultRole.roleName} (${defaultRole.roleId})`)

          // 第三步：尝试自动刷新面板数据（如果失败不影响前面的保存）
          try {
            this.autoRefreshPanel(actualUserId, defaultRole.roleId)
            console.log(`🔄 已启动自动刷新面板`)
          } catch (refreshError) {
            console.warn(`⚠️ 自动刷新面板失败，但不影响登录数据保存:`, refreshError.message)
          }
        } catch (bindError) {
          console.warn(`⚠️ 绑定角色失败，但基础登录信息已保存:`, bindError.message)
        }
      } else {
        console.warn(`⚠️ 没有角色信息，跳过角色绑定`)
      }

      console.log(`✅ 登录数据处理完成`)
    } catch (error) {
      console.error(`❌ 保存登录信息时发生未预期错误:`, error)
    }
  }

  /**
   * 自动刷新面板数据（检查刷新间隔）
   */
  async autoRefreshPanel(userId, roleId) {
    try {
      console.log(`🔄 自动刷新面板数据: 用户=${userId}, 角色=${roleId}`)

      // 检查刷新间隔
      const config = (await import("./config.js")).default
      const refreshInterval = config.getRefreshInterval()

      if (refreshInterval > 0) {
        // 有刷新间隔限制，检查是否在冷却期
        const lastRefreshKey = `lastRefresh_${userId}`
        const lastRefreshTime = global[lastRefreshKey] || 0
        const now = Date.now()
        const timeDiff = (now - lastRefreshTime) / 1000

        if (timeDiff < refreshInterval) {
          const remainingTime = Math.ceil(refreshInterval - timeDiff)
          console.log(`⏰ 用户 ${userId} 刷新面板冷却中，剩余 ${remainingTime}s`)
          return
        }
      }

      // 动态导入 Refresh 类
      const { Refresh } = await import("../apps/refresh.js")
      const refreshInstance = new Refresh()

      // 创建模拟事件对象
      const mockEvent = {
        user_id: userId,
        bot: { uin: "default" },
        self_id: "default",
        msg: "刷新面板",
        reply: msg => {
          console.log(`📢 自动刷新消息: ${msg}`)
        },
      }

      // 执行刷新
      await refreshInstance.refreshAllPanel(mockEvent)

      // 更新最后刷新时间
      global[`lastRefresh_${userId}`] = Date.now()
      console.log(`✅ 自动刷新面板完成`)
    } catch (error) {
      console.error(`❌ 自动刷新面板失败:`, error)
    }
  }

  /**
   * 异步获取bat token，不阻塞登录响应
   */
  async getBatTokenAsync(role, token, did, loginId) {
    try {
      console.log(`🔑 异步获取bat token: 角色=${role.roleName} (${role.roleId})`)
      const kuroApi = new (await import("./api/kuroapi.js")).default()
      const bat = await kuroApi.getRequestToken(role.roleId, token, did)

      if (bat) {
        console.log(`🔑 bat token获取成功: ${bat.substring(0, 10)}...`)

        // 更新登录结果中的bat token
        const loginResult = this.pendingLogins.get(loginId)
        if (loginResult) {
          loginResult.bat = bat
          this.pendingLogins.set(loginId, loginResult)
        }

        // 更新数据库中的bat token
        const tokenData = this.tokenUserMap.get(loginId)
        if (tokenData) {
          try {
            const User = (await import("../components/User.js")).User
            const user = new User(tokenData.userId, tokenData.botId)

            await user.updateBatToken(bat)
            console.log(`💾 数据库中的bat token已更新`)
          } catch (error) {
            console.error(`❌ 更新数据库中的bat token失败:`, error)
          }
        }
      } else {
        console.log(`🔑 bat token获取失败`)
      }
    } catch (error) {
      console.warn(`⚠️ 异步获取bat token失败:`, error.message)
    }
  }

  /**
   * 获取过期页面
   */
  getExpiredPage() {
    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录链接已过期 - Rover Plugin</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 400px;
            width: 90%;
        }
        .icon {
            font-size: 64px;
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            margin-bottom: 16px;
            font-size: 24px;
        }
        p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        .countdown {
            font-size: 18px;
            color: #667eea;
            font-weight: bold;
            margin: 20px 0;
        }
        .redirect-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">⏰</div>
        <h1>登录链接已过期</h1>
        <p>此登录链接已经使用过，为了安全起见，每个登录链接只能使用一次。</p>
        <div class="redirect-info">
            <p><strong>正在跳转到项目主页...</strong></p>
            <div class="countdown" id="countdown">3秒后跳转</div>
        </div>
        <p>如需重新登录，请在机器人中发送 <code>#鸣潮登录</code> 获取新的登录链接。</p>
    </div>

    <script>
        let seconds = 3;
        const countdownElement = document.getElementById('countdown');

        const timer = setInterval(() => {
            seconds--;
            if (seconds > 0) {
                countdownElement.textContent = seconds + '秒后跳转';
            } else {
                countdownElement.textContent = '正在跳转...';
                clearInterval(timer);
                window.location.href = 'https://github.com/tyql688/WutheringWavesUID';
            }
        }, 1000);
    </script>
</body>
</html>
    `
  }

  /**
   * 获取成功页面
   */
  getSuccessPage(data) {
    try {
      const templatePath = path.join(__dirname, "../resources/web/success.html")
      let htmlContent = fs.readFileSync(templatePath, "utf8")

      // 替换模板变量
      htmlContent = htmlContent.replace(/\{\{userName\}\}/g, data.userName || "")
      htmlContent = htmlContent.replace(/\{\{roleName\}\}/g, data.roleName || "")
      htmlContent = htmlContent.replace(/\{\{roleId\}\}/g, data.roleId || "")
      htmlContent = htmlContent.replace(/\{\{gameLevel\}\}/g, data.gameLevel || "")

      return htmlContent
    } catch (error) {
      console.error("读取成功页面模板失败:", error)
      return this.getErrorPage("页面加载失败")
    }
  }

  /**
   * 获取404页面
   */
  get404Page() {
    try {
      const templatePath = path.join(__dirname, "../resources/web/404.html")
      return fs.readFileSync(templatePath, "utf8")
    } catch (error) {
      console.error("读取404页面模板失败:", error)
      return this.getErrorPage("页面未找到")
    }
  }

  /**
   * 获取错误页面
   */
  getErrorPage(message) {
    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>错误 - Rover Plugin</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }
        .error-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            text-align: center;
            max-width: 400px;
        }
        .error-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }
        .error-message {
            color: #333;
            font-size: 18px;
            margin-bottom: 20px;
        }
        .error-description {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">❌</div>
        <div class="error-message">${message}</div>
        <div class="error-description">请稍后重试或联系管理员</div>
    </div>
</body>
</html>
    `
  }

  /**
   * 存储绑定结果
   */
  storeBindResult(bindResult) {
    // 这里可以存储到Redis、文件或数据库
    // 暂时存储到内存中，实际使用时应该持久化
    const resultKey = `bind_result_${bindResult.userId}_${bindResult.timestamp}`

    // 可以使用Redis存储
    if (global.redis) {
      global.redis.set(resultKey, JSON.stringify(bindResult), { EX: 300 }) // 5分钟过期
    }

    console.log("✅ 绑定结果已存储:", resultKey)
  }

  /**
   * 生成登录token（参考WutheringWavesUID实现）
   * @param {string} userId Bot用户ID
   * @returns {string} 8位随机token
   */
  generateLoginToken(userId) {
    // 使用SHA256哈希生成token，取前8位
    return crypto.createHash("sha256").update(userId).digest("hex").substring(0, 8)
  }

  /**
   * 生成随机登录ID
   * @returns {string} 随机登录ID
   */
  generateRandomLoginId() {
    // 生成16位随机字符串
    return crypto.randomBytes(8).toString("hex")
  }

  /**
   * 获取登录URL
   */
  getLoginUrl(userId = "default", botId = "default") {
    // 生成随机登录token，而不是直接使用用户ID
    const loginToken = this.generateLoginToken(userId + Date.now())

    // 存储token到用户ID的映射，设置5分钟过期（与WutheringWavesUID保持一致）
    this.tokenUserMap.set(loginToken, {
      userId: userId,
      botId: botId,
      createTime: Date.now(),
      expireTime: Date.now() + 5 * 60 * 1000, // 5分钟过期
    })

    // 清理过期的token
    this.cleanExpiredTokens()

    if (config.isLocalLoginMode()) {
      const baseUrl = config.getLocalLoginUrl()
      return `${baseUrl}/login?id=${loginToken}`
    } else {
      const remoteUrl = config.getRemoteLoginUrl()
      if (!remoteUrl) {
        throw new Error("远程登录URL未配置")
      }
      // 远程模式：直接使用远程服务器的登录接口
      return `${remoteUrl}/login?id=${loginToken}`
    }
  }

  /**
   * 清理过期的token
   */
  cleanExpiredTokens() {
    const now = Date.now()
    for (const [token, data] of this.tokenUserMap.entries()) {
      if (data.expireTime < now) {
        this.tokenUserMap.delete(token)
        this.pendingLogins.delete(token)
      }
    }
  }

  /**
   * 根据token获取用户ID
   */
  getUserIdByToken(token) {
    const tokenData = this.tokenUserMap.get(token)
    if (!tokenData) {
      return null
    }

    // 检查是否过期
    if (tokenData.expireTime < Date.now()) {
      this.tokenUserMap.delete(token)
      this.pendingLogins.delete(token)
      return null
    }

    return tokenData.userId
  }

  /**
   * 检查服务器状态
   */
  isServerRunning() {
    return this.isRunning
  }
}

// 创建全局实例
const webLoginServer = new WebLoginServer()

export default webLoginServer
