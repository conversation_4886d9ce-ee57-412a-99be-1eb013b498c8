/**
 * Bat Token测试脚本 - 专门测试bat token的获取和使用
 */

import { Database } from "./lib/api.js"
import KuroApi from "./components/api/kuroapi.js"

// 测试配置 - 使用您提供的真实token和did
const TEST_CONFIG = {
  userId: "test_user_12345",
  token: "eyJhbGciOiJIUzI1NiJ9.eyJjcmVhdGVkIjoxNzUzNjMxMjg2Njc1LCJ1c2VySWQiOjEwODE2NzMyfQ.Z9BLJ1jtanrNfl1JLvcifr8ypSXEu7513sQvy9I-A1c",
  did: "6F02FE7B671ACA64694F19FB67EBEBAD07659846",
}

async function testBatTokenFlow() {
  console.log("🧪 开始bat token获取流程测试...")
  console.log(`Token: ${TEST_CONFIG.token.substring(0, 20)}...`)
  console.log(`DID: ${TEST_CONFIG.did}`)

  try {
    const api = new KuroApi()

    // 步骤1: 尝试获取角色列表来获取真实UID
    console.log("\n📋 步骤1: 尝试获取角色列表...")
    const roleListResult = await api.getRoleList(TEST_CONFIG.token, TEST_CONFIG.did)
    
    console.log(`角色列表响应: code=${roleListResult.code}, msg=${roleListResult.msg}`)
    
    let uid = null
    if (roleListResult.code === 200 && roleListResult.data && roleListResult.data.length > 0) {
      const firstRole = roleListResult.data[0]
      uid = firstRole.roleId
      console.log(`✅ 获取到真实UID: ${uid}`)
      console.log(`角色名: ${firstRole.roleName}`)
    } else {
      // 如果获取角色列表失败，使用一个测试UID
      uid = "100000001"
      console.log(`❌ 获取角色列表失败，使用测试UID: ${uid}`)
    }

    // 步骤2: 测试bat token获取
    console.log("\n🔑 步骤2: 测试bat token获取...")
    console.log("正在调用 getRequestToken...")
    
    const batToken = await api.getRequestToken(uid, TEST_CONFIG.token, TEST_CONFIG.did, null, true)
    
    if (batToken) {
      console.log(`✅ 成功获取bat token: ${batToken.substring(0, 20)}...`)
      console.log(`Bat token长度: ${batToken.length}`)
      
      // 步骤3: 测试使用bat token的API
      console.log("\n🎮 步骤3: 测试需要bat token的API...")
      
      // 测试基础数据API
      console.log("测试基础数据API...")
      const baseData = await api.getBaseData(uid, TEST_CONFIG.token, batToken)
      console.log(`基础数据: code=${baseData.code}, msg=${baseData.msg}`)
      if (baseData.code === 200 && baseData.data) {
        console.log(`  等级: ${baseData.data.level || "未知"}`)
        console.log(`  世界等级: ${baseData.data.worldLevel || "未知"}`)
      }
      
      // 测试角色数据API
      console.log("测试角色数据API...")
      const roleData = await api.getRoleData(uid, batToken)
      console.log(`角色数据: code=${roleData.code}, msg=${roleData.msg}`)
      if (roleData.code === 200 && roleData.data) {
        console.log(`  角色数量: ${roleData.data.roleList?.length || 0}`)
      }
      
      // 测试探索数据API
      console.log("测试探索数据API...")
      const exploreData = await api.getExploreData(uid, batToken)
      console.log(`探索数据: code=${exploreData.code}, msg=${exploreData.msg}`)
      if (exploreData.code === 200 && exploreData.data) {
        console.log(`  总探索度: ${exploreData.data.totalPercent || "未知"}%`)
      }
      
      // 步骤4: 测试绑定到数据库并使用统一API
      console.log("\n💾 步骤4: 测试完整绑定流程...")
      
      try {
        // 清理旧数据
        console.log("清理旧数据...")
        await Database.WavesUser.destroy({
          where: { user_id: TEST_CONFIG.userId }
        })
        await Database.WavesBind.destroy({
          where: { user_id: TEST_CONFIG.userId }
        })

        // 创建用户记录（包含bat token）
        console.log("创建用户记录...")
        await Database.WavesUser.create({
          user_id: TEST_CONFIG.userId,
          bot_id: "default",
          uid: uid,
          cookie: TEST_CONFIG.token,
          did: TEST_CONFIG.did,
          bat: batToken,
          platform: "ios",
          status: "正常",
          push_switch: "off",
          sign_switch: "off",
          stamina_bg_value: "0",
          bbs_sign_switch: "off"
        })

        // 创建绑定记录
        console.log("创建绑定记录...")
        await Database.WavesBind.create({
          user_id: TEST_CONFIG.userId,
          bot_id: "default",
          uid: uid,
          is_default: 1
        })

        console.log("✅ 数据库绑定成功")

        // 步骤5: 验证绑定并测试统一API
        console.log("\n🔍 步骤5: 验证绑定并测试统一API...")
        
        const boundUid = await Database.WavesBind.getUidByUser(TEST_CONFIG.userId)
        const account = await Database.WavesUser.getPrimaryAccount(TEST_CONFIG.userId)
        
        console.log(`绑定UID: ${boundUid}`)
        console.log(`账号信息: ${account ? "已获取" : "未找到"}`)
        console.log(`存储的Bat Token: ${account?.bat ? account.bat.substring(0, 20) + "..." : "无"}`)

        // 测试统一API接口
        console.log("\n🚀 步骤6: 测试统一API接口...")
        const { RoverAPI } = await import("./lib/api.js")
        
        const isUserBound = await RoverAPI.isUserBound(TEST_CONFIG.userId)
        console.log(`用户绑定状态: ${isUserBound}`)
        
        if (isUserBound) {
          console.log("测试获取基础数据...")
          const baseDataResult = await RoverAPI.getBaseData(TEST_CONFIG.userId)
          console.log(`基础数据获取: ${baseDataResult.success ? "✅ 成功" : "❌ 失败"} - ${baseDataResult.message}`)
          
          console.log("测试获取完整数据...")
          const completeData = await RoverAPI.getUserCompleteData(TEST_CONFIG.userId)
          console.log(`完整数据获取: ${completeData.success ? "✅ 成功" : "❌ 失败"} - ${completeData.message}`)
        }

      } catch (dbError) {
        console.error("❌ 数据库操作失败:", dbError)
      }
      
    } else {
      console.log("❌ 获取bat token失败")
      console.log("可能的原因:")
      console.log("1. Token已过期")
      console.log("2. UID不正确")
      console.log("3. 网络问题或风险控制")
      console.log("4. API接口变更")
    }

  } catch (error) {
    console.error("❌ 测试失败:", error)
    console.error("错误堆栈:", error.stack)
  }
}

// 运行测试
testBatTokenFlow()
