<!DOCTYPE html>
<html lang="zh-cn">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width">
  <link rel="shortcut icon" href="#"/>
  <link rel="preload" href="{{_res_path}}/common/font/HYWH-65W.woff" as="font" type="font/woff">
  <link rel="preload" href="{{_res_path}}/common/font/NZBZ.woff" as="font" type="font/woff">
  <link rel="preload" href="{{_res_path}}/common/font/tttgbnumber.woff" as="font" type="font/woff">
  <link rel="stylesheet" type="text/css" href="{{_res_path}}/common/common.css"/>
  <link rel="stylesheet" type="text/css" href="{{_res_path}}/character/profile-detail.css"/>
  <title>鸣潮角色面板</title>
</head>
{{set elemCls = {火:'pyro',冰:'cryo',风:'anemo',雷:'electro',量子:'quantum',虚数:'geo',物理:'sr', }[element||elem] || element || elem || 'hydro' }}
<body class="elem-{{elemCls}} {{displayMode || mode || `default`}}-mode {{bodyClass}}" {{sys.scale}}>
<div class="container elem-bg" id="container">

{{set weapon = data.weapon || {} }}
{{set imgs = data.imgs || {} }}


<div class="profile-cont game-ww">
  <div class="basic">
    <div class="main-pic" style="background-image:url({{_res_path}}{{data.pile}})"></div>
    <div class="detail">
      <div class="char-name">{{data.name}}</div>
      <div class="char-lv">特征码 {{uid}} - Lv.{{data.level}}
        <span class="cons cons-{{data.chainUnlockNum}}">{{data.chainUnlockNum}}链</span></div>

      <!-- 在权重查询模式下不显示角色属性和命座 -->
      {{if displayMode !== "artis"}}
      <ul class="char-attr">
        {{if data.roleAttributeList && data.roleAttributeList.length > 0}}
        {{each data.roleAttributeList attribute}}
        <li>
          <div class="icon">
            <img src="{{_res_path}}{{attribute.pic}}" alt="{{attribute.name}}">
          </div>
          <div class="title{{if attribute.valid}} score-grade grade-{{attribute.valid}}{{/if}}">{{attribute.name}}</div>
          <div class="value{{if attribute.valid}} score-grade grade-{{attribute.valid}}{{/if}}">{{attribute.value}}</div>
        </li>
        {{/each}}
        {{/if}}
      </ul>
      {{/if}}
    </div>
    {{if displayMode !== "artis"}}
    <div class="char-cons">
      {{if data.chainList}}
      {{each data.chainList chain idx}}
      <div class="cons-item">
        <div class="talent-icon {{!chain.unlocked ? 'off' : '' }}">
          <div class="talent-icon-img" style="background-image:url({{_res_path}}{{chain.pic}})"></div>
        </div>
      </div>
      {{/each}}
      {{else}}
      {{set cons = [1,2,3,4,5,6]}}
      {{each cons idx}}
      <div class="cons-item">
        <div class="talent-icon {{idx * 1 > data.chainUnlockNum * 1 ? 'off' : '' }}">
          <div class="talent-icon-img" style="background-image:url({{_res_path}}{{imgs['cons' + idx]}})"></div>
        </div>
      </div>
      {{/each}}
      {{/if}}
    </div>
    {{/if}}

    <div class="data-info">
      {{if data.dataSource}}
      <span>数据源：{{data.dataSource === 'change' ? '面板变换' : data.dataSource}}</span>
      {{/if}}
      {{if data.updateTime}}
      <span class="time">{{data.updateTime}}</span>
      {{/if}}
    </div>
  </div>

  <div class="profile-detail">
    <!-- 在权重查询模式下不显示技能 -->
    {{if displayMode !== "artis"}}
    <div class="ww-talent">
      <div class="char-talents left">
        {{each data.skillList skill idx}}
        {{if skill.type === '常态攻击' || skill.type === '共鸣技能' || skill.type === '共鸣回路' || skill.type === '共鸣解放' || skill.type === '变奏技能'}}
        <div class="talent-item">
          <div class="talent-icon">
            <div class="talent-icon-img" style="background-image:url({{_res_path}}{{skill.pic}})"></div>
          </div>
          <div class="talent-info">
            <div class="talent-name">{{skill.type}}</div>
            <div class="talent-level">Lv{{skill.level}}</div>
          </div>
        </div>
        {{/if}}
        {{/each}}
      </div>
    </div>
    {{/if}}

    <!-- 声骸属性汇总 - 在权重查询模式下不显示 -->
    {{if displayMode !== "artis" && data.equipPhantomAddPropList && data.equipPhantomAddPropList.length > 0}}
    <div class="phantom-summary">
      <div class="arti-info">
        <div class="score-section">
          {{if data.totalScore && data.totalScore > 0}}
          <div class="score-item">
            <div class="score-grade grade-{{data.totalScoreBackground}}">{{data.totalScoreBackground}}</div>
            <div class="score-label">声骸评级</div>
          </div>
          <div class="score-item">
            <div class="score-value">{{data.totalScore}}</div>
            <div class="score-label">声骸评分</div>
          </div>
          {{else}}
          <div class="score-item">
            <div class="score-grade">暂无评分</div>
          </div>
          {{/if}}
        </div>
        <div class="arti-info-detail">
          {{each data.equipPhantomAddPropList prop idx}}
          <div class="arti-prop-item">
            <span class="prop-name{{if prop.valid}} score-grade grade-{{prop.valid}}{{/if}}">{{prop.name}}</span>
            <span class="prop-value{{if prop.valid}} score-grade grade-{{prop.valid}}{{/if}}">{{prop.value}}</span>
          </div>
          {{/each}}
        </div>
      </div>
    </div>
    {{/if}}

    <div class="artis">
      <!-- 在权重查询模式下不显示武器 -->
      {{if displayMode !== "artis" && weapon && weapon.name}}
      <div class="item weapon">
        <div class="img" style="background-image:url({{_res_path}}{{weapon.pic}})"></div>
        <div class="head">
          <strong>{{weapon.name}}</strong>
          <div class="weapon-level-info">
            <span class="info"><span class="affix affix-{{weapon.resonLevel}}">精{{weapon.resonLevel}}</span> Lv.{{weapon.level}} </span>
          </div>
        </div>

        <ul class="detail attr weapon-main-attr">
          {{if weapon.mainPropList && weapon.mainPropList.length > 0}}
          {{each weapon.mainPropList mainProp idx}}
          <li class="weapon-main-prop">
            <div class="icon">
              <img src="{{_res_path}}{{mainProp.pic}}" alt="{{mainProp.attributeName}}">
            </div>
            <div class="title-val">
              <div class="main-prop-line">
                <span class="title">{{mainProp.attributeName}}</span>
                <span class="val">{{mainProp.attributeValue}}</span>
              </div>
            </div>
          </li>
          {{/each}}
          {{/if}}
        </ul>

        <div class="weapon-desc-cont">
          <div class="weapon-desc">{{weapon.effect}}</div>
        </div>
      </div>
      {{/if}}


      <!-- 在非权重查询模式下显示声骸 -->
      {{if displayMode !== "artis" && data.equipPhantomList && data.equipPhantomList.length > 0}}
      {{each data.equipPhantomList phantom idx}}
      <div class="item arti phantom">
        {{if phantom && phantom.name}}
        <div class="arti-icon">
          <div class="img" style="background-image:url({{_res_path}}{{phantom.pic}})"></div>
          <span>+{{phantom.level}}</span>
        </div>
        <div class="head">
          <strong>{{phantom.name}}</strong>
          <span class="phantom-cost">消耗: {{phantom.cost}}</span>
          <div class="phantom-set">
            <span class="score-grade grade-{{phantom.score[1]}}">{{phantom.score[0]}}分 - {{phantom.score[1]}}</span>
          </div>
        </div>
        <ul class="detail attr">

          {{if phantom.mainPropList && phantom.mainPropList.length > 0}}
          {{each phantom.mainPropList mainProp}}
          <li class="arti-main">
            <div class="icon">
              <img src="{{_res_path}}{{mainProp.pic}}" alt="{{mainProp.attributeName}}">
            </div>
            <div class="title-val">
              <div class="main-prop-line">
                <span class="title">{{mainProp.attributeName}}</span>
                <span class="val">{{mainProp.attributeValue}}</span>
              </div>
            </div>
          </li>
          {{/each}}
          {{/if}}

          {{if phantom.subPropList && phantom.subPropList.length > 0}}
          {{each phantom.subPropList subProp}}
          {{if subProp && subProp.attributeName}}
          <li class="sub-prop">
            <div class="dot"></div>
            <span class="title{{if subProp.valid}} score-grade grade-{{subProp.valid}}{{/if}}">{{subProp.attributeName.replace('加成','')}}</span>
            <span class="val{{if subProp.isMax}} max-value{{else if subProp.valid}} score-grade grade-{{subProp.valid}}{{/if}}">{{subProp.attributeValue}}</span>
          </li>
          {{/if}}
          {{/each}}
          {{/if}}
        </ul>
        {{/if}}
      </div>
      {{/each}}
      {{/if}}
    </div>
  </div>
</div>

  <!-- 权重分析部分 - 仅在artis模式下显示 -->
  {{if displayMode === "artis" && weights && weights.length > 0}}
  <div class="artis-mark-cont">
    <div class="artis-mark-header">
      <div class="mark-title">声骸评分详情</div>
      <div class="mark-subtitle">基于声骸属性的权重评分</div>
    </div>

    <!-- 声骸详细展示 - 权重查询模式，显示具体分数 -->
    {{if data.equipPhantomList && data.equipPhantomList.length > 0}}
    <div class="artis">
      {{each data.equipPhantomList phantom idx}}
      <div class="item arti">
        <div class="arti-icon">
          <div class="img" style="background-image: url('{{_res_path}}{{phantom.pic}}')"></div>
          <span>+{{phantom.level || 0}}</span>
        </div>
        <div class="head">
          <strong>{{phantom.name || `声骸${idx + 1}`}}</strong>
          <span class="mark-{{phantom.score && phantom.score[1] ? phantom.score[1] : 'C'}}">
            {{phantom.score && phantom.score[0] ? phantom.score[0].toFixed(1) : '0.0'}}分 - {{phantom.score && phantom.score[1] ? phantom.score[1] : 'C'}}
          </span>
        </div>
        <ul class="detail">
          <!-- 主属性 - 使用与副属性相同的结构 -->
          {{if phantom.mainPropList && phantom.mainPropList.length > 0}}
          {{each phantom.mainPropList mainProp}}
          <li class="arti-main">
            <span class="title">{{mainProp.attributeName}}</span>
            <span class="val">+{{mainProp.attributeValue}}</span>
          </li>
          {{/each}}
          {{/if}}

          <!-- 副属性 - 显示具体分数在右边，使用table-cell布局 -->
          {{if phantom.subPropList && phantom.subPropList.length > 0}}
          {{each phantom.subPropList subProp}}
          <li class="sub-prop-with-score">
            <span class="title">{{subProp.attributeName}}</span>
            <span class="val">+{{subProp.attributeValue}}</span>
            {{if subProp.score}}
            <span class="score">{{subProp.score.toFixed(1)}}</span>
            {{/if}}
          </li>
          {{/each}}
          {{/if}}
        </ul>
      </div>
      {{/each}}
    </div>
    {{/if}}

    <!-- 权重配置表 - miao-plugin风格，WutheringWavesUID信息内容 -->
    {{each weights weightSection idx}}
    {{if weightSection.name === "权重配置说明" && weightSection.details && weightSection.details.length > 0}}
    <div class="cont artis-mark-cont">
      <div class="cont-title">#{{data.name}}词条权重表</div>

      <!-- 权重表 -->
      <div class="dmg-cont cont-table">
        <div class="tr thead">
          <div class="th">属性</div>
          <div class="th">C4主词条权重</div>
          <div class="th">C3主词条权重</div>
          <div class="th">C1主词条权重</div>
          <div class="th">副词条权重</div>
        </div>
        {{each weightSection.details detail}}
        {{if detail.type === "权重配置"}}
        <div class="tr">
          <div class="title">{{detail.name}}</div>
          <div class="value">{{detail.c4Weight || "-"}}</div>
          <div class="value">{{detail.c3Weight || "-"}}</div>
          <div class="value">{{detail.c1Weight || "-"}}</div>
          <div class="value">{{detail.weight}}</div>
        </div>
        {{/if}}
        {{/each}}
      </div>

      <!-- 权重说明 -->
      <div class="cont-footer">
        <div>词条得分：词条数值 × 当前词条权重 / 声骸未对齐最高分 × 对齐分数(50)</div>
        <div>声骸评分标准：SSS≥{{data.sssScore || "200.00"}}分/ SS≥{{data.ssScore || "180.00"}}分／S≥{{data.sScore || "160.00"}}分 / A≥{{data.aScore || "140.00"}}分 / B≥{{data.bScore || "120.00"}}分 / C</div>
        <div>当前角色评分标准仅供参考与娱乐，不代表任何官方或权威的评价。</div>
      </div>
    </div>
    {{/if}}
    {{/each}}

    <!-- 评分说明 -->
    <div class="score-explanation">
      <div class="section-title">评分说明</div>
      <div class="explanation-content">
        <p>• 声骸评分基于角色专属权重配置计算，权重值越高表示该属性对角色越重要</p>
        <p>• 评级从高到低为：SSS > SS > S > A > B > C，评分仅供参考</p>
        <p>• 实际搭配需考虑角色技能机制、队伍配置和个人游戏风格</p>
        <p>• 权重配置来源于角色理论计算和实战数据分析</p>
      </div>
    </div>
  </div>
  {{/if}}

  <div class="copyright">{{@copyright || sys?.copyright}}</div>
</div>
</body>
</html>
