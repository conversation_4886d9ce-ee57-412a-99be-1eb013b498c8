import fs from "fs"
import path from "path"
import { dataPath } from "./path.js"
import config from "./config.js"

/**
 * 刷新面板管理器 - 参考WutheringWavesUID实现
 * 负责角色面板数据的刷新、保存和管理
 */
class RefreshPanelManager {
  constructor() {
    // 信号量管理器，用于控制并发
    this.semaphoreManager = new SemaphoreManager()

    // 角色数据保存目录
    this.playerDataPath = path.join(dataPath, "characters")
    this.ensureDirectoryExists(this.playerDataPath)

    // 面板数据保存目录 - 按照要求保存到 data/rover/players/mc/
    this.panelDataPath = path.join(dataPath, "rover", "players", "mc")
    this.ensureDirectoryExists(this.panelDataPath)
  }

  /**
   * 确保目录存在
   * @param {string} dirPath - 目录路径
   */
  ensureDirectoryExists(dirPath) {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true })
    }
  }

  /**
   * 保存角色面板信息 - 参考WutheringWavesUID的save_card_info实现
   * @param {string} uid - 用户UID
   * @param {Array} wavesData - 角色数据数组
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 保存结果
   */
  async saveCardInfo(uid, wavesData, options = {}) {
    if (!wavesData || wavesData.length === 0) {
      return { success: false, message: "没有角色数据需要保存" }
    }

    try {
      // 创建用户目录
      const userDir = path.join(this.playerDataPath, uid)
      this.ensureDirectoryExists(userDir)

      // 数据文件路径
      const dataFilePath = path.join(userDir, "rawData.json")

      // 读取现有数据
      let oldData = {}
      if (fs.existsSync(dataFilePath)) {
        try {
          const content = fs.readFileSync(dataFilePath, "utf8")
          const existingData = JSON.parse(content)
          // 将数组转换为以roleId为key的对象
          oldData = {}
          if (Array.isArray(existingData)) {
            existingData.forEach(item => {
              if (item.role && item.role.roleId) {
                oldData[item.role.roleId] = item
              }
            })
          }
        } catch (error) {
          console.warn(`读取现有数据失败: ${error.message}`)
          // 如果文件损坏，删除它
          fs.unlinkSync(dataFilePath)
        }
      }

      // 处理新数据
      const refreshUpdate = {}
      const refreshUnchanged = {}

      for (const item of wavesData) {
        if (!item.role || !item.role.roleId) {
          console.warn("跳过无效的角色数据:", item)
          continue
        }

        const roleId = item.role.roleId
        const oldItem = oldData[roleId]

        if (!oldItem || JSON.stringify(oldItem) !== JSON.stringify(item)) {
          refreshUpdate[roleId] = item
          console.log(`✅ 角色数据已更新: ${item.role.roleName || roleId}`)
        } else {
          refreshUnchanged[roleId] = item
          console.log(`ℹ️ 角色数据无变化: ${item.role.roleName || roleId}`)
        }

        // 更新数据
        oldData[roleId] = item
      }

      // 转换回数组格式
      const saveData = Object.values(oldData)

      // 预下载资源（只下载更新的角色资源）- 使用统一的资源下载器
      if (Object.keys(refreshUpdate).length > 0) {
        const { resourceDownloader } = await import("../utils/resourceDownloader.js")
        await resourceDownloader.downloadMultipleCharacterResources(Object.values(refreshUpdate))
      }

      // 保存到原来的位置（兼容性）
      fs.writeFileSync(dataFilePath, JSON.stringify(saveData, null, 2), "utf8")

      // 同时保存到新的面板数据位置 data/players/mc/{uid}.json
      const panelFilePath = path.join(this.panelDataPath, `${uid}.json`)
      fs.writeFileSync(panelFilePath, JSON.stringify(saveData, null, 2), "utf8")

      console.log(`✅ 角色面板数据已保存: ${uid} (${saveData.length}个角色)`)
      console.log(`📁 面板数据保存位置: ${panelFilePath}`)

      return {
        success: true,
        message: `成功保存${saveData.length}个角色的面板数据`,
        data: {
          total: saveData.length,
          updated: Object.keys(refreshUpdate).length,
          unchanged: Object.keys(refreshUnchanged).length,
          updatedRoleIds: Object.keys(refreshUpdate).map(id => parseInt(id)), // 添加更新的角色ID列表
          refreshUpdate,
          refreshUnchanged,
        },
      }
    } catch (error) {
      console.error("保存角色面板数据失败:", error)
      return {
        success: false,
        message: `保存失败: ${error.message}`,
      }
    }
  }

  /**
   * 读取角色面板信息
   * @param {string} uid - 用户UID
   * @param {string} roleId - 角色ID（可选，不提供则返回所有角色）
   * @returns {Promise<Object>} 角色数据
   */
  async getCardInfo(uid, roleId = null) {
    try {
      const userDir = path.join(this.playerDataPath, uid)
      const dataFilePath = path.join(userDir, "rawData.json")

      if (!fs.existsSync(dataFilePath)) {
        return {
          success: false,
          message: "未找到角色面板数据，请先刷新面板",
        }
      }

      const content = fs.readFileSync(dataFilePath, "utf8")
      const allData = JSON.parse(content)

      if (!Array.isArray(allData)) {
        return {
          success: false,
          message: "角色数据格式错误",
        }
      }

      if (roleId) {
        // 返回指定角色的数据
        const characterData = allData.find(item => item.role && item.role.roleId === roleId)

        if (!characterData) {
          return {
            success: false,
            message: `未找到角色ID为${roleId}的数据`,
          }
        }

        return {
          success: true,
          data: characterData,
        }
      } else {
        // 返回所有角色数据
        return {
          success: true,
          data: allData,
        }
      }
    } catch (error) {
      console.error("读取角色面板数据失败:", error)
      return {
        success: false,
        message: `读取失败: ${error.message}`,
      }
    }
  }

  /**
   * 获取用户的角色列表
   * @param {string} uid - 用户UID
   * @returns {Promise<Array>} 角色列表
   */
  async getCharacterList(uid) {
    try {
      const result = await this.getCardInfo(uid)
      if (!result.success) {
        return []
      }

      return result.data.map(item => ({
        roleId: item.role.roleId,
        roleName: item.role.roleName,
        level: item.role.level,
        element: item.role.element,
        weaponName: item.weaponData?.weapon?.weaponName || "未知武器",
        lastUpdateTime: item.lastUpdateTime || Date.now(),
      }))
    } catch (error) {
      console.error("获取角色列表失败:", error)
      return []
    }
  }

  /**
   * 清理过期的角色数据
   * @param {number} maxAge - 最大保存时间（毫秒）
   * @returns {Promise<Object>} 清理结果
   */
  async cleanupExpiredData(maxAge = 30 * 24 * 60 * 60 * 1000) {
    // 默认30天
    try {
      const userDirs = fs.readdirSync(this.playerDataPath)
      let cleanedCount = 0

      for (const userDir of userDirs) {
        const userPath = path.join(this.playerDataPath, userDir)
        const dataFilePath = path.join(userPath, "rawData.json")

        if (fs.existsSync(dataFilePath)) {
          const stats = fs.statSync(dataFilePath)
          const age = Date.now() - stats.mtime.getTime()

          if (age > maxAge) {
            fs.unlinkSync(dataFilePath)
            // 如果目录为空，也删除目录
            const remainingFiles = fs.readdirSync(userPath)
            if (remainingFiles.length === 0) {
              fs.rmdirSync(userPath)
            }
            cleanedCount++
            console.log(`🗑️ 清理过期数据: ${userDir}`)
          }
        }
      }

      return {
        success: true,
        message: `清理了${cleanedCount}个过期的角色数据文件`,
      }
    } catch (error) {
      console.error("清理过期数据失败:", error)
      return {
        success: false,
        message: `清理失败: ${error.message}`,
      }
    }
  }

  // 旧的预下载方法已删除，现在使用统一的资源下载器
}

/**
 * 信号量管理器 - 参考WutheringWavesUID实现
 * 用于控制并发请求数量
 */
class SemaphoreManager {
  constructor() {
    this.lastConfig = config.getRefreshConcurrency()
    this.semaphore = this.createSemaphore(this.lastConfig)
    this.semaphoreLock = false
  }

  /**
   * 创建信号量
   * @param {number} value - 信号量值
   * @returns {Object} 信号量对象
   */
  createSemaphore(value) {
    let count = value
    const waiters = []

    return {
      async acquire() {
        return new Promise(resolve => {
          if (count > 0) {
            count--
            resolve()
          } else {
            waiters.push(resolve)
          }
        })
      },

      release() {
        if (waiters.length > 0) {
          const resolve = waiters.shift()
          resolve()
        } else {
          count++
        }
      },
    }
  }

  /**
   * 获取信号量
   * @returns {Promise<Object>} 信号量对象
   */
  async getSemaphore() {
    const currentConfig = config.getRefreshConcurrency()

    if (config.useGlobalSemaphore()) {
      return await this.getGlobalSemaphore(currentConfig)
    } else {
      return this.createSemaphore(currentConfig)
    }
  }

  /**
   * 获取全局信号量
   * @param {number} currentConfig - 当前配置值
   * @returns {Promise<Object>} 信号量对象
   */
  async getGlobalSemaphore(currentConfig) {
    if (this.lastConfig !== currentConfig && !this.semaphoreLock) {
      this.semaphoreLock = true
      this.semaphore = this.createSemaphore(currentConfig)
      this.lastConfig = currentConfig
      this.semaphoreLock = false
    }
    return this.semaphore
  }
}

// 创建全局实例
const refreshPanelManager = new RefreshPanelManager()

export default refreshPanelManager
export { RefreshPanelManager, SemaphoreManager }
