# HTML 模板数据传入指南

## 概述

本文档详细说明了 rover-plugin 中每个 HTML 模板需要的数据结构，以及如何正确传入这些数据。

## 数据传入流程

### 1. 基本流程
```javascript
// 1. 准备渲染数据
const renderData = {
  // 数据字段
}

// 2. 调用渲染器
const img = await renderer.render(e, "模板路径", renderData)

// 3. 发送结果
if (img) {
  await e.reply(img)
}
```

### 2. 系统自动添加的字段
Yunzai 系统会自动为所有模板添加以下字段：

```javascript
{
  // 基础系统字段
  sys: { scale: 1 },
  copyright: "Created By TRSS-Yunzai<span class=\"version\">3.0.0</span>",

  // 插件资源路径
  _res_path: "../../../plugins/rover-plugin/resources/",
  _plugin: "rover-plugin",
  _htmlPath: "character/profile-detail",
  saveId: "profile-detail",
  tplFile: "./plugins/rover-plugin/resources/character/profile-detail.html",

  // miao-plugin 兼容字段 (由 Yunzai 系统自动添加，rover-plugin 不使用)
  _miao_path: "../../../plugins/miao-plugin/resources/",
  _tpl_path: "/path/to/miao-plugin/resources/common/tpl/",
  defaultLayout: "/path/to/miao-plugin/resources/common/layout/default.html",
  elemLayout: "/path/to/miao-plugin/resources/common/layout/elem.html"
}
```

**重要说明**:
- `_miao_path`、`_tpl_path` 等字段由 **Yunzai 系统核心** 自动添加
- 这些字段用于兼容 miao-plugin 模板，rover-plugin 不直接使用
- 即使没有安装 miao-plugin，这些字段也会存在（只是路径无效）
- rover-plugin 只使用 `_res_path` 字段来引用自己的资源

## 各模板数据详解

### 1. 角色面板 (character/profile-detail.html)

#### 使用场景
- 角色详细面板展示
- 权重查询面板（`displayMode: "artis"`）

#### 调用方式
```javascript
// apps/card.js - 角色面板
const renderData = imageMapper.mapRoleDetailImages(characterData.roleId, roleDetail)
renderData.charName = charName
renderData.uid = uid
renderData.element = processedData.element || "hydro"
renderData.displayMode = "default"
renderData.saveTime = new Date().toLocaleString()

const img = await renderer.render(e, "character/profile-detail", renderData)
```

```javascript
// apps/weightQuery.js - 权重查询
const renderData = imageMapper.mapRoleDetailImages(characterData.role?.roleId, roleDetail)
renderData.charName = charName
renderData.uid = uid
renderData.element = processedData.element
renderData.displayMode = "artis"  // 关键：权重查询模式
renderData.saveTime = new Date().toLocaleString()
renderData.weights = weightData   // 权重数据

const img = await renderer.render(e, "character/profile-detail", renderData)
```

#### 必需数据字段
```javascript
{
  // 基础信息
  charName: "维拉",                    // 角色名称
  uid: "123456789",                   // 用户UID
  element: "hydro",                   // 元素类型
  displayMode: "default",             // 显示模式: "default" | "artis"
  saveTime: "2024-01-01 12:00:00",   // 保存时间
  
  // 角色数据 (由 ImageManager.mapRoleDetailImages() 生成)
  data: {
    // 角色基本信息
    name: "维拉",
    level: 90,
    star: 5,
    element: "hydro",
    pile: "/images/character/pile/1404.png",
    
    // 武器信息 (displayMode !== "artis" 时显示)
    weapon: {
      name: "苍鳞千嶂",
      level: 90,
      resonLevel: 1,
      pic: "/images/weapon/21020014.png",
      mainPropList: [...]
    },
    
    // 技能信息 (displayMode !== "artis" 时显示)
    skillList: [...],
    
    // 命座信息 (displayMode !== "artis" 时显示)
    chainList: [...],
    
    // 声骸信息
    equipPhantomList: [
      {
        name: "鸣钟之龟",
        level: 25,
        cost: 4,
        pic: "/images/phantom/43020014.png",
        score: [85.2, "S"],
        mainPropList: [...],
        subPropList: [
          {
            attributeName: "暴击",
            attributeValue: "8.2%",
            score: 12.5,  // 权重查询模式下的分数
            valid: "S"
          }
        ]
      }
    ],
    
    // 角色属性 (displayMode !== "artis" 时显示)
    roleAttributeList: [...],
    
    // 声骸属性汇总 (displayMode !== "artis" 时显示)
    equipPhantomAddPropList: [...]
  }
}
```

#### 权重查询特有字段
```javascript
{
  // 权重分析数据 (仅 displayMode === "artis" 时)
  weights: [
    {
      name: "攻击力%",
      weight: 1.0,
      value: 43.8,
      score: 43.8,
      scoreGrade: "S"
    }
  ]
}
```

#### 模板中的使用
```html
<!-- 基础信息 -->
<div class="char-name">{{charName}}</div>
<div class="uid">UID: {{uid}}</div>

<!-- 条件显示 -->
{{if displayMode !== "artis"}}
  <!-- 显示武器、技能、命座 -->
{{/if}}

<!-- 声骸信息 -->
{{each data.equipPhantomList phantom}}
  <div class="phantom">
    <img src="{{_res_path}}{{phantom.pic}}">
    {{each phantom.subPropList subProp}}
      <span>{{subProp.attributeName}}: {{subProp.attributeValue}}</span>
      {{if subProp.score}}
        <span class="score">{{subProp.score.toFixed(1)}}</span>
      {{/if}}
    {{/each}}
  </div>
{{/each}}

<!-- 权重数据 (仅权重查询模式) -->
{{if weights}}
  {{each weights weight}}
    <div>{{weight.name}}: {{weight.score}}</div>
  {{/each}}
{{/if}}
```

### 2. 角色列表 (character/profile-list.html)

#### 使用场景
- 显示用户所有角色的概览列表

#### 调用方式
```javascript
// apps/card.js
const renderData = {
  uid,
  game: "ww",
  chars,
  servName: "库街区",
  hasNew: false,
  msg: `共 ${chars.length} 个角色`,
  element: "hydro",
  background: background?.text,
  updateTime: { profile: new Date().toLocaleString() }
}

const img = await renderer.render(e, "character/profile-list", renderData)
```

#### 必需数据字段
```javascript
{
  // 基础信息
  uid: "123456789",
  game: "ww",
  servName: "库街区",
  
  // 角色列表
  chars: [
    {
      id: 1404,
      name: "维拉",
      level: 90,
      star: 5,
      element: "hydro",
      cons: 0,
      weapon: {
        name: "苍鳞千嶂",
        level: 90,
        star: 5
      },
      pic: "/images/character/face/1404.png",
      weaponPic: "/images/weapon/21020014.png"
    }
  ],
  
  // 显示信息
  hasNew: false,
  msg: "共 8 个角色",
  background: "bg-01.jpg",
  updateTime: { profile: "2024-01-01 12:00:00" }
}
```

#### 模板中的使用
```html
<div class="uid">UID: {{uid}}</div>
<div class="msg">{{msg}}</div>

{{each chars char}}
  <div class="char-item">
    <img src="{{_res_path}}{{char.pic}}">
    <div class="name">{{char.name}}</div>
    <div class="level">Lv.{{char.level}}</div>
    <div class="cons">{{char.cons}}命</div>
  </div>
{{/each}}
```

### 3. 声骸列表 (character/echo-list.html)

#### 使用场景
- 显示用户所有声骸的详细信息

#### 调用方式
```javascript
// apps/echoList.js
const renderData = {
  uid,
  game: "ww",
  artis: displayEchos,
  artisKeyTitle,
  element: "hydro",
  background: background?.text,
  saveTime: new Date().toLocaleString()
}

const img = await renderer.render(e, "character/echo-list", renderData)
```

#### 必需数据字段
```javascript
{
  // 基础信息
  uid: "123456789",
  game: "ww",
  
  // 声骸列表 (使用 artis 字段名兼容 miao-plugin)
  artis: [
    {
      name: "鸣钟之龟",
      level: 25,
      cost: 4,
      star: 5,
      pic: "/images/phantom/43020014.png",
      score: [85.2, "S"],
      mainProp: {
        name: "攻击力%",
        value: "22.8%"
      },
      subProps: [
        {
          name: "暴击",
          value: "8.2%",
          valid: "S"
        }
      ],
      character: {
        name: "维拉",
        pic: "/images/character/face/1404.png"
      }
    }
  ],
  
  // 属性标题映射
  artisKeyTitle: {
    "攻击力": "攻击",
    "攻击力%": "攻击%",
    "暴击": "暴击",
    "暴击伤害": "爆伤"
  },
  
  // 可选字段
  background: "bg-01.jpg",
  saveTime: "2024-01-01 12:00:00"
}
```

#### 模板中的使用
```html
<div class="uid">UID: {{uid}}</div>

{{each artis arti}}
  <div class="arti-item">
    <img src="{{_res_path}}{{arti.pic}}">
    <div class="name">{{arti.name}}</div>
    <div class="level">+{{arti.level}}</div>
    <div class="score">{{arti.score[0]}}分 - {{arti.score[1]}}</div>
    
    <!-- 主属性 -->
    <div class="main-prop">
      {{arti.mainProp.name}}: {{arti.mainProp.value}}
    </div>
    
    <!-- 副属性 -->
    {{each arti.subProps subProp}}
      <div class="sub-prop {{subProp.valid}}">
        {{subProp.name}}: {{subProp.value}}
      </div>
    {{/each}}
    
    <!-- 装备角色 -->
    {{if arti.character}}
      <div class="character">
        <img src="{{_res_path}}{{arti.character.pic}}">
        <span>{{arti.character.name}}</span>
      </div>
    {{/if}}
  </div>
{{/each}}
```

### 4. 角色卡片 (character/ww-style.html)

#### 使用场景
- WutheringWavesUID 风格的角色卡片展示

#### 调用方式
```javascript
// apps/roleinfo.js
const renderData = {
  uid,
  game: "ww",
  servName: "库街区",
  element: "hydro",
  chars: processedChars
}

const img = await renderer.render(e, "character/ww-style", renderData)
```

#### 必需数据字段
```javascript
{
  // 基础信息
  uid: "123456789",
  game: "ww",
  servName: "库街区",
  element: "hydro",
  
  // 角色数据
  chars: [
    {
      id: 1404,
      name: "维拉",
      level: 90,
      star: 5,
      element: "hydro",
      cons: 0,
      pic: "/images/character/card/1404.png"
    }
  ]
}
```

### 5. 练度统计 (character/training-stat.html)

#### 使用场景
- miao-plugin 风格的练度统计展示

#### 调用方式
```javascript
// apps/trainingStat.js
const renderData = {
  uid,
  chars: processedChars,
  mode: "training",
  conNum: -1,
  totalCount: 1,
  background: background?.text,
  pct: function(num) {
    return (num * 100).toFixed(1)
  }
}

const img = await renderer.render(e, "character/training-stat", renderData)
```

#### 必需数据字段
```javascript
{
  // 基础信息
  uid: "123456789",
  mode: "training",
  conNum: -1,
  totalCount: 1,
  
  // 角色数据
  chars: [
    {
      id: 1404,
      name: "维拉",
      level: 90,
      star: 5,
      element: "hydro",
      weapon: { level: 90 },
      talent: { a: 10, e: 10, q: 10 },
      cons: 0,
      pic: "/images/character/face/1404.png",
      
      // 练度统计特有字段
      avgLevel: 85.5,
      totalScore: 425,
      rank: 1
    }
  ],
  
  // 工具函数
  pct: function(num) {
    return (num * 100).toFixed(1)
  }
}
```

## 数据处理最佳实践

### 1. 图片路径处理
```javascript
// ✅ 正确方式 - 通过 ImageManager 处理
const renderData = imageMapper.mapRoleDetailImages(charId, roleDetail)

// ❌ 错误方式 - 直接硬编码
const renderData = {
  data: {
    pile: "/images/character/pile/1404.png"  // 不要这样做
  }
}
```

### 2. 数据合并
```javascript
// ✅ 正确方式 - 使用 Object.assign 合并
Object.assign(renderData.data, {
  totalScore: weightData[0]?.totalScore || 0
})

// ❌ 错误方式 - 直接覆盖
renderData.data = processedData  // 会丢失 ImageManager 处理的数据
```

### 3. 条件字段
```javascript
// ✅ 为条件显示的字段提供默认值
renderData.displayMode = renderData.displayMode || "default"
renderData.element = renderData.element || "hydro"
```

### 4. 错误处理
```javascript
// ✅ 验证必需字段
if (!uid) {
  throw new Error("UID is required for rendering")
}

// ✅ 提供降级方案
if (!renderData.chars || renderData.chars.length === 0) {
  await e.reply("❌ 暂无角色数据")
  return
}
```

## 调试技巧

### 1. 数据检查
```javascript
// 渲染前检查数据
console.log(`[模块名] 渲染数据:`, {
  uid: renderData.uid,
  dataKeys: Object.keys(renderData.data || {}),
  charCount: renderData.chars?.length
})
```

### 2. 模板调试
```html
<!-- 在模板中输出调试信息 -->
<!-- DEBUG: uid={{uid}}, displayMode={{displayMode}} -->

<!-- 检查数据是否存在 -->
{{if data.equipPhantomList}}
  <!-- 有声骸数据 -->
{{else}}
  <!-- 无声骸数据 -->
{{/if}}
```

### 3. 开发模式
```javascript
// 在开发模式下，Yunzai 会自动保存渲染数据到 temp/ViewData/
// 可以查看实际传入模板的数据结构
```

## 完整示例

### 角色面板完整示例

#### JavaScript 代码
```javascript
// apps/card.js
async generateCharacterPanel(e, uid, charName) {
  try {
    // 1. 获取角色数据
    const characterData = await User.getCharacterData(uid, charId)

    // 2. 处理数据
    const processedData = dataProcessor.processCharacterData(characterData)

    // 3. 创建角色模型
    const roleDetail = new RoleDetail(processedData)

    // 4. 映射图片路径
    const renderData = imageMapper.mapRoleDetailImages(characterData.roleId, roleDetail)

    // 5. 添加业务字段
    renderData.charName = charName
    renderData.uid = uid
    renderData.element = processedData.element || "hydro"
    renderData.displayMode = "default"
    renderData.saveTime = new Date().toLocaleString()

    // 6. 验证数据
    if (!renderData.data.equipPhantomList) {
      renderData.data.equipPhantomList = []
    }

    // 7. 渲染
    const img = await renderer.render(e, "character/profile-detail", renderData)

    // 8. 返回结果
    return img
  } catch (error) {
    console.error("生成角色面板失败:", error)
    return null
  }
}
```

#### HTML 模板使用
```html
<!-- character/profile-detail.html -->
<!DOCTYPE html>
<html>
<head>
  <link rel="stylesheet" href="{{_res_path}}character/profile-detail.css">
</head>
<body class="elem-{{element}} {{displayMode}}-mode">
  <div class="container">
    <!-- 角色基本信息 -->
    <div class="char-info">
      <div class="char-name">{{charName}}</div>
      <div class="char-level">Lv.{{data.level}}</div>
      <div class="uid">UID: {{uid}}</div>
    </div>

    <!-- 角色立绘 -->
    <div class="char-pic">
      <img src="{{_res_path}}{{data.pile}}">
    </div>

    <!-- 武器信息 (非权重查询模式) -->
    {{if displayMode !== "artis" && data.weapon}}
      <div class="weapon">
        <img src="{{_res_path}}{{data.weapon.pic}}">
        <div class="weapon-name">{{data.weapon.name}}</div>
        <div class="weapon-level">Lv.{{data.weapon.level}}</div>
      </div>
    {{/if}}

    <!-- 声骸信息 -->
    <div class="phantoms">
      {{each data.equipPhantomList phantom}}
        <div class="phantom-item">
          <img src="{{_res_path}}{{phantom.pic}}">
          <div class="phantom-name">{{phantom.name}}</div>
          <div class="phantom-level">+{{phantom.level}}</div>

          <!-- 主属性 -->
          {{each phantom.mainPropList mainProp}}
            <div class="main-prop">
              {{mainProp.attributeName}}: {{mainProp.attributeValue}}
            </div>
          {{/each}}

          <!-- 副属性 -->
          {{each phantom.subPropList subProp}}
            <div class="sub-prop">
              <span>{{subProp.attributeName}}: {{subProp.attributeValue}}</span>
              {{if subProp.score}}
                <span class="score">{{subProp.score.toFixed(1)}}</span>
              {{/if}}
            </div>
          {{/each}}
        </div>
      {{/each}}
    </div>

    <!-- 权重分析 (仅权重查询模式) -->
    {{if displayMode === "artis" && weights}}
      <div class="weight-analysis">
        <h3>权重分析</h3>
        {{each weights weight}}
          <div class="weight-item">
            <span>{{weight.name}}</span>
            <span>{{weight.score.toFixed(1)}}分</span>
          </div>
        {{/each}}
      </div>
    {{/if}}

    <!-- 保存时间 -->
    <div class="save-time">{{saveTime}}</div>
  </div>
</body>
</html>
```

### 权重查询完整示例

#### JavaScript 代码
```javascript
// apps/weightQuery.js
async generateWeightPanel(e, uid, charName) {
  try {
    // 1-4. 数据处理 (同角色面板)
    const renderData = imageMapper.mapRoleDetailImages(characterData.role?.roleId, roleDetail)

    // 5. 添加权重查询特有字段
    renderData.charName = charName
    renderData.uid = uid
    renderData.element = processedData.element
    renderData.displayMode = "artis"  // 关键：权重查询模式
    renderData.saveTime = new Date().toLocaleString()

    // 6. 计算权重数据
    const weightData = await this.calculateWeights(processedData, roleDetail)
    renderData.weights = weightData

    // 7. 为声骸副属性添加分数
    if (renderData.data.equipPhantomList && calcFile) {
      renderData.data.equipPhantomList = renderData.data.equipPhantomList.map(phantom => {
        if (phantom.subPropList) {
          phantom.subPropList = phantom.subPropList.map(subProp => {
            const weight = calcFile.sub_props?.[subProp.attributeName] || 0
            const value = parseFloat(subProp.attributeValue.replace("%", ""))
            const score = isNaN(value) ? 0 : value * weight

            return {
              ...subProp,
              score: score
            }
          })
        }
        return phantom
      })
    }

    // 8. 渲染 (使用相同模板，但 displayMode 不同)
    const img = await renderer.render(e, "character/profile-detail", renderData)
    return img
  } catch (error) {
    console.error("生成权重面板失败:", error)
    return null
  }
}
```

## 常见问题和解决方案

### 1. 图片不显示
**问题**: 模板中图片路径错误
```html
<!-- ❌ 错误 -->
<img src="{{data.pile}}">

<!-- ✅ 正确 -->
<img src="{{_res_path}}{{data.pile}}">
```

### 2. 数据为空
**问题**: 模板中访问不存在的数据
```html
<!-- ❌ 可能出错 -->
<div>{{data.weapon.name}}</div>

<!-- ✅ 安全访问 -->
{{if data.weapon}}
  <div>{{data.weapon.name}}</div>
{{else}}
  <div>暂无武器</div>
{{/if}}
```

### 3. 循环数据为空
**问题**: 循环一个可能为空的数组
```html
<!-- ❌ 可能出错 -->
{{each data.equipPhantomList phantom}}
  <div>{{phantom.name}}</div>
{{/each}}

<!-- ✅ 安全循环 -->
{{if data.equipPhantomList && data.equipPhantomList.length > 0}}
  {{each data.equipPhantomList phantom}}
    <div>{{phantom.name}}</div>
  {{/each}}
{{else}}
  <div>暂无声骸</div>
{{/if}}
```

### 4. 条件显示
**问题**: 根据模式显示不同内容
```html
<!-- 权重查询模式下隐藏武器 -->
{{if displayMode !== "artis"}}
  <div class="weapon">...</div>
{{/if}}

<!-- 权重查询模式下显示权重分析 -->
{{if displayMode === "artis" && weights}}
  <div class="weight-analysis">...</div>
{{/if}}
```

### 5. 数据格式化
**问题**: 数值需要格式化显示
```html
<!-- 保留小数位 -->
<span>{{subProp.score.toFixed(1)}}分</span>

<!-- 百分比处理 -->
<span>{{subProp.attributeValue}}</span>

<!-- 条件格式化 -->
{{if phantom.score && phantom.score[0]}}
  <span>{{phantom.score[0].toFixed(1)}}分 - {{phantom.score[1]}}</span>
{{else}}
  <span>暂无评分</span>
{{/if}}
```

## 性能优化建议

### 1. 数据预处理
```javascript
// ✅ 在 JavaScript 中预处理复杂逻辑
const processedChars = chars.map(char => ({
  ...char,
  displayName: `${char.name} Lv.${char.level}`,
  isMaxLevel: char.level >= 90
}))

// 模板中直接使用
<div>{{char.displayName}}</div>
```

### 2. 避免复杂计算
```html
<!-- ❌ 在模板中进行复杂计算 -->
<div>{{(char.exp / char.maxExp * 100).toFixed(1)}}%</div>

<!-- ✅ 在 JavaScript 中预计算 -->
<div>{{char.expPercent}}%</div>
```

### 3. 合理使用条件
```html
<!-- ✅ 减少重复的条件判断 -->
{{set showWeapon = displayMode !== "artis" && data.weapon}}
{{if showWeapon}}
  <div class="weapon">...</div>
{{/if}}
```

## 使用常量的最佳实践

### 1. 模板路径使用常量
```javascript
// ✅ 使用常量
import { TEMPLATE_PATHS } from "../utils/constants.js"
const img = await renderer.render(e, TEMPLATE_PATHS.CHARACTER_DETAIL, renderData)

// ❌ 硬编码路径
const img = await renderer.render(e, "character/profile-detail", renderData)
```

### 2. 配置值使用常量
```javascript
// ✅ 使用常量
import { DEFAULT_CONFIG, DISPLAY_MODES } from "../utils/constants.js"
renderData.element = DEFAULT_CONFIG.ELEMENT
renderData.displayMode = DISPLAY_MODES.ARTIS

// ❌ 硬编码值
renderData.element = "hydro"
renderData.displayMode = "artis"
```

### 3. 错误消息使用常量
```javascript
// ✅ 使用常量
import { ERROR_MESSAGES } from "../utils/constants.js"
await e.reply(ERROR_MESSAGES.USER_NOT_FOUND)

// ❌ 硬编码消息
await e.reply("❌ 获取用户信息失败")
```

## 总结

正确的数据传入流程：
1. **数据获取**: 从 API 获取原始数据
2. **数据处理**: 使用 DataProcessor 处理
3. **模型创建**: 创建 RoleDetail 等模型
4. **图片映射**: 使用 ImageManager 处理图片路径
5. **业务字段**: 添加特定功能的字段
6. **常量使用**: 使用 constants.js 中的常量替代硬编码
7. **数据验证**: 确保必需字段存在
8. **渲染执行**: 调用 renderer.render()
9. **结果处理**: 发送图片或错误信息

遵循这个流程和本文档的规范，可以确保数据正确传入 HTML 模板并正常渲染。
