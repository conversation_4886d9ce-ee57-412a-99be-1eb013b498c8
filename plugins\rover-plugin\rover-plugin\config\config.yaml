# rover-plugin 默认配置文件
# 详细配置请查看对应的专项配置文件：
# - webLogin.yaml: 网页登录配置
# - proxy.yaml: 代理配置

# 功能开关
features:
  characterPanel: true    # 角色面板功能
  refreshPanel: true      # 刷新面板功能
  smsLogin: true          # 短信验证码登录
  webLogin: true          # 网页登录功能
  ranking: true           # 排行榜功能
  autoSign: false         # 自动签到功能

# 排行配置
ranking:
  # 全局排行token (用于全服排行)
  globalToken: ""

  # 群排行显示数量
  groupDisplayCount: 20

# 刷新面板配置
refreshPanel:
  # 刷新并发数 (建议值: 10-20，使用代理时可以更高)
  concurrency: 15

  # 是否使用全局共享并发限制
  useGlobalSemaphore: true

  # 刷新间隔(秒) 0表示无限制
  interval: 0

  # 刷新间隔通知文案
  intervalNotify: "请等待{}s后尝试刷新面板！"

# 声骸列表配置
echoList:
  # 默认显示数量
  defaultCount: 20

  # 最大显示数量
  maxCount: 50

# 安全配置
security:
  # 最大绑定UID数量
  maxBindNum: 5

  # 是否允许@查询
  atCheck: true

  # 是否可以使用UID直接查询练度
  roleListQuery: true

  # 是否显示UID
  hideUid: false
