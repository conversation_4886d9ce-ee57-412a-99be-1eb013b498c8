import { Sequelize } from "sequelize"
import path from "path"
import fs from "fs"
import sqlite3 from "sqlite3"
import { dataPath } from "../path.js"

const dbDir = path.join(dataPath, "rover", "db")
const dbPath = path.join(dbDir, "rover.db")

// 确保数据库目录存在
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true })
}

const sequelize = new Sequelize({
  dialect: "sqlite",
  storage: dbPath,
  logging: false,
  dialectOptions: {
    mode: sqlite3.OPEN_READWRITE | sqlite3.OPEN_CREATE,
  },
})

await sequelize.authenticate()
await sequelize.query("PRAGMA journal_mode = WAL;")
await sequelize.query("PRAGMA synchronous = NORMAL;")
await sequelize.query("PRAGMA busy_timeout = 5000;")

export default sequelize
