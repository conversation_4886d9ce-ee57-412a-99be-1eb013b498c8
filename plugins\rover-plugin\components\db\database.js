import { DataTypes, Op } from "sequelize"
import sequelize from "./sequelize.js"

const WavesUserDB = sequelize.define(
  "wavesuser",
  {
    id: {
      // 自增id
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false,
    },
    bot_id: {
      // 机器人id onebot
      type: DataTypes.STRING,
      allowNull: false,
    },
    user_id: {
      // qqid
      type: DataTypes.STRING,
      allowNull: false,
    },
    cookie: {
      // 用户cookie,token
      type: DataTypes.STRING,
      allowNull: false,
    },
    stoken: {
      // 无用
      type: DataTypes.STRING,
      allowNull: true,
    },
    status: {
      // 状态 无效 or null or ""
      type: DataTypes.STRING,
      allowNull: true,
    },
    push_switch: {
      // 推送开关
      type: DataTypes.STRING,
      allowNull: false,
    },
    sign_switch: {
      // 签到开关
      type: DataTypes.STRING,
      allowNull: false,
    },
    uid: {
      // 鸣潮uid = wavesId
      type: DataTypes.STRING,
      allowNull: true,
    },
    record_id: {
      // 抽卡记录id = 无用
      type: DataTypes.STRING,
      allowNull: true,
    },
    platform: {
      // 平台 ios, android
      type: DataTypes.STRING,
      allowNull: false,
    },
    stamina_bg_value: {
      // 体力背景值
      type: DataTypes.STRING,
      allowNull: false,
    },
    bbs_sign_switch: {
      // 论坛签到开关
      type: DataTypes.STRING,
      allowNull: false,
    },
    bat: {
      // bat
      type: DataTypes.TEXT,
      allowNull: false,
      defaultValue: "",
    },
    did: {
      // did
      type: DataTypes.TEXT,
      allowNull: false,
      defaultValue: "",
    },
  },
  {
    tableName: "wavesuser",
    timestamps: false,
  },
)

const WavesBindDB = sequelize.define(
  "wavesbind",
  {
    id: {
      // 自增id
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false,
    },
    bot_id: {
      // 机器人id onebot
      type: DataTypes.STRING,
      allowNull: false,
    },
    user_id: {
      // qqid
      type: DataTypes.STRING,
      allowNull: false,
    },
    group_id: {
      // 群id
      type: DataTypes.STRING,
      allowNull: true,
    },
    uid: {
      // 鸣潮uid = wavesId
      type: DataTypes.STRING,
      allowNull: true,
    },
  },
  {
    tableName: "wavesbind",
    timestamps: false,
  },
)

// 创建表
async function initDB() {
  await sequelize.sync({ force: false })
}
initDB()

class WavesUser {
  /**
   * 根据用户ID获取用户token
   * @param {string} user_id - 用户ID
   * @returns {Promise<string|null>} token
   */
  static async getTokenByUser(user_id) {
    try {
      const user = await WavesUserDB.findOne({
        where: { user_id },
      })
      return user ? user.cookie : null
    } catch (error) {
      console.error("获取用户token失败:", error)
      return null
    }
  }

  /**
   * 根据用户ID获取用户DID
   * @param {string} user_id - 用户ID
   * @returns {Promise<string|null>} DID
   */
  static async getDidByUser(user_id) {
    try {
      const user = await WavesUserDB.findOne({
        where: { user_id },
      })
      return user ? user.did : null
    } catch (error) {
      console.error("获取用户DID失败:", error)
      return null
    }
  }

  /**
   * 创建或更新用户数据（改进版：支持多账号）
   * @param {Object} userData - 用户数据
   * @returns {Promise<Object>} 用户记录
   */
  static async createOrUpdate(userData) {
    const transaction = await sequelize.transaction()

    try {
      const { user_id, bot_id, uid } = userData

      // 如果提供了UID，检查是否已存在相同UID的记录
      if (uid && uid !== "" && !uid.startsWith("temp_")) {
        const existingByUid = await WavesUserDB.findOne({
          where: { user_id, bot_id, uid },
          transaction,
        })

        if (existingByUid) {
          // 更新现有记录
          await existingByUid.update(userData, { transaction })
          await transaction.commit()
          return existingByUid
        }
      }

      // 查找用户的主记录（第一个记录或没有UID的记录）
      const existingUser = await WavesUserDB.findOne({
        where: { user_id, bot_id },
        order: [["id", "ASC"]], // 获取最早的记录
        transaction,
      })

      if (existingUser) {
        // 如果现有记录没有UID或是临时UID，更新它
        if (!existingUser.uid || existingUser.uid === "" || existingUser.uid.startsWith("temp_")) {
          await existingUser.update(userData, { transaction })
          await transaction.commit()
          return existingUser
        } else if (uid && uid !== "" && !uid.startsWith("temp_")) {
          // 如果现有记录有UID且新数据也有不同的UID，创建新记录（多账号支持）
          const newUser = await WavesUserDB.create(userData, { transaction })
          await transaction.commit()
          return newUser
        } else {
          // 更新现有记录
          await existingUser.update(userData, { transaction })
          await transaction.commit()
          return existingUser
        }
      } else {
        // 创建新记录
        const newUser = await WavesUserDB.create(userData, { transaction })
        await transaction.commit()
        return newUser
      }
    } catch (error) {
      await transaction.rollback()
      console.error("创建或更新用户数据失败:", error)
      throw error
    }
  }

  /**
   * 获取用户的所有账号
   * @param {string} user_id - 用户ID
   * @param {string} bot_id - 机器人ID
   * @returns {Promise<Array>} 账号列表
   */
  static async getUserAccounts(user_id, bot_id) {
    try {
      const accounts = await WavesUserDB.findAll({
        where: { user_id, bot_id },
        order: [["id", "ASC"]],
      })
      return accounts
    } catch (error) {
      console.error("获取用户账号失败:", error)
      return []
    }
  }

  /**
   * 获取用户的主账号（第一个有效账号）
   * @param {string} user_id - 用户ID
   * @returns {Promise<Object|null>} 主账号
   */
  static async getPrimaryAccount(user_id) {
    try {
      // 优先获取有UID且状态正常的账号
      const account = await WavesUserDB.findOne({
        where: {
          user_id,
          uid: { [Op.ne]: "" },
          status: { [Op.or]: [null, ""] },
        },
        order: [["id", "ASC"]],
      })

      if (account) return account

      // 如果没有，获取第一个账号
      return await WavesUserDB.findOne({
        where: { user_id },
        order: [["id", "ASC"]],
      })
    } catch (error) {
      console.error("获取主账号失败:", error)
      return null
    }
  }

  /**
   * 根据UID获取账号
   * @param {string} user_id - 用户ID
   * @param {string} bot_id - 机器人ID
   * @param {string} uid - 鸣潮UID
   * @returns {Promise<Object|null>} 账号信息
   */
  static async getAccountByUid(user_id, bot_id, uid) {
    try {
      return await WavesUserDB.findOne({
        where: { user_id, bot_id, uid },
      })
    } catch (error) {
      console.error("根据UID获取账号失败:", error)
      return null
    }
  }

  /**
   * 删除账号
   * @param {string} user_id - 用户ID
   * @param {string} bot_id - 机器人ID
   * @param {string} uid - 鸣潮UID
   * @returns {Promise<Object>} 操作结果
   */
  static async removeAccount(user_id, bot_id, uid) {
    const transaction = await sequelize.transaction()

    try {
      // 删除 WavesUser 记录
      const userResult = await WavesUserDB.destroy({
        where: { user_id, bot_id, uid },
        transaction,
      })

      // 删除对应的 WavesBind 记录
      const bindResult = await WavesBind.removeWavesUid(user_id, bot_id, uid, transaction)

      await transaction.commit()

      return {
        success: userResult > 0,
        message: userResult > 0 ? "账号删除成功" : "账号不存在",
        deletedRecords: { user: userResult, bind: bindResult },
      }
    } catch (error) {
      await transaction.rollback()
      console.error("删除账号失败:", error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 标记cookie无效
   * @param {string} uid - 鸣潮uid = wavesId
   * @param {string} cookie - 用户cookie,token
   * @param {string} mark - 状态 无效 or null or ""
   * @returns {Promise<boolean>} 是否成功
   */
  static async markCookieInvalid(uid, cookie, mark = "无效") {
    const [count] = await WavesUserDB.update(
      { status: mark },
      {
        where: {
          uid,
          cookie,
        },
      },
    )
    return count > 0
  }

  /**
   * 查询cookie
   * @param {string} uid - 鸣潮uid = wavesId
   * @param {string} user_id - qqid
   * @param {string} bot_id - 机器人id onebot
   * @returns {Promise<string>} cookie
   */
  static async selectCookie(uid, user_id, bot_id) {
    const user = await WavesUserDB.findOne({
      where: { uid, user_id, bot_id },
    })
    return user ? user.cookie : null
  }

  /**
   * 查询鸣潮用户
   * @param {string} uid - 鸣潮uid = wavesId
   * @param {string} user_id - qqid
   * @param {string} bot_id - 机器人id onebot
   * @returns {Promise<Object>} 鸣潮用户
   */
  static async selectWavesUser(uid, user_id, bot_id) {
    return await WavesUserDB.findOne({
      where: { uid, user_id, bot_id },
    })
  }

  /**
   * 查询用户cookie的uid
   * @param {string} user_id - qqid
   * @returns {Promise<string[]>} uid列表
   */
  static async selectUserCookieUids(user_id) {
    const users = await WavesUserDB.findAll({
      where: {
        user_id,
        cookie: { [Op.ne]: "" },
        [Op.or]: [{ status: null }, { status: "" }],
      },
    })
    return users.map(u => u.uid)
  }

  /**
   * 查询用户
   * @param {string} cookie - 用户cookie,token
   * @returns {Promise<Object>} 用户
   */
  static async selectDataByCookie(cookie) {
    return await WavesUserDB.findOne({ where: { cookie } })
  }

  /**
   * 查询用户
   * @param {string} cookie - 用户cookie,token
   * @param {string} uid - 鸣潮uid = wavesId
   * @returns {Promise<Object>} 用户
   */
  static async selectDataByCookieAndUid(cookie, uid) {
    try {
      // 使用更安全的查询方式
      const { Op } = await import("sequelize")
      return await WavesUserDB.findOne({
        where: {
          [Op.and]: [{ cookie: { [Op.eq]: cookie } }, { uid: { [Op.eq]: uid } }],
        },
      })
    } catch (error) {
      console.warn("数据库查询失败，尝试原始SQL:", error.message)
      try {
        // 使用原始SQL查询
        const { sequelize } = await import("./sequelize.js")
        const [results] = await sequelize.query(
          "SELECT * FROM waves_user WHERE cookie = ? AND uid = ? LIMIT 1",
          {
            replacements: [cookie, uid],
            type: sequelize.QueryTypes.SELECT,
          },
        )
        return results || null
      } catch (sqlError) {
        console.error("原始SQL查询也失败:", sqlError.message)
        return null
      }
    }
  }

  /**
   * 查询用户属性
   * @param {string} user_id - qqid
   * @param {string} bot_id - 机器人id onebot
   * @param {string} attr_key - 属性名
   * @param {string} attr_value - 属性值
   * @returns {Promise<Object>} 用户
   */
  static async getUserByAttr(user_id, bot_id, attr_key, attr_value) {
    const users = await WavesUserDB.findAll({ where: { user_id, bot_id } })
    if (!users.length) return null
    return users.find(u => u[attr_key] === attr_value) || null
  }

  /**
   * 查询所有鸣潮用户
   * @returns {Promise<Object[]>} 鸣潮用户列表
   */
  static async getWavesAllUser() {
    const users = await WavesUserDB.findAll({
      where: {
        [Op.or]: [{ status: null }, { status: "" }],
        cookie: { [Op.ne]: "" },
      },
    })
    return users
  }

  /**
   * 删除所有无效cookie
   * @returns {Promise<number>} 删除数量
   */
  static async deleteAllInvalidCookie() {
    const { count } = await WavesUserDB.destroy({
      where: {
        [Op.or]: [{ status: "无效" }, { cookie: "" }],
      },
    })
    return count
  }

  /**
   * 删除cookie
   * @param {string} uid - 鸣潮uid = wavesId
   * @param {string} user_id - qqid
   * @param {string} bot_id - 机器人id onebot
   * @returns {Promise<number>} 删除数量
   */
  static async deleteCookie(uid, user_id, bot_id) {
    const count = await WavesUserDB.destroy({
      where: { uid, user_id, bot_id },
    })
    return count
  }

  /**
   * 根据ID查找用户记录
   * @param {number} id - 用户记录ID
   * @returns {Promise<Object|null>} 用户记录
   */
  static async findByPk(id) {
    try {
      return await WavesUserDB.findByPk(id)
    } catch (error) {
      console.error("根据ID查找用户记录失败:", error)
      return null
    }
  }

  /**
   * 查找所有用户记录
   * @param {Object} where - 查询条件
   * @returns {Promise<Array>} 用户记录列表
   */
  static async findAll(where = {}) {
    try {
      return await WavesUserDB.findAll({ where })
    } catch (error) {
      console.error("查找用户记录失败:", error)
      return []
    }
  }

  /**
   * 删除用户记录
   * @param {Object} where - 删除条件
   * @param {Object} transaction - 事务对象
   * @returns {Promise<number>} 删除的记录数
   */
  static async destroy(where, transaction = null) {
    try {
      const options = transaction ? { where, transaction } : { where }
      return await WavesUserDB.destroy(options)
    } catch (error) {
      console.error("删除用户记录失败:", error)
      return 0
    }
  }

  /**
   * 更新用户记录
   * @param {Object} values - 更新值
   * @param {Object} where - 更新条件
   * @param {Object} transaction - 事务对象
   * @returns {Promise<Array>} 更新结果
   */
  static async update(values, where, transaction = null) {
    try {
      const options = transaction ? { where, transaction } : { where }
      return await WavesUserDB.update(values, options)
    } catch (error) {
      console.error("更新用户记录失败:", error)
      return [0]
    }
  }
}

class WavesBind {
  /**
   * 根据用户ID获取绑定的UID
   * @param {string} user_id - 用户ID
   * @returns {Promise<string|null>} 绑定的UID
   */
  static async getUidByUser(user_id) {
    try {
      const bind = await WavesBindDB.findOne({
        where: { user_id },
      })

      if (bind && bind.uid) {
        // 如果有多个UID，返回第一个
        const uids = bind.uid.split("_").filter(Boolean)
        return uids.length > 0 ? uids[0] : null
      }
      return null
    } catch (error) {
      console.error("获取用户UID失败:", error)
      return null
    }
  }

  /**
   * 绑定UID到用户
   * @param {string} user_id - 用户ID
   * @param {string} bot_id - 机器人ID
   * @param {string} uid - UID
   * @param {string} group_id - 群组ID（可选）
   * @returns {Promise<Object|null>} 绑定结果
   */
  static async bindUid(user_id, bot_id, uid, group_id = null) {
    try {
      const result = await this.insertWavesUid(user_id, bot_id, uid, group_id)
      if (result === 0) {
        return await WavesBindDB.findOne({ where: { user_id, bot_id } })
      }
      return null
    } catch (error) {
      console.error("绑定UID失败:", error)
      return null
    }
  }

  /**
   * 根据用户ID和机器人ID获取绑定的UID
   * @param {string} user_id - 用户ID
   * @param {string} bot_id - 机器人ID
   * @returns {Promise<string|null>} 绑定的UID
   */
  static async getUidByUserAndBot(user_id, bot_id) {
    try {
      const bind = await WavesBindDB.findOne({
        where: { user_id, bot_id },
      })
      if (bind && bind.uid) {
        // 如果有多个UID，返回第一个
        const uids = bind.uid.split("_").filter(Boolean)
        return uids.length > 0 ? uids[0] : null
      }
      return null
    } catch (error) {
      console.error("获取用户UID失败:", error)
      return null
    }
  }

  /**
   * 根据 group_id 获取该群下所有绑定的 uid 列表
   * @param {string} group_id
   * @returns {Promise<string[]>} uid 列表
   */
  static async getGroupAllUid(group_id) {
    if (!group_id) return []
    const binds = await WavesBindDB.findAll({
      where: {
        group_id: { [Op.substring]: group_id },
      },
    })
    // 取出所有 uid 字段，拆分下划线，去重
    const uidSet = new Set()
    binds.forEach(b => {
      if (b.uid) {
        b.uid.split("_").forEach(u => {
          if (u) uidSet.add(u)
        })
      }
    })
    return Array.from(uidSet)
  }

  /**
   * 插入绑定 uid
   * @param {string} user_id qqid
   * @param {string} bot_id 机器人id onebot
   * @param {string} uid 鸣潮uid = wavesId
   * @param {string} [group_id] 群id
   * @param {boolean} [is_digit=true] 是否为数字
   * @returns {Promise<number>} 0=成功, -1=uid无效, -2=已绑定, -3=uid非数字
   */
  static async insertWavesUid(user_id, bot_id, uid, group_id = null, is_digit = true) {
    if (!uid) return -1
    if (is_digit && !/^[0-9]+$/.test(uid)) return -3

    // 查找是否已存在绑定
    let bind = await WavesBindDB.findOne({ where: { user_id, bot_id } })

    // 第一次绑定
    if (!bind) {
      await WavesBindDB.create({ user_id, bot_id, uid, group_id })
      return 0
    }

    // 多 uid 处理
    let uid_list = bind.uid ? bind.uid.split("_").filter(Boolean) : []
    let group_list = bind.group_id ? bind.group_id.split("_").filter(Boolean) : []

    // 已经绑定了该UID
    if (uid_list.includes(uid)) return -2

    // 更新 uid
    uid_list.push(uid)
    let new_uid = uid_list.join("_")

    // group_id 处理
    if (group_id && !group_list.includes(group_id)) {
      group_list.push(group_id)
    }
    let new_group_id = group_list.join("_")

    await WavesBindDB.update(
      { uid: new_uid, group_id: new_group_id },
      { where: { user_id, bot_id } },
    )
    return 0
  }

  /**
   * 移除绑定的UID（支持事务）
   * @param {string} user_id - 用户ID
   * @param {string} bot_id - 机器人ID
   * @param {string} uid - 要移除的UID
   * @param {Object} transaction - 事务对象
   * @returns {Promise<number>} 移除的记录数
   */
  static async removeWavesUid(user_id, bot_id, uid, transaction = null) {
    try {
      const options = transaction ? { transaction } : {}

      const bind = await WavesBindDB.findOne({
        where: { user_id, bot_id },
        ...options,
      })

      if (!bind || !bind.uid) {
        return 0 // 没有找到绑定记录
      }

      const uid_list = bind.uid.split("_").filter(Boolean)
      const new_uid_list = uid_list.filter(u => u !== uid)

      if (new_uid_list.length === 0) {
        // 如果没有剩余UID，删除整个绑定记录
        await bind.destroy(options)
        return 1
      } else {
        // 更新UID列表
        await bind.update(
          {
            uid: new_uid_list.join("_"),
          },
          options,
        )
        return 1
      }
    } catch (error) {
      console.error("移除绑定UID失败:", error)
      return 0
    }
  }

  /**
   * 获取用户的所有绑定UID（只需要用户ID）
   * @param {string} user_id - 用户ID
   * @returns {Promise<Array>} UID列表
   */
  static async getAllUidsByUser(user_id) {
    try {
      const bind = await WavesBindDB.findOne({
        where: { user_id },
      })

      if (!bind || !bind.uid) {
        return []
      }

      return bind.uid.split("_").filter(Boolean)
    } catch (error) {
      console.error("获取用户所有UID失败:", error)
      return []
    }
  }

  /**
   * 获取用户的所有绑定UID（需要用户ID和机器人ID）
   * @param {string} user_id - 用户ID
   * @param {string} bot_id - 机器人ID
   * @returns {Promise<Array>} UID列表
   */
  static async getAllUidsByUserAndBot(user_id, bot_id) {
    try {
      const bind = await WavesBindDB.findOne({
        where: { user_id, bot_id },
      })

      if (!bind || !bind.uid) {
        return []
      }

      return bind.uid.split("_").filter(Boolean)
    } catch (error) {
      console.error("获取用户所有UID失败:", error)
      return []
    }
  }

  /**
   * 设置默认UID（将指定UID移到第一位）- 只需要用户ID
   * @param {string} user_id - 用户ID
   * @param {string} uid - 要设为默认的UID
   * @returns {Promise<boolean>} 是否成功
   */
  static async setDefaultUid(user_id, uid) {
    try {
      const bind = await WavesBindDB.findOne({
        where: { user_id },
      })

      if (!bind || !bind.uid) {
        return false
      }

      const uid_list = bind.uid.split("_").filter(Boolean)
      if (!uid_list.includes(uid)) {
        return false // UID不存在
      }

      // 将指定UID移到第一位
      const other_uids = uid_list.filter(u => u !== uid)
      const new_uid_list = [uid, ...other_uids]

      await bind.update({ uid: new_uid_list.join("_") })
      return true
    } catch (error) {
      console.error("设置默认UID失败:", error)
      return false
    }
  }

  /**
   * 设置默认UID（将指定UID移到第一位）- 需要用户ID和机器人ID
   * @param {string} user_id - 用户ID
   * @param {string} bot_id - 机器人ID
   * @param {string} uid - 要设为默认的UID
   * @returns {Promise<boolean>} 是否成功
   */
  static async setDefaultUidWithBot(user_id, bot_id, uid) {
    try {
      const bind = await WavesBindDB.findOne({
        where: { user_id, bot_id },
      })

      if (!bind || !bind.uid) {
        return false
      }

      const uid_list = bind.uid.split("_").filter(Boolean)
      if (!uid_list.includes(uid)) {
        return false // UID不存在
      }

      // 将指定UID移到第一位
      const other_uids = uid_list.filter(u => u !== uid)
      const new_uid_list = [uid, ...other_uids]

      await bind.update({ uid: new_uid_list.join("_") })
      return true
    } catch (error) {
      console.error("设置默认UID失败:", error)
      return false
    }
  }

  /**
   * 获取所有绑定记录
   * @returns {Promise<Array>} 所有绑定记录
   */
  static async getAllBinds() {
    try {
      return await WavesBindDB.findAll()
    } catch (error) {
      console.error("获取所有绑定记录失败:", error)
      return []
    }
  }

  /**
   * 根据ID查找绑定记录
   * @param {number} id - 绑定记录ID
   * @returns {Promise<Object|null>} 绑定记录
   */
  static async findById(id) {
    try {
      return await WavesBindDB.findByPk(id)
    } catch (error) {
      console.error("根据ID查找绑定记录失败:", error)
      return null
    }
  }

  /**
   * 删除绑定记录
   * @param {Object} where - 删除条件
   * @param {Object} transaction - 事务对象
   * @returns {Promise<number>} 删除的记录数
   */
  static async destroy(where, transaction = null) {
    try {
      const options = transaction ? { where, transaction } : { where }
      return await WavesBindDB.destroy(options)
    } catch (error) {
      console.error("删除绑定记录失败:", error)
      return 0
    }
  }

  /**
   * 更新绑定记录
   * @param {Object} values - 更新值
   * @param {Object} where - 更新条件
   * @param {Object} transaction - 事务对象
   * @returns {Promise<Array>} 更新结果
   */
  static async update(values, where, transaction = null) {
    try {
      const options = transaction ? { where, transaction } : { where }
      return await WavesBindDB.update(values, options)
    } catch (error) {
      console.error("更新绑定记录失败:", error)
      return [0]
    }
  }

  /**
   * 查找所有绑定记录
   * @param {Object} where - 查询条件
   * @returns {Promise<Array>} 绑定记录列表
   */
  static async findAll(where = {}) {
    try {
      return await WavesBindDB.findAll({ where })
    } catch (error) {
      console.error("查找绑定记录失败:", error)
      return []
    }
  }
}

export { WavesUser, WavesBind, WavesUserDB, WavesBindDB }
