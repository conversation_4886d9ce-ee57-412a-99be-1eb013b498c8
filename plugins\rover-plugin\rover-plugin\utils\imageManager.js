import fs from "fs"
import path from "path"
import https from "https"
import http from "http"
import { dataPath } from "../components/path.js"

/**
 * 统一的图片管理器
 * 整合图片下载、路径映射、类型判断等所有图片相关功能
 */
export class ImageManager {
  constructor() {
    this.pluginRoot = path.join(process.cwd(), "plugins", "rover-plugin")
    this.imageDir = path.join(this.pluginRoot, "resources", "images")
    this.ensureImageDir()
  }

  /**
   * 图片类型映射配置 - 统一的图片类型判断规则
   */
  static IMAGE_TYPE_PATTERNS = [
    { pattern: /chain_icon|Chain/i, type: "chain", desc: "共鸣链图标" },
    {
      pattern: /attribute_icon|role_attribute_icon|Attribute/i,
      type: "attribute",
      desc: "属性图标",
    },
    { pattern: /phantom_icon|element_group_pic|Phantom/i, type: "phantom", desc: "声骸图标" },
    { pattern: /skill_icon|role_skill_icon|Skill/i, type: "skill", desc: "技能图标" },
    { pattern: /weapon_icon|Weapon/i, type: "weapon", desc: "武器图标" },
    { pattern: /role_icon/i, type: "avatar", desc: "角色头像" },
    { pattern: /role_pic/i, type: "pile", desc: "角色立绘" },
  ]

  /**
   * 确保图片目录存在
   */
  ensureImageDir() {
    const types = ImageManager.IMAGE_TYPE_PATTERNS.map(p => p.type)
    const dirs = types.map(type => path.join(this.imageDir, type))

    dirs.forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true })
      }
    })
  }

  /**
   * 统一的图片类型判断逻辑
   * @param {string} url - 图片URL
   * @returns {string} - 图片类型
   */
  getImageType(url) {
    if (!url) return "misc"

    for (const { pattern, type } of ImageManager.IMAGE_TYPE_PATTERNS) {
      if (pattern.test(url)) {
        return type
      }
    }

    return "misc"
  }

  /**
   * 从URL获取文件名
   * @param {string} url - 图片URL
   * @returns {string} - 文件名
   */
  getImageFileName(url) {
    try {
      const urlPath = new URL(url).pathname
      const fileName = path.basename(urlPath)
      return fileName || `image_${Date.now()}.png`
    } catch {
      return `image_${Date.now()}.png`
    }
  }

  /**
   * 检查本地图片文件是否存在
   * @param {string} imageUrl - 图片URL
   * @returns {string|null} - 如果存在返回本地路径，否则返回null
   */
  checkLocalImageExists(imageUrl) {
    if (!imageUrl) return null

    const imageType = this.getImageType(imageUrl)
    const fileName = this.getImageFileName(imageUrl)
    const localPath = path.join(this.imageDir, imageType, fileName)

    if (fs.existsSync(localPath)) {
      return localPath
    }

    return null
  }

  /**
   * 获取本地图片路径（用于模板）
   * @param {string} imageUrl - 图片URL
   * @returns {string} - 本地图片路径或原URL
   */
  getLocalPath(imageUrl) {
    if (!imageUrl || !imageUrl.startsWith("http")) {
      return imageUrl
    }

    const localPath = this.checkLocalImageExists(imageUrl)
    if (localPath) {
      // 转换为相对于插件resources目录的路径
      const pluginResourcesPath = path.join(this.pluginRoot, "resources")
      const relativePath = path.relative(pluginResourcesPath, localPath)
      // 返回相对路径，让模板引擎处理_res_path前缀
      return relativePath.replace(/\\/g, "/") // 转换为web路径格式
    }
    return imageUrl
  }

  /**
   * 从URL下载图片
   * @param {string} url - 图片URL
   * @param {string} localPath - 本地保存路径
   * @returns {Promise<boolean>} - 下载是否成功
   */
  async downloadImage(url, localPath) {
    return new Promise(resolve => {
      // 如果文件已存在，跳过下载
      if (fs.existsSync(localPath)) {
        resolve(true)
        return
      }

      const protocol = url.startsWith("https:") ? https : http

      protocol
        .get(url, response => {
          if (response.statusCode === 200) {
            const fileStream = fs.createWriteStream(localPath)
            response.pipe(fileStream)

            fileStream.on("finish", () => {
              fileStream.close()
              console.log(`✅ 下载成功: ${path.basename(localPath)}`)
              resolve(true)
            })

            fileStream.on("error", err => {
              console.error(`❌ 文件写入失败: ${err.message}`)
              fs.unlink(localPath, () => {}) // 删除不完整的文件
              resolve(false)
            })
          } else {
            console.error(`❌ 下载失败: ${url} (状态码: ${response.statusCode})`)
            resolve(false)
          }
        })
        .on("error", err => {
          console.error(`❌ 网络错误: ${err.message}`)
          resolve(false)
        })
    })
  }

  /**
   * 下载单个图片并返回本地路径
   * @param {string} url - 图片URL
   * @returns {Promise<string>} - 本地图片路径
   */
  async downloadAndGetLocalPath(url) {
    if (!url) return ""

    const imageType = this.getImageType(url)
    const fileName = this.getImageFileName(url)
    const localPath = path.join(this.imageDir, imageType, fileName)

    // 先检查文件是否已存在，避免不必要的下载
    if (fs.existsSync(localPath)) {
      return localPath
    }

    const success = await this.downloadImage(url, localPath)
    if (success) {
      return localPath
    }

    return url // 如果下载失败，返回原URL
  }

  /**
   * 获取图片类型的描述信息
   * @param {string} url - 图片URL
   * @returns {string} - 图片类型描述
   */
  getImageTypeDescription(url) {
    const type = this.getImageType(url)
    const pattern = ImageManager.IMAGE_TYPE_PATTERNS.find(p => p.type === type)
    return pattern ? pattern.desc : "其他图片"
  }

  /**
   * 获取所有支持的图片类型
   * @returns {Array} - 图片类型配置数组
   */
  static getSupportedImageTypes() {
    return ImageManager.IMAGE_TYPE_PATTERNS
  }

  /**
   * 批量并行下载图片
   * @param {Array} urls - 图片URL数组
   * @param {number} concurrency - 并发数，默认10
   * @returns {Promise<Array>} - 下载结果数组
   */
  async batchDownload(urls, concurrency = 10) {
    if (!urls || urls.length === 0) return []

    const results = []
    const executing = []

    for (const url of urls) {
      const promise = this.downloadAndGetLocalPath(url).then(localPath => ({
        url,
        localPath,
        success: localPath !== url,
      }))

      results.push(promise)

      if (urls.length >= concurrency) {
        executing.push(promise)

        if (executing.length >= concurrency) {
          await Promise.race(executing)
          executing.splice(
            executing.findIndex(p => p === promise),
            1,
          )
        }
      }
    }

    return Promise.all(results)
  }

  /**
   * 映射角色数据中的所有图片路径
   * @param {string} wavesId - 角色ID
   * @param {Object} roleDetail - RoleDetail实例
   * @returns {Object} - 映射后的数据
   */
  mapRoleDetailImages(wavesId, roleDetail) {
    // 处理角色基础信息，支持不同的数据结构
    const role = roleDetail.role || roleDetail

    const mappedData = {
      // 基础角色信息
      name: role.roleName || roleDetail.charName,
      level: role.level || roleDetail.level,
      starLevel: role.starLevel || roleDetail.starLevel,
      breach: role.breach || roleDetail.breach,
      chainUnlockNum: role.chainUnlockNum || roleDetail.chainUnlockNum,
      pic: this.getLocalPath(role.roleIconUrl || roleDetail.pic), // 映射角色头像
      pile: this.getLocalPath(role.rolePicUrl || roleDetail.pile), // 映射角色立绘
      isMainRole: role.isMainRole || roleDetail.isMainRole,
    }

    // 武器信息 - 映射图片路径
    const weapon = roleDetail.weapon
      ? {
          name: roleDetail.weapon.weaponName || roleDetail.weapon.name || "",
          level: roleDetail.weapon.level || 1,
          starLevel: roleDetail.weapon.weaponStarLevel || roleDetail.weapon.starLevel || 1,
          breach: roleDetail.weapon.breach || 0,
          resonLevel: roleDetail.weapon.resonLevel || 1,
          pic: this.getLocalPath(roleDetail.weapon.weaponIcon || roleDetail.weapon.pic), // 映射武器图标
          typeName: roleDetail.weapon.typeName || "",
          effect: roleDetail.weapon.effectDescription || roleDetail.weapon.effect || "", // 武器描述
          mainPropList: (roleDetail.weapon.mainPropList || []).map(mainProp => ({
            ...mainProp,
            pic: this.getLocalPath(mainProp.pic),
          })),
        }
      : {}

    // 角色属性 - 处理roleAttributeList，去掉"加成"字样并映射图片
    const roleAttributeList = (roleDetail.roleAttributeList || []).map(attribute => ({
      ...attribute,
      pic: this.getLocalPath(attribute.pic),
    }))

    // 技能信息 - 映射图片路径
    const skillList = (roleDetail.skillList || []).map(skill => ({
      ...skill,
      pic: this.getLocalPath(skill.pic), // RoleDetail类中技能图标字段为pic
    }))

    // 共鸣链信息 - 映射图片路径
    const chainList = (roleDetail.chainList || []).map(chain => ({
      ...chain,
      pic: this.getLocalPath(chain.pic || chain.iconUrl), // RoleDetail类将iconUrl映射为pic
    }))

    // 声骸信息 - 映射图片路径
    const equipPhantomList = (roleDetail.equipPhantomList || []).map(phantom => ({
      ...phantom,
      pic: this.getLocalPath(phantom.phantomProp?.iconUrl || phantom.pic),
      name: phantom.phantomProp?.name || phantom.name,
      // 映射主属性图标
      mainPropList: (phantom.mainPropList || []).map(mainProp => ({
        ...mainProp,
        pic: this.getLocalPath(mainProp.pic),
      })),
      // 映射副属性图标（如果有的话）
      subPropList: (phantom.subPropList || []).map(subProp => ({
        ...subProp,
        pic: subProp.pic ? this.getLocalPath(subProp.pic) : subProp.pic,
      })),
    }))

    // 声骸属性汇总 - 映射图片路径
    const equipPhantomAddPropList = (roleDetail.equipPhantomAddPropList || []).map(prop => ({
      ...prop,
      name: prop.attributeName || prop.name,
      value: prop.attributeValue || prop.value,
      pic: this.getLocalPath(prop.pic || prop.iconUrl),
      valid: prop.valid || "C",
    }))

    return {
      save_id: wavesId,
      // uid 将在调用处设置为用户UID，这里不设置避免覆盖
      game: "ww", // 鸣潮游戏标识
      data: {
        ...mappedData,
        weapon,
        roleAttributeList,
        skillList,
        chainList,
        equipPhantomList,
        equipPhantomAddPropList,
        totalScore: roleDetail.totalScore,
        totalScoreBackground: roleDetail.totalScoreBackground,
      },
      bodyClass: `char-${roleDetail.charName}`,
      mode: "profile",
    }
  }
}

// 创建单例实例
export const imageMapper = new ImageManager()
