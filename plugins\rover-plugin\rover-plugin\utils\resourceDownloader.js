import { imageMapper } from "./imageManager.js"

/**
 * 统一的资源下载管理器
 * 提供统一的资源下载接口，确保逻辑一致性
 */
export class ResourceDownloader {
  constructor() {
    this.downloadingUrls = new Set() // 正在下载的URL，避免重复下载
  }

  /**
   * 检查并下载单个资源
   * @param {string} url - 资源URL
   * @param {string} description - 资源描述（用于日志）
   * @returns {Promise<string>} - 本地路径或原URL
   */
  async ensureResource(url, description = "资源") {
    if (!url) return ""

    // 检查是否已存在
    const existingPath = imageMapper.checkLocalImageExists(url)
    if (existingPath) {
      return existingPath
    }

    // 检查是否正在下载，避免重复下载
    if (this.downloadingUrls.has(url)) {
      console.log(`⏳ ${description}正在下载中，等待完成...`)
      // 等待下载完成
      while (this.downloadingUrls.has(url)) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
      // 重新检查是否已存在
      const newPath = imageMapper.checkLocalImageExists(url)
      return newPath || url
    }

    // 开始下载
    this.downloadingUrls.add(url)
    try {
      console.log(`📥 下载${description}...`)
      const localPath = await imageMapper.downloadAndGetLocalPath(url)
      return localPath
    } catch (error) {
      console.warn(`⚠️ 下载${description}失败: ${error.message}`)
      return url
    } finally {
      this.downloadingUrls.delete(url)
    }
  }

  /**
   * 批量并发下载资源
   * @param {Array} resources - 资源列表 [{url, description}]
   * @param {number} concurrency - 并发数，默认10
   * @returns {Promise<Array>} - 下载结果
   */
  async batchDownload(resources, concurrency = 10) {
    if (!resources || resources.length === 0) return []

    // 过滤已存在的资源
    const needDownload = resources.filter(resource => {
      const existingPath = imageMapper.checkLocalImageExists(resource.url)
      return !existingPath
    })

    if (needDownload.length === 0) {
      console.log(`✅ 所有 ${resources.length} 个资源都已存在，无需下载`)
      return resources.map(resource => ({
        ...resource,
        localPath: imageMapper.checkLocalImageExists(resource.url),
        success: true,
        cached: true,
      }))
    }

    console.log(
      `📊 开始下载: ${resources.length - needDownload.length} 个已存在, ${needDownload.length} 个需下载`,
    )

    // 并发下载
    const downloadPromises = needDownload.map(async (resource, index) => {
      try {
        console.log(
          `📥 [${index + 1}/${needDownload.length}] 下载${resource.description}: ${resource.url}`,
        )
        const localPath = await imageMapper.downloadAndGetLocalPath(resource.url)
        return {
          ...resource,
          localPath,
          success: true,
          cached: false,
        }
      } catch (error) {
        console.warn(`⚠️ 下载${resource.description}失败: ${error.message}`)
        return {
          ...resource,
          localPath: resource.url,
          success: false,
          error: error.message,
        }
      }
    })

    // 控制并发数
    const results = []
    for (let i = 0; i < downloadPromises.length; i += concurrency) {
      const batch = downloadPromises.slice(i, i + concurrency)
      const batchResults = await Promise.all(batch)
      results.push(...batchResults)
    }

    const successCount = results.filter(r => r.success).length
    console.log(`✅ 批量下载完成: ${successCount}/${needDownload.length} 个成功`)

    return results
  }

  /**
   * 下载角色相关的所有资源
   * @param {Object} characterData - 角色数据
   * @returns {Promise<void>}
   */
  async downloadCharacterResources(characterData) {
    const resources = []
    const role = characterData.role || characterData

    // 收集角色头像和立绘
    if (role.roleIconUrl) {
      resources.push({
        url: role.roleIconUrl,
        description: `角色头像(${role.roleName || "未知"})`,
      })
    }
    if (role.rolePicUrl) {
      resources.push({
        url: role.rolePicUrl,
        description: `角色立绘(${role.roleName || "未知"})`,
      })
    }

    // 收集角色属性图标
    if (characterData.roleAttributeList) {
      characterData.roleAttributeList.forEach(attr => {
        if (attr.iconUrl) {
          resources.push({
            url: attr.iconUrl,
            description: `属性图标(${attr.attributeName || "未知"})`,
          })
        }
      })
    }

    // 收集技能图标
    if (characterData.skillList) {
      characterData.skillList.forEach(skill => {
        if (skill.skill?.iconUrl) {
          resources.push({
            url: skill.skill.iconUrl,
            description: `技能图标(${skill.skill.name || "未知"})`,
          })
        }
      })
    }

    // 收集共鸣链图标
    if (characterData.chainList) {
      characterData.chainList.forEach(chain => {
        if (chain.iconUrl) {
          resources.push({
            url: chain.iconUrl,
            description: `共鸣链图标(${chain.name || "未知"})`,
          })
        }
      })
    }

    // 收集武器资源
    if (characterData.weaponData?.weapon?.weaponIcon) {
      resources.push({
        url: characterData.weaponData.weapon.weaponIcon,
        description: `武器图标(${characterData.weaponData.weapon.weaponName || "未知"})`,
      })
    }

    // 收集武器属性图标
    if (characterData.weaponData?.mainPropList) {
      characterData.weaponData.mainPropList.forEach(prop => {
        if (prop.iconUrl) {
          resources.push({
            url: prop.iconUrl,
            description: `武器属性图标(${prop.attributeName || "未知"})`,
          })
        }
      })
    }

    // 收集声骸资源
    if (characterData.phantomData?.equipPhantomList) {
      characterData.phantomData.equipPhantomList.forEach(phantom => {
        // 检查 phantom 是否为 null
        if (!phantom) return

        if (phantom.phantomProp?.iconUrl) {
          resources.push({
            url: phantom.phantomProp.iconUrl,
            description: `声骸图标(${phantom.phantomProp.name || "未知"})`,
          })
        }
        if (phantom.fetterDetail?.iconUrl) {
          resources.push({
            url: phantom.fetterDetail.iconUrl,
            description: `套装图标(${phantom.fetterDetail.name || "未知"})`,
          })
        }
        // 主属性图标
        if (phantom.mainProps) {
          phantom.mainProps.forEach(prop => {
            if (prop && prop.iconUrl) {
              resources.push({
                url: prop.iconUrl,
                description: `声骸主属性图标(${prop.attributeName || "未知"})`,
              })
            }
          })
        }
        // 副属性图标
        if (phantom.subProps) {
          phantom.subProps.forEach(prop => {
            if (prop && prop.iconUrl) {
              resources.push({
                url: prop.iconUrl,
                description: `声骸副属性图标(${prop.attributeName || "未知"})`,
              })
            }
          })
        }
      })
    }

    // 收集声骸属性加成图标
    if (characterData.equipPhantomAddPropList) {
      characterData.equipPhantomAddPropList.forEach(prop => {
        if (prop && prop.iconUrl) {
          resources.push({
            url: prop.iconUrl,
            description: `声骸属性加成图标(${prop.attributeName || "未知"})`,
          })
        }
      })
    }

    // 收集声骸属性列表图标
    if (characterData.equipPhantomAttributeList) {
      characterData.equipPhantomAttributeList.forEach(attr => {
        if (attr && attr.iconUrl) {
          resources.push({
            url: attr.iconUrl,
            description: `声骸属性列表图标(${attr.attributeName || "未知"})`,
          })
        }
      })
    }

    // 批量下载所有资源
    if (resources.length > 0) {
      await this.batchDownload(resources, 8) // 适中的并发数
    }
  }

  /**
   * 批量下载多个角色的资源 - 优化版本，智能检查缺失资源
   * @param {Array} characterList - 角色数据列表
   * @returns {Promise<void>}
   */
  async downloadMultipleCharacterResources(characterList) {
    if (!characterList || characterList.length === 0) return

    console.log(`🔍 检查 ${characterList.length} 个角色的资源完整性...`)

    // 收集所有角色的资源，使用 Map 去重
    const allResources = new Map()

    characterList.forEach((characterData, index) => {
      const roleName = characterData.role?.roleName || `角色${index + 1}`
      const resources = this.collectCharacterResources(characterData)

      resources.forEach(resource => {
        if (!allResources.has(resource.url)) {
          allResources.set(resource.url, {
            ...resource,
            characters: [roleName],
          })
        } else {
          // 记录哪些角色使用了这个资源
          allResources.get(resource.url).characters.push(roleName)
        }
      })
    })

    const uniqueResources = Array.from(allResources.values())

    // 预先过滤出真正缺失的资源
    const missingResources = uniqueResources.filter(resource => {
      const existingPath = imageMapper.checkLocalImageExists(resource.url)
      return !existingPath
    })

    if (missingResources.length === 0) {
      console.log(`✅ 所有 ${uniqueResources.length} 个资源都已存在，无需下载`)
      return
    }

    console.log(
      `📊 资源统计: 总计 ${uniqueResources.length} 个，缺失 ${missingResources.length} 个`,
    )

    // 只下载缺失的资源
    await this.batchDownload(missingResources, 10)

    console.log(`🎉 资源补充完成`)
  }

  /**
   * 收集角色资源但不下载 - 从 downloadCharacterResources 提取的逻辑
   * @param {Object} characterData - 角色数据
   * @returns {Array} 资源列表
   */
  collectCharacterResources(characterData) {
    const resources = []
    const role = characterData.role || characterData

    // 收集角色头像和立绘
    if (role.roleIconUrl) {
      resources.push({
        url: role.roleIconUrl,
        description: `角色头像(${role.roleName || "未知"})`,
      })
    }
    if (role.rolePicUrl) {
      resources.push({
        url: role.rolePicUrl,
        description: `角色立绘(${role.roleName || "未知"})`,
      })
    }

    // 收集角色属性图标
    if (characterData.roleAttributeList) {
      characterData.roleAttributeList.forEach(attr => {
        if (attr.iconUrl) {
          resources.push({
            url: attr.iconUrl,
            description: `属性图标(${attr.attributeName || "未知"})`,
          })
        }
      })
    }

    // 收集技能图标
    if (characterData.skillList) {
      characterData.skillList.forEach(skill => {
        if (skill.skill?.iconUrl) {
          resources.push({
            url: skill.skill.iconUrl,
            description: `技能图标(${skill.skill.name || "未知"})`,
          })
        }
      })
    }

    // 收集共鸣链图标
    if (characterData.chainList) {
      characterData.chainList.forEach(chain => {
        if (chain.iconUrl) {
          resources.push({
            url: chain.iconUrl,
            description: `共鸣链图标(${chain.name || "未知"})`,
          })
        }
      })
    }

    // 收集武器资源
    if (characterData.weaponData?.weapon?.weaponIcon) {
      resources.push({
        url: characterData.weaponData.weapon.weaponIcon,
        description: `武器图标(${characterData.weaponData.weapon.weaponName || "未知"})`,
      })
    }

    // 收集武器属性图标
    if (characterData.weaponData?.mainPropList) {
      characterData.weaponData.mainPropList.forEach(prop => {
        if (prop.iconUrl) {
          resources.push({
            url: prop.iconUrl,
            description: `武器属性图标(${prop.attributeName || "未知"})`,
          })
        }
      })
    }

    // 收集声骸资源
    if (characterData.phantomData?.equipPhantomList) {
      characterData.phantomData.equipPhantomList.forEach(phantom => {
        // 检查 phantom 是否为 null
        if (!phantom) return

        if (phantom.phantomProp?.iconUrl) {
          resources.push({
            url: phantom.phantomProp.iconUrl,
            description: `声骸图标(${phantom.phantomProp.name || "未知"})`,
          })
        }
        if (phantom.fetterDetail?.iconUrl) {
          resources.push({
            url: phantom.fetterDetail.iconUrl,
            description: `套装图标(${phantom.fetterDetail.name || "未知"})`,
          })
        }
        // 主属性图标
        if (phantom.mainProps) {
          phantom.mainProps.forEach(prop => {
            if (prop && prop.iconUrl) {
              resources.push({
                url: prop.iconUrl,
                description: `声骸主属性图标(${prop.attributeName || "未知"})`,
              })
            }
          })
        }
        // 副属性图标
        if (phantom.subProps) {
          phantom.subProps.forEach(prop => {
            if (prop && prop.iconUrl) {
              resources.push({
                url: prop.iconUrl,
                description: `声骸副属性图标(${prop.attributeName || "未知"})`,
              })
            }
          })
        }
      })
    }

    // 收集声骸属性加成图标
    if (characterData.equipPhantomAddPropList) {
      characterData.equipPhantomAddPropList.forEach(prop => {
        if (prop && prop.iconUrl) {
          resources.push({
            url: prop.iconUrl,
            description: `声骸属性加成图标(${prop.attributeName || "未知"})`,
          })
        }
      })
    }

    // 收集声骸属性列表图标
    if (characterData.equipPhantomAttributeList) {
      characterData.equipPhantomAttributeList.forEach(attr => {
        if (attr && attr.iconUrl) {
          resources.push({
            url: attr.iconUrl,
            description: `声骸属性列表图标(${attr.attributeName || "未知"})`,
          })
        }
      })
    }

    return resources
  }
}

// 创建单例实例
export const resourceDownloader = new ResourceDownloader()
