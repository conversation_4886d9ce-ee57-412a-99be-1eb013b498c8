/**
 * 伤害计算系统主入口
 * 基于WutheringWavesUID的伤害计算系统
 * 
 * 完整目录结构:
 * utils/map/
 * ├── id2name.json              # 角色ID名称映射 (使用现有的)
 * ├── alias/                    # 别名映射 (使用现有的)
 * ├── character/                # 角色数据 (使用现有的)
 * └── damage/                   # 伤害计算系统
 *     ├── index.js              # 主入口文件
 *     ├── buff.js               # 角色buff计算
 *     ├── damage.js             # 武器、声骸、套装效果计算
 *     ├── register.js           # 伤害计算器注册管理
 *     ├── panelDamage.js        # 面板伤害计算集成
 *     └── {角色ID}/             # 各角色伤害计算
 *         ├── data.json         # 角色倍率数据
 *         └── damage.js         # 角色伤害计算器
 */

// 导出核心模块
export { WavesDamageCalculator } from "../../wavesDamageCalculator.js"

// 导出buff计算
export {
  applyCharacterBuff,
  getCharacterBuffFunction,
  changliBuff,
  shou<PERSON><PERSON>Buff,
  sanhuaBuff,
  motefeBuff,
  weilinaBuff,
  zhe<PERSON><PERSON>uff,
  yinlinBuff,
  fengzhuBuff
} from "./buff.js"

// 导出伤害计算
export {
  weaponDamage,
  echoDamage,
  phaseDamage,
  checkCharId,
  DAMAGE_TYPE,
  CHAR_ATTR,
  SONATA_ID
} from "./damage.js"

// 导出注册器
export {
  damageDetailRegister,
  damageRankRegister,
  registerAllDamage,
  getDamageCalculatorByName,
  getDamageCalculatorById,
  isCharacterSupported,
  getSupportedCharacters,
  DamageCalculatorFactory
} from "./register.js"

// 导出面板集成
export {
  panelDamageCalculator,
  calculatePanelDamageData,
  PanelDamageCalculator
} from "./panelDamage.js"

// 导出角色伤害计算器
export { ChangliDamageDetail } from "./1205/damage.js"

/**
 * 伤害计算系统初始化
 */
export async function initializeDamageSystem() {
  try {
    console.log("🔥 初始化伤害计算系统...")
    
    // 注册所有角色的伤害计算器
    const { registerAllDamage } = await import("./register.js")
    await registerAllDamage()
    
    console.log("✅ 伤害计算系统初始化完成")
    return true
  } catch (error) {
    console.error("❌ 伤害计算系统初始化失败:", error)
    return false
  }
}

/**
 * 获取系统状态
 */
export async function getDamageSystemStatus() {
  try {
    const { getSupportedCharacters } = await import("./register.js")
    const supportedCharacters = getSupportedCharacters()
    
    return {
      initialized: true,
      supportedCharacters: supportedCharacters.length,
      characters: supportedCharacters,
      features: [
        "角色伤害计算",
        "武器效果计算", 
        "声骸效果计算",
        "套装效果计算",
        "buff效果计算",
        "面板集成"
      ]
    }
  } catch (error) {
    return {
      initialized: false,
      error: error.message
    }
  }
}

/**
 * 便捷的伤害计算接口
 * 供其他模块直接调用
 */
export class DamageCalculationAPI {
  /**
   * 计算角色面板伤害
   * @param {Object} params - 计算参数
   * @returns {Object|null} 伤害计算结果
   */
  static async calculatePanelDamage(params) {
    const { calculatePanelDamageData } = await import("./panelDamage.js")
    return await calculatePanelDamageData(params)
  }

  /**
   * 检查角色是否支持伤害计算
   * @param {string} characterName - 角色名称
   * @returns {boolean} 是否支持
   */
  static async isSupported(characterName) {
    const { isCharacterSupported } = await import("./register.js")
    return isCharacterSupported(characterName)
  }

  /**
   * 获取支持的角色列表
   * @returns {Array} 支持的角色列表
   */
  static async getSupportedCharacters() {
    const { getSupportedCharacters } = await import("./register.js")
    return getSupportedCharacters()
  }

  /**
   * 创建角色伤害计算器
   * @param {string} characterName - 角色名称
   * @returns {Object|null} 伤害计算器实例
   */
  static async createCalculator(characterName) {
    const { DamageCalculatorFactory } = await import("./register.js")
    return DamageCalculatorFactory.create(characterName)
  }
}

/**
 * 默认导出
 */
export default {
  // 初始化
  initializeDamageSystem,
  getDamageSystemStatus,
  
  // API接口
  DamageCalculationAPI,
  
  // 面板集成
  calculatePanelDamageData,
  
  // 核心计算器
  WavesDamageCalculator: () => import("../../wavesDamageCalculator.js").then(m => m.WavesDamageCalculator),
  
  // 工厂类
  DamageCalculatorFactory: () => import("./register.js").then(m => m.DamageCalculatorFactory)
}
