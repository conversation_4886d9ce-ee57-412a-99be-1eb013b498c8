/* WutheringWavesUID 风格的角色卡片样式 */

/* 基础容器 */
body,
.container {
  width: 1000px;
  min-height: 800px;
}

.ww-card-container {
  position: relative;
  width: 100%;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  border-radius: 0;
  overflow: hidden;
}

/* 头部信息区域 */
.header-section {
  position: relative;
  width: 100%;
  height: 300px;
}

.base-info-bg {
  position: absolute;
  top: 170px;
  left: 35px;
  width: 400px;
  height: 120px;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.6));
  border-radius: 15px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.user-info {
  position: absolute;
  left: 120px;
  top: 20px;
}

.user-name {
  font-size: 24px;
  font-weight: bold;
  color: white;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.user-id {
  font-size: 16px;
  color: #ffd700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.user-avatar {
  position: absolute;
  left: 10px;
  top: 10px;
  width: 100px;
  height: 100px;
}

.avatar-ring {
  position: absolute;
  width: 100px;
  height: 100px;
  border: 3px solid #ffd700;
  border-radius: 50%;
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

.avatar-img {
  position: absolute;
  top: 5px;
  left: 5px;
  width: 90px;
  height: 90px;
  border-radius: 50%;
  background: #333;
  background-size: cover;
  background-position: center;
}

.title-bar {
  position: absolute;
  top: 220px;
  right: 50px;
  display: flex;
  gap: 80px;
}

.level-info {
  text-align: center;
}

.level-label {
  font-size: 16px;
  color: #999;
  margin-bottom: 5px;
}

.level-value {
  font-size: 28px;
  font-weight: bold;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* 基本信息区域 */
.basic-info-section {
  position: relative;
  margin-top: 20px;
  padding: 0 50px;
}

.line-title {
  position: relative;
  width: 100%;
  height: 60px;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1), transparent);
  border-left: 4px solid #ffd700;
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.line-title span {
  font-size: 24px;
  font-weight: bold;
  color: white;
  margin-left: 20px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.info-blocks {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.info-block {
  width: 130px;
  height: 120px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(5px);
  position: relative;
  overflow: hidden;
}

.info-block.color-y::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #ffd700, #ffed4e);
}

.info-block.color-g::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #4ade80, #22c55e);
}

.info-block.color-p::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #a855f7, #8b5cf6);
}

.info-value {
  font-size: 28px;
  font-weight: bold;
  color: white;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.info-key {
  font-size: 16px;
  color: #ccc;
  text-align: center;
}

/* 角色信息区域 */
.character-section {
  position: relative;
  padding: 0 50px;
  margin-bottom: 50px;
}

.character-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-top: 20px;
}

.char-card {
  position: relative;
  width: 200px;
  height: 180px;
}

.char-bg {
  position: relative;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.char-bg:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 215, 0, 0.5);
}

.char-avatar {
  position: absolute;
  top: 10px;
  left: 10px;
  width: 130px;
  height: 130px;
  border-radius: 10px;
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.char-avatar .avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.attribute-icon {
  position: absolute;
  top: 13px;
  right: 13px;
  width: 40px;
  height: 40px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.5);
}

.char-level {
  position: absolute;
  bottom: 45px;
  left: 15px;
  font-size: 18px;
  font-weight: bold;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
}

.weapon-bg {
  position: absolute;
  bottom: 5px;
  left: 123px;
  width: 75px;
  height: 75px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.weapon-icon {
  width: 100%;
  height: 100%;
  background: #444;
  border-radius: 6px;
}

.chain-info {
  position: absolute;
  bottom: 20px;
  left: 18px;
  background: rgba(96, 12, 120, 0.8);
  color: white;
  padding: 3px 8px;
  border-radius: 7px;
  font-size: 14px;
  font-weight: bold;
}

.star-display {
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 14px;
  color: #ffd700;
  text-shadow: 0 0 4px rgba(255, 215, 0, 0.8);
  font-weight: bold;
}

.star-display.rarity-5 {
  color: #ffd700;
  text-shadow: 0 0 6px rgba(255, 215, 0, 1);
}

.star-display.rarity-4 {
  color: #a855f7;
  text-shadow: 0 0 6px rgba(168, 85, 247, 0.8);
}

.star-display.rarity-3 {
  color: #3b82f6;
  text-shadow: 0 0 6px rgba(59, 130, 246, 0.8);
}

/* 右侧装饰 */
.right-decoration {
  position: absolute;
  top: 0;
  right: 0;
  width: 90px;
  height: 100%;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.1), transparent);
  opacity: 0.5;
}

/* 底部信息 */
.footer-info {
  text-align: center;
  padding: 20px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .character-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 900px) {
  .character-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .title-bar {
    flex-direction: column;
    gap: 20px;
  }
}
