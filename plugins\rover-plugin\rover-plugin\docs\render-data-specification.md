# Rover Plugin 渲染数据规范文档

## 概述

本文档规范了 rover-plugin 中所有模板渲染时的数据结构，确保数据传递的一致性和代码的可维护性。

## 渲染架构

### 渲染流程
```
应用层 (apps/*.js) 
    ↓ 调用 renderer.render()
统一渲染器 (utils/renderer.js)
    ↓ 调用 e.runtime.render()
Yunzai Runtime (lib/plugins/runtime.js)
    ↓ 调用 puppeteer.screenshot()
Puppeteer 渲染器 (renderers/puppeteer/)
    ↓ 生成图片
返回结果
```

### 核心组件
- **Renderer**: 统一渲染工具 (`utils/renderer.js`)
- **ImageManager**: 图片路径映射 (`utils/imageManager.js`)
- **DataProcessor**: 数据处理 (`utils/dataProcessor.js`)
- **RoleDetail**: 角色数据模型 (`model/roleDetail.js`)

## 通用渲染数据结构

### 基础数据字段
所有模板都会包含以下基础字段：

```javascript
{
  // 系统字段 (由 Yunzai Runtime 自动添加)
  sys: { scale: 1 },
  copyright: "Created By TRSS-Yunzai<span class=\"version\">3.0.0</span>",
  _res_path: "../../../plugins/rover-plugin/resources/",
  _miao_path: "../../../plugins/miao-plugin/resources/",
  _tpl_path: "/path/to/miao-plugin/resources/common/tpl/",
  _plugin: "rover-plugin",
  _htmlPath: "character/profile-detail",
  saveId: "profile-detail",
  tplFile: "./plugins/rover-plugin/resources/character/profile-detail.html",
  
  // 业务字段 (由应用层传入)
  uid: "123456789",           // 用户UID
  game: "ww",                 // 游戏标识
  element: "hydro",           // 元素类型 (用于主题色)
  displayMode: "artis",       // 显示模式
  saveTime: "2024-01-01 12:00:00"  // 保存时间
}
```

## 各模块渲染数据规范

### 1. 角色面板 (profile-detail)

**应用文件**: `apps/card.js`
**模板路径**: `character/profile-detail`
**数据处理**: `ImageManager.mapRoleDetailImages()`

```javascript
const renderData = {
  // 基础信息
  charName: "维拉",
  uid: "123456789",
  element: "hydro",
  displayMode: "default",  // "default" | "artis"
  saveTime: "2024-01-01 12:00:00",
  
  // 角色数据 (由 ImageManager 映射)
  data: {
    // 角色基本信息
    name: "维拉",
    level: 90,
    star: 5,
    element: "hydro",
    pile: "/images/character/pile/1404.png",
    
    // 武器信息
    weapon: {
      name: "苍鳞千嶂",
      level: 90,
      resonLevel: 1,
      pic: "/images/weapon/21020014.png",
      mainPropList: [
        {
          attributeName: "攻击力",
          attributeValue: "587",
          pic: "/images/attribute/attack.png"
        }
      ]
    },
    
    // 技能信息
    skillList: [
      {
        type: "常态攻击",
        level: 10,
        pic: "/images/skill/1404_1.png"
      }
    ],
    
    // 命座信息
    chainList: [
      {
        unlocked: true,
        pic: "/images/chain/1404_1.png"
      }
    ],
    
    // 声骸信息
    equipPhantomList: [
      {
        name: "鸣钟之龟",
        level: 25,
        cost: 4,
        pic: "/images/phantom/43020014.png",
        score: [85.2, "S"],
        mainPropList: [
          {
            attributeName: "攻击力%",
            attributeValue: "22.8%",
            pic: "/images/attribute/attack_percent.png"
          }
        ],
        subPropList: [
          {
            attributeName: "暴击",
            attributeValue: "8.2%",
            score: 12.5,  // 权重查询模式下添加
            valid: "S"
          }
        ]
      }
    ],
    
    // 角色属性 (非权重查询模式)
    roleAttributeList: [
      {
        attributeName: "攻击力",
        attributeValue: "2156",
        pic: "/images/attribute/attack.png",
        valid: "A"
      }
    ],
    
    // 声骸属性汇总 (非权重查询模式)
    equipPhantomAddPropList: [
      {
        attributeName: "攻击力%",
        attributeValue: "43.8%",
        pic: "/images/attribute/attack_percent.png",
        valid: "S"
      }
    ]
  },
  
  // 权重查询特有字段
  weights: [
    {
      name: "攻击力%",
      weight: 1.0,
      value: 43.8,
      score: 43.8,
      scoreGrade: "S"
    }
  ]
}
```

### 2. 角色列表 (profile-list)

**应用文件**: `apps/card.js`
**模板路径**: `character/profile-list`

```javascript
const renderData = {
  uid: "123456789",
  game: "ww",
  servName: "库街区",
  hasNew: false,
  msg: "共 8 个角色",
  background: "bg-01.jpg",
  
  chars: [
    {
      id: 1404,
      name: "维拉",
      level: 90,
      star: 5,
      element: "hydro",
      cons: 0,
      weapon: {
        name: "苍鳞千嶂",
        level: 90,
        star: 5
      },
      pic: "/images/character/face/1404.png",
      weaponPic: "/images/weapon/21020014.png"
    }
  ]
}
```

### 3. 声骸列表 (echo-list)

**应用文件**: `apps/echoList.js`
**模板路径**: `character/echo-list`

```javascript
const renderData = {
  uid: "123456789",
  game: "ww",
  background: "bg-01.jpg",
  
  artis: [
    {
      name: "鸣钟之龟",
      level: 25,
      cost: 4,
      star: 5,
      pic: "/images/phantom/43020014.png",
      score: [85.2, "S"],
      mainProp: {
        name: "攻击力%",
        value: "22.8%"
      },
      subProps: [
        {
          name: "暴击",
          value: "8.2%",
          valid: "S"
        }
      ],
      character: {
        name: "维拉",
        pic: "/images/character/face/1404.png"
      }
    }
  ],
  
  artisKeyTitle: {
    "攻击力": "攻击",
    "攻击力%": "攻击%",
    "暴击": "暴击",
    "暴击伤害": "爆伤"
  }
}
```

### 4. 角色卡片 (ww-style)

**应用文件**: `apps/roleinfo.js`
**模板路径**: `character/ww-style`

```javascript
const renderData = {
  uid: "123456789",
  game: "ww",
  servName: "库街区",
  element: "hydro",
  
  chars: [
    {
      id: 1404,
      name: "维拉",
      level: 90,
      star: 5,
      element: "hydro",
      cons: 0,
      pic: "/images/character/card/1404.png"
    }
  ]
}
```

### 5. 练度统计 (training-stat)

**应用文件**: `apps/trainingStat.js`
**模板路径**: `character/training-stat`

```javascript
const renderData = {
  uid: "123456789",
  game: "ww",
  mode: "training",
  conNum: -1,
  totalCount: 1,
  
  chars: [
    {
      id: 1404,
      name: "维拉",
      level: 90,
      star: 5,
      element: "hydro",
      weapon: { level: 90 },
      talent: { a: 10, e: 10, q: 10 },
      cons: 0,
      pic: "/images/character/face/1404.png",
      
      // 练度统计特有字段
      avgLevel: 85.5,
      totalScore: 425,
      rank: 1
    }
  ],
  
  // 工具函数
  pct: function(num) {
    return (num * 100).toFixed(1)
  }
}
```

### 6. 排行榜 (group-rank)

**应用文件**: `apps/rank.js`
**模板路径**: `rank/group-rank`

```javascript
const renderData = {
  groupId: "123456789",
  updateTime: "2024-01-01 12:00:00",
  totalCount: 50,
  
  rankList: [
    {
      rank: 1,
      uid: "123456789",
      nickname: "玩家1",
      level: 90,
      score: 1250,
      characterCount: 8
    }
  ]
}
```

## 数据处理流程

### 1. 数据获取
```javascript
// 从API获取原始数据
const characterData = await User.getCharacterData(uid, charId)
```

### 2. 数据处理
```javascript
// 使用 DataProcessor 处理数据
const processedData = dataProcessor.processCharacterData(characterData)
```

### 3. 数据模型化
```javascript
// 创建角色详情模型
const roleDetail = new RoleDetail(processedData)
```

### 4. 图片路径映射
```javascript
// 使用 ImageManager 映射图片路径
const renderData = imageMapper.mapRoleDetailImages(charId, roleDetail)
```

### 5. 添加业务字段
```javascript
// 添加特定功能的字段
renderData.charName = charName
renderData.uid = uid
renderData.displayMode = "artis"
```

### 6. 执行渲染
```javascript
// 使用统一渲染器渲染
const img = await renderer.render(e, "character/profile-detail", renderData)
```

## 最佳实践

### 1. 数据一致性
- 所有模块使用相同的数据处理流程
- 统一的字段命名规范
- 统一的图片路径处理

### 2. 错误处理
- 在数据处理的每个环节添加错误处理
- 提供有意义的错误信息
- 确保渲染失败时有合适的降级方案

### 3. 性能优化
- 缓存处理过的数据
- 避免重复的图片路径映射
- 合理使用异步处理

### 4. 可维护性
- 保持数据结构的向后兼容性
- 使用 TypeScript 类型定义（如果可能）
- 详细的代码注释和文档

## 数据字段规范

### 必需字段
以下字段在所有渲染数据中都必须存在：
- `uid`: 用户UID (string)
- `game`: 游戏标识，固定为 "ww" (string)

### 可选字段
- `element`: 元素类型，用于主题色 (string)
- `displayMode`: 显示模式 (string)
- `saveTime`: 保存时间 (string)
- `background`: 背景图片 (string)

### 图片路径规范
所有图片路径都应该：
1. 使用相对路径，以 `/images/` 开头
2. 通过 `ImageManager.getLocalPath()` 方法处理
3. 在模板中使用 `{{_res_path}}{{pic}}` 格式引用

### 分数和评级规范
- `score`: 数值分数，保留1位小数 (number)
- `valid`: 评级等级，可选值: "S", "A", "B", "C" (string)
- `scoreGrade`: 分数等级，与 `valid` 含义相同 (string)

## 代码一致性检查

### 当前存在的问题
1. **权重查询数据覆盖**: `weightQuery.js` 中直接覆盖了 `renderData.data`
2. **字段命名不一致**: 部分地方使用 `attributeName`，部分使用 `name`
3. **分数字段混用**: `score`、`valid`、`scoreGrade` 字段使用不统一

### 建议的修复方案
1. **统一数据合并方式**:
```javascript
// 错误方式
renderData.data = processedData

// 正确方式
Object.assign(renderData.data, {
  // 只添加需要的字段
  totalScore: processedData.totalScore,
  // ...
})
```

2. **统一字段命名**:
```javascript
// 统一使用以下字段名
{
  attributeName: "攻击力%",  // 属性名称
  attributeValue: "22.8%",  // 属性值
  pic: "/images/...",       // 图片路径
  score: 12.5,              // 数值分数
  valid: "S"                // 评级等级
}
```

## 注意事项

1. **图片路径**: 所有图片路径都应该通过 `ImageManager` 处理
2. **数据验证**: 在渲染前验证必要字段的存在
3. **模式区分**: 根据 `displayMode` 字段控制不同的显示逻辑
4. **兼容性**: 保持与 miao-plugin 模板的兼容性
5. **调试**: 使用 console.log 记录关键数据传递过程
6. **数据不可变性**: 避免直接修改从 `ImageManager` 返回的数据
7. **错误降级**: 确保关键字段缺失时有合理的默认值

## 维护指南

### 添加新模板时
1. 在本文档中添加对应的数据结构规范
2. 确保使用统一的渲染器 (`utils/renderer.js`)
3. 遵循现有的字段命名规范
4. 添加适当的错误处理和日志

### 修改现有模板时
1. 检查是否影响其他模块的数据结构
2. 更新相关的文档说明
3. 确保向后兼容性
4. 进行充分的测试

### 调试渲染问题时
1. 检查 `console.log` 输出的数据结构
2. 验证图片路径是否正确
3. 确认模板文件是否存在
4. 检查数据字段是否完整
