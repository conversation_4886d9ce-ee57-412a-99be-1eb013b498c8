body {
  width: 600px;
}

.container {
  width: 600px;
  padding: 0;
  background-size: cover;
  overflow: hidden;
}

.profile-cont {
  padding: 0 10px;
  margin-right: 5px;
}

.basic {
  position: relative;
  margin: 0 -15px 15px -10px;
}

.basic:after {
  content: "";
  display: block;
  position: absolute;
  left: 8px;
  top: 115px;
  bottom: 0;
  right: 8px;
  box-shadow: 0 0 2px 0 #fff;
  border-radius: 5px;
  z-index: 1;
}

.main-pic {
  width: 1400px;
  height: 500px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin-left: -545px;
  position: relative;
  z-index: 2;
}

.detail {
  position: absolute;
  right: 20px;
  top: 20px;
  color: #fff;
  z-index: 3;
}

.char-name {
  font-size: 50px;
  text-shadow: 0 0 3px #000, 2px 2px 4px rgba(0, 0, 0, 0.7);
  text-align: right;
}

.char-lv {
  margin-bottom: 20px;
  text-shadow: 0 0 3px #000, 2px 2px 4px rgba(0, 0, 0, 0.7);
  text-align: right;
}

.char-lv .cons {
  margin-left: 5px;
  vertical-align: bottom;
}

.char-attr {
  backdrop-filter: blur(2px);
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  overflow: hidden;
  margin-top: -5px;
}

.char-attr li {
  width: 100%;
  max-width: 350px;
  font-size: 17px;
  list-style: none;
  height: 32px;
  line-height: 32px;
  text-shadow: 0 0 1px rgba(0, 0, 0, 0.5);
  display: flex;
  padding-left: 3px;
  box-sizing: border-box;
}

.char-attr li:nth-child(even) {
  background: rgba(0, 0, 0, 0.4);
}

.char-attr li:nth-child(odd) {
  background: rgba(50, 50, 50, 0.4);
}

.char-attr .icon {
  width: 26px;
  padding: 2px 5px;
}

.char-attr .icon img {
  display: inline-block;
  height: 16px;
  width: 16px;
}

.char-attr .title {
  flex: 1;
  text-shadow: 0 0 1px rgba(0, 0, 0, 0.8), 1px 1px 3px rgb(0 0 0 / 50%);
  white-space: nowrap;
  overflow: visible;
  min-width: 0;
}

.char-attr .value {
  text-align: right;
  font-weight: normal;
  padding-right: 10px;
  text-shadow: 0 0 1px rgba(0, 0, 0, 0.8), 1px 1px 3px rgb(0 0 0 / 50%);
  white-space: nowrap;
  flex-shrink: 0;
}

.char-cons {
  display: flex;
  width: 250px;
  position: absolute;
  bottom: 5px;
  left: 20px;
}

.char-cons .cons-item {
  flex: 1;
}

.char-cons .talent-icon {
  width: 50px;
  height: 50px;
  margin: 0 -5px;
  padding: 5px;
  display: table;
  border-radius: 50%;
  position: relative;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center center;
  z-index: 90;
}

.char-cons .talent-icon.off {
  filter: grayscale(100%);
  opacity: 0.4;
}

.char-cons .talent-icon-img {
  width: 46%;
  height: 46%;
  position: absolute;
  top: 50%;
  left: 50%;
  margin: -22% 0 0 -23%;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.data-info {
  position: absolute;
  bottom: -10px;
  right: 15px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.85);
  text-align: right;
  text-shadow: 1px 1px 1px #000;
  z-index: 2;
  line-height: 20px;
  padding-right: 5px;
}

.data-info .time {
  margin-left: 5px;
}

.ww-talent {
  margin: 2px 0;
  border-radius: 10px;
  background: url("../common/cont/card-bg.png") top left repeat-x;
  background-size: auto 150%;
  position: relative;
  box-shadow: 0 0 1px 0 #ccc, 2px 2px 4px 0 rgba(50, 50, 50, 0.8);
  overflow: hidden;
  color: #fff;
  height: 62px;
  padding: 8px 8px;
}

.char-talents {
  display: flex;
  width: 100%;
  margin: 0;
  justify-content: space-between;
}

.char-talents.left {
  transform-origin: center left;
  margin: 0;
  width: 100%;
}

.char-talents .talent-item {
  flex: 1;
  margin: 0 0.5px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 1px 3px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  min-height: 44px;
  max-width: 160px;
}

.char-talents .talent-icon {
  width: 36px;
  height: 36px;
  padding: 2px;
  display: table;
  border-radius: 50%;
  position: relative;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center center;
  z-index: 90;
  margin-right: 4px;
  flex-shrink: 0;
}

.char-talents .talent-icon-img {
  width: 46%;
  height: 46%;
  position: absolute;
  top: 50%;
  left: 50%;
  margin: -22% 0 0 -23%;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 移除图标上的等级数字样式 */

/* 技能信息容器 */
.char-talents .talent-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  flex: 1;
  min-height: 44px;
}

.talent-name {
  font-size: 10px;
  color: #fff;
  text-shadow: 0 0 3px #000, 1px 1px 1px #000;
  font-weight: 500;
  line-height: 1.1;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 85px;
}

.talent-level {
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
  width: auto;
  min-width: 28px;
  height: 16px;
  line-height: 16px;
  font-size: 9px;
  text-align: center;
  border-radius: 8px;
  color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  font-weight: 600;
  display: inline-block;
  margin-top: 2px;
  padding: 0 4px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.artis {
  display: flex;
  width: 600px;
  flex-wrap: wrap;
  margin: 10px -5px 5px;
}

.artis .item {
  width: 185px;
  border-radius: 10px;
  background: url("../common/cont/card-bg.png") top left repeat-x;
  background-size: auto 100%;
  margin: 5px;
  position: relative;
  box-shadow: 0 0 1px 0 #ccc, 2px 2px 4px 0 rgba(50, 50, 50, 0.8);
  overflow: hidden;
}

.artis .item.weapon {
  height: auto;
  overflow: visible;
  flex-direction: column;
  display: flex;
}

.artis .weapon .img {
  width: 100px;
  height: 100px;
  top: -10px;
  right: -10px;
  position: absolute;
  z-index: 2;
  background-position: center;
  background-size: contain;
  background-repeat: no-repeat;
}

.artis .weapon .head {
  position: relative;
  height: 90px;
  padding: 15px 0 10px 15px;
  z-index: 3;
  border-radius: 10px 10px 0 0;
  background: linear-gradient(
    to right,
    rgba(0, 0, 0, 0.7),
    rgba(0, 0, 0, 0.7),
    rgba(25, 25, 25, 0.5),
    rgba(25, 25, 25, 0),
    rgba(25, 25, 25, 0)
  );
  color: #fff;
}

.artis .weapon .head strong {
  font-size: 15px;
  margin-bottom: 8px;
  display: block;
}

.artis .weapon .weapon-level-info {
  margin-top: -5px;
}

.artis .weapon .head > span {
  display: block;
}

.artis .weapon span.info {
  font-size: 14px;
  margin-bottom: 8px;
}

.artis .weapon .affix {
  color: #000;
  padding: 0 7px;
  border-radius: 4px;
  font-size: 14px;
  width: 40px;
  margin-right: 5px;
}

.artis .weapon .affix-1 {
  box-shadow: 0 0 4px 0 #a3a3a3 inset;
  background: #ebebebaa;
}

.artis .weapon .affix-2 {
  box-shadow: 0 0 4px 0 #51b72fbd inset;
  background: #ddffdeaa;
}

.artis .weapon .affix-3 {
  box-shadow: 0 0 4px 0 #396cdecf inset;
  background: #ddebffaa;
}

.artis .weapon .affix-4 {
  box-shadow: 0 0 4px 0 #c539debf inset;
  background: #ffddf0aa;
}

.artis .weapon .affix-5 {
  box-shadow: 0 0 4px 0 #deaf39 inset;
  background: #fff6dd;
}

.artis .weapon .weapon-main-attr {
  margin: 2px 0 0 0;
  padding: 0;
  list-style: none;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
  border-radius: 0 !important;
}

.artis .weapon .weapon-main-prop {
  background: rgba(25, 25, 25, 0.5);
  font-size: 16px;
  padding: 6px 8px;
  font-weight: bold;
  min-height: 32px;
  height: auto;
  line-height: normal;
  display: flex;
  align-items: center;
  border: none;
  width: 100%;
  box-sizing: border-box;
  border-radius: 0 !important;
}

.artis .weapon .weapon-main-attr li:nth-child(odd) {
  background: rgba(50, 50, 50, 0.4);
  border-radius: 0 !important;
}

.artis .weapon .weapon-main-attr li:nth-child(even) {
  background: rgba(0, 0, 0, 0.4);
  border-radius: 0 !important;
}

.artis .weapon .weapon-main-prop .icon {
  width: 20px;
  height: 20px;
  padding: 0;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.artis .weapon .weapon-main-prop .icon img {
  width: 100%;
  height: 100%;
}

.artis .weapon .weapon-main-prop .title {
  padding-left: 0;
}

.artis .weapon .weapon-main-prop .title-val {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 0;
}

.artis .weapon .weapon-main-prop .main-prop-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.artis .weapon .weapon-main-prop .main-prop-line .title {
  font-size: 13px;
  color: #fff;
}

.artis .weapon .weapon-main-prop .main-prop-line .val {
  font-size: 13px;
  color: #fff;
  font-weight: bold;
}

.artis .weapon .weapon-desc-cont {
  margin-top: 0;
  padding: 8px;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 0 0 10px 10px;
  overflow: hidden;
  flex: 1;
  display: flex;
  align-items: flex-start;
  width: 100%;
  box-sizing: border-box;
}

.artis .weapon .weapon-desc {
  font-size: 12px;
  line-height: 16px;
  color: #bbb;
  text-shadow: 0 0 1px rgba(0, 0, 0, 0.7);
  word-wrap: break-word;
  white-space: normal;
  overflow-wrap: break-word;
  width: 100%;
  text-align: left;
}

.artis .item.arti {
  overflow: visible;
}

.artis .item.arti .head {
  position: relative;
  border-radius: 10px 10px 0 0;
  text-shadow: 0 0 1px #000, 1px 1px 2px rgba(0, 0, 0, 0.7);
  padding: 15px 10px 5px 15px;
  background: linear-gradient(
    to right,
    rgba(0, 0, 0, 0.7),
    rgba(0, 0, 0, 0.7),
    rgba(25, 25, 25, 0.3),
    rgba(25, 25, 25, 0),
    rgba(25, 25, 25, 0)
  );
  color: #fff;
}

.artis .item.arti .head strong {
  font-size: 15px;
  display: block;
  white-space: nowrap;
  overflow: hidden;
}

.artis .item.arti .head span {
  font-size: 14px;
}

.artis .item.arti .arti-icon {
  left: auto;
  right: 0;
  top: -12px;
  width: 90px;
  height: 90px;
  position: absolute;
}

.artis .item.arti .arti-icon .img {
  width: 100%;
  height: 100%;
  margin: 0;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  -webkit-mask: linear-gradient(45deg, #0000 0, #0005 30%, #000 50%);
}

.artis .item.arti .arti-icon span {
  position: absolute;
  top: 50px;
  right: 8px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 5px;
  height: 18px;
  line-height: 18px;
  padding: 0 3px;
  color: #fff;
  font-size: 12px;
  display: block;
}

.artis ul.detail {
  width: 100%;
  padding: 0;
  position: initial;
  display: table;
  backdrop-filter: blur(2px);
  border-radius: 0 0 10px 10px;
  overflow: hidden;
}

.artis ul.detail li {
  padding: 0 3px;
  font-size: 14px;
  position: relative;
  width: 100%;
  display: table-row;
  line-height: 26px;
  height: 26px;
  white-space: nowrap;
}

.artis ul.detail li:nth-child(even) {
  background: rgba(0, 0, 0, 0.4);
}

.artis ul.detail li:nth-child(odd) {
  background: rgba(50, 50, 50, 0.4);
}

/* 主属性样式 - 与副属性完全一致 */
.artis ul.detail li.arti-main {
  background: rgba(25, 25, 25, 0.5);
  font-size: 14px;
  padding: 0 3px;
  position: relative;
  width: 100%;
  display: table-row;
  line-height: 26px;
  height: 26px;
  white-space: nowrap;
  box-sizing: border-box;
  max-width: 100%;
  overflow: hidden;
}

/* 主属性使用与副属性相同的布局结构 */
.artis ul.detail li.arti-main span {
  position: initial;
  display: table-cell;
  color: #fff;
}

.artis ul.detail li.arti-main span.title {
  text-align: left;
  padding-left: 30px;
  font-size: 13px;
  max-width: 110px;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: bold;
}

.artis ul.detail li.arti-main span.val {
  text-align: right;
  padding-right: 10px;
  font-size: 13px;
  max-width: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: bold;
}

/* 移除旧的复杂布局样式 */
.artis ul.detail li.arti-main .icon,
.artis ul.detail li.arti-main .title-val,
.artis ul.detail li.arti-main .main-prop-line {
  display: none;
}

.artis ul.detail li.sub-prop {
  display: flex;
  align-items: center;
  padding: 0 8px;
}

.artis ul.detail li.sub-prop .dot {
  width: 4px;
  height: 4px;
  background: #fff;
  border-radius: 50%;
  margin-right: 8px;
  flex-shrink: 0;
}

.artis ul.detail li.sub-prop .title {
  flex: 1;
  color: #fff;
  font-size: 14px;
  text-align: left;
}

.artis ul.detail li.sub-prop .val {
  color: #fff;
  font-size: 14px;
  text-align: right;
}

.artis ul.detail li.sub-prop .val.max-value {
  color: #e85656 !important;
}

.phantom-set {
  font-size: 12px;
  color: #aaa;
  padding: 5px 0 0 0;
  margin-top: 5px;
}

.phantom-cost {
  font-size: 12px;
  color: #aaa;
}

.game-ww .char-lv .cons {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
}

/* 声骸属性汇总容器 */
.phantom-summary {
  margin: 6px 0;
}

/* 声骸属性汇总样式 */
.arti-info {
  display: flex;
  margin: 4px 0;
  border-radius: 10px;
  background: url("../common/cont/card-bg.png") top left repeat-x;
  background-size: auto 150%;
  position: relative;
  box-shadow: 0 0 1px 0 #ccc, 2px 2px 4px 0 rgba(50, 50, 50, 0.8);
  overflow: hidden;
  min-height: 100px;
}

/* 左侧评分区域 */
.score-section {
  width: 160px;
  padding: 12px;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 12px;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.score-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.score-value {
  font-size: 28px;
  color: #fff;
  font-weight: 700;
  line-height: 1;
}

.score-grade {
  font-size: 45px;
  font-weight: 700;
  line-height: 1;
}

.artis ul.detail li.sub-prop .title.score-grade.grade-C {
  color: #32eb7c !important;
  font-size: inherit !important;
  font-weight: inherit !important;
  line-height: inherit !important;
}

.artis ul.detail li.sub-prop .title.score-grade.grade-B {
  color: #417deb !important;
  font-size: inherit !important;
  font-weight: inherit !important;
  line-height: inherit !important;
}

.artis ul.detail li.sub-prop .title.score-grade.grade-A {
  color: #d699ff !important;
  font-size: inherit !important;
  font-weight: inherit !important;
  line-height: inherit !important;
}

.artis ul.detail li.sub-prop .title.score-grade.grade-S {
  color: #ffe699 !important;
  font-size: inherit !important;
  font-weight: inherit !important;
  line-height: inherit !important;
}

.artis ul.detail li.sub-prop .val.score-grade.grade-C {
  color: #32eb7c !important;
  font-size: inherit !important;
  font-weight: inherit !important;
  line-height: inherit !important;
}

.artis ul.detail li.sub-prop .val.score-grade.grade-B {
  color: #417deb !important;
  font-size: inherit !important;
  font-weight: inherit !important;
  line-height: inherit !important;
}

.artis ul.detail li.sub-prop .val.score-grade.grade-A {
  color: #d699ff !important;
  font-size: inherit !important;
  font-weight: inherit !important;
  line-height: inherit !important;
}

.artis ul.detail li.sub-prop .val.score-grade.grade-S {
  color: #ffe699 !important;
  font-size: inherit !important;
  font-weight: inherit !important;
  line-height: inherit !important;
}

.char-attr .title.score-grade.grade-C {
  color: #32eb7c !important;
  font-size: inherit !important;
  font-weight: inherit !important;
  line-height: inherit !important;
}

.char-attr .title.score-grade.grade-B {
  color: #417deb !important;
  font-size: inherit !important;
  font-weight: inherit !important;
  line-height: inherit !important;
}

.char-attr .title.score-grade.grade-A {
  color: #d699ff !important;
  font-size: inherit !important;
  font-weight: inherit !important;
  line-height: inherit !important;
}

.char-attr .title.score-grade.grade-S {
  color: #ffe699 !important;
  font-size: inherit !important;
  font-weight: inherit !important;
  line-height: inherit !important;
}

.char-attr .value.score-grade.grade-C {
  color: #32eb7c !important;
  font-size: inherit !important;
  font-weight: inherit !important;
  line-height: inherit !important;
}

.char-attr .value.score-grade.grade-B {
  color: #417deb !important;
  font-size: inherit !important;
  font-weight: inherit !important;
  line-height: inherit !important;
}

.char-attr .value.score-grade.grade-A {
  color: #d699ff !important;
  font-size: inherit !important;
  font-weight: inherit !important;
  line-height: inherit !important;
}

.char-attr .value.score-grade.grade-S {
  color: #ffe699 !important;
  font-size: inherit !important;
  font-weight: inherit !important;
  line-height: inherit !important;
}

.prop-name.score-grade.grade-C {
  color: #32eb7c !important;
  font-size: 11px !important;
  font-weight: normal !important;
  line-height: normal !important;
}

.prop-name.score-grade.grade-B {
  color: #417deb !important;
  font-size: 11px !important;
  font-weight: normal !important;
  line-height: normal !important;
}

.prop-name.score-grade.grade-A {
  color: #d699ff !important;
  font-size: 11px !important;
  font-weight: normal !important;
  line-height: normal !important;
}

.prop-name.score-grade.grade-S {
  color: #ffe699 !important;
  font-size: 11px !important;
  font-weight: normal !important;
  line-height: normal !important;
}

.prop-value.score-grade.grade-C {
  color: #32eb7c !important;
  font-size: 13px !important;
  font-weight: 600 !important;
  line-height: normal !important;
}

.prop-value.score-grade.grade-B {
  color: #417deb !important;
  font-size: 13px !important;
  font-weight: 600 !important;
  line-height: normal !important;
}

.prop-value.score-grade.grade-A {
  color: #d699ff !important;
  font-size: 13px !important;
  font-weight: 600 !important;
  line-height: normal !important;
}

.prop-value.score-grade.grade-S {
  color: #ffe699 !important;
  font-size: 13px !important;
  font-weight: 600 !important;
  line-height: normal !important;
}

.score-grade.grade-C {
  color: #32eb7c;
}

.score-grade.grade-B {
  color: #417deb;
}

.score-grade.grade-A {
  color: #d699ff;
}

.score-grade.grade-S {
  color: #ffe699;
}

.score-grade.grade-SS {
  color: #e3b837;
}

.score-grade.grade-SSS {
  color: #e85656;
}

.score-label {
  font-size: 11px;
  color: #ccc;
  text-align: center;
}

.arti-info-detail {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(4, auto);
  grid-auto-flow: column;
  gap: 2px 4px;
  padding: 10px 12px;
  align-content: start;
}

.arti-prop-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 8px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  min-height: 28px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.prop-name {
  font-size: 11px;
  color: #ccc;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  text-align: left;
}

.prop-value {
  font-size: 13px;
  color: #fff;
  font-weight: 600;
  margin-left: 6px;
  flex-shrink: 0;
  text-align: right;
}

/* 权重分析部分样式 - 仅在artis模式下生效，使用miao-plugin暗色主题 */
.artis-mark-cont {
  margin: 20px 0;
  padding: 0;
  background: transparent;
  border-radius: 0;
  box-shadow: none;
}

.artis-mark-header {
  text-align: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid rgba(255, 255, 255, 0.2);
}

.mark-title {
  font-size: 20px;
  font-weight: bold;
  color: #d3bc8e;
  margin-bottom: 5px;
  font-family: "HYWH-65W", sans-serif;
  text-shadow: 0 0 3px #000, 1px 1px 2px rgba(0, 0, 0, 0.7);
}

.mark-subtitle {
  font-size: 14px;
  color: #ccc;
  font-family: "NZBZ", sans-serif;
  text-shadow: 0 0 1px rgba(0, 0, 0, 0.8);
}

/* 声骸展示样式 - 参考miao-plugin */
.artis {
  display: flex;
  width: 600px;
  flex-wrap: wrap;
  margin: 10px -5px 5px;
  box-sizing: border-box;
}

.artis .item {
  width: 185px;
  border-radius: 10px;
  background: url("../common/cont/card-bg.png") top left repeat-x;
  background-size: auto 100%;
  margin: 5px;
  position: relative;
  box-shadow: 0 0 1px 0 #ccc, 2px 2px 4px 0 rgba(50, 50, 50, 0.8);
  overflow: hidden;
  box-sizing: border-box;
  max-width: 185px;
}

.artis .item.arti {
  overflow: visible;
}

.artis .item.arti .head {
  position: relative;
  border-radius: 10px 10px 0 0;
  text-shadow: 0 0 1px #000, 1px 1px 2px rgba(0, 0, 0, 0.7);
  padding: 15px 10px 5px 15px;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7), rgba(25, 25, 25, 0.3), rgba(25, 25, 25, 0), rgba(25, 25, 25, 0));
  color: #fff;
}

.artis .item.arti .head strong {
  font-size: 15px;
  display: block;
  white-space: nowrap;
  overflow: hidden;
}

.artis .item.arti .head span {
  font-size: 14px;
}

.artis .item.arti .arti-icon {
  left: auto;
  right: 0;
  top: -12px;
  width: 90px;
  height: 90px;
  position: absolute;
}

.artis .item.arti .arti-icon .img {
  width: 100%;
  height: 100%;
  margin: 0;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  -webkit-mask: linear-gradient(45deg, #0000 0, #0005 30%, #000 50%);
  mask: linear-gradient(45deg, #0000 0, #0005 30%, #000 50%);
}

.artis .item.arti .arti-icon span {
  position: absolute;
  top: 50px;
  right: 8px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 5px;
  height: 18px;
  line-height: 18px;
  padding: 0 3px;
  color: #fff;
  font-size: 12px;
  display: block;
}

.artis ul.detail {
  backdrop-filter: blur(2px);
  border-radius: 0 0 10px 10px;
  overflow: hidden;
  width: 100%;
  max-width: 185px;
  padding: 0;
  position: initial;
  display: table;
  box-sizing: border-box;
}

.artis ul.detail li {
  padding: 0 3px;
  font-size: 14px;
  position: relative;
  width: 100%;
  display: table-row;
  line-height: 26px;
  height: 26px;
  white-space: nowrap;
  box-sizing: border-box;
  max-width: 100%;
  overflow: hidden;
}

.artis ul.detail li span {
  position: initial;
  display: table-cell;
  color: #fff;
}

.artis ul.detail li span.title {
  text-align: left;
  padding-left: 30px;
  font-size: 14px;
}

.artis ul.detail li span.title i.eff {
  position: absolute;
  display: block;
  left: 3px;
  top: 4px;
  font-size: 12px;
  font-style: normal;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 5px;
  height: 18px;
  line-height: 18px;
  width: 23px;
  text-align: center;
}

/* 权重查询模式下的副属性分数显示 - 与声骸列表样式一致 */
.artis ul.detail li.sub-prop-with-score {
  padding: 0 3px;
  font-size: 14px;
  position: relative;
  width: 100%;
  display: table-row;
  line-height: 26px;
  height: 26px;
  white-space: nowrap;
  box-sizing: border-box;
  max-width: 100%;
  overflow: hidden;
}

.artis ul.detail li.sub-prop-with-score span {
  position: initial;
  display: table-cell;
  color: #fff;
}

.artis ul.detail li.sub-prop-with-score .title {
  text-align: left;
  padding-left: 30px;
  font-size: 13px;
  max-width: 110px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.artis ul.detail li.sub-prop-with-score .val {
  text-align: right;
  padding-right: 10px;
  font-size: 13px;
  max-width: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.artis ul.detail li.sub-prop-with-score .score {
  color: #ffd700;
  font-size: 11px;
  font-weight: bold;
  text-align: right;
  padding-right: 10px;
  max-width: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.artis ul.detail li span.val {
  text-align: right;
  padding-right: 10px;
  font-size: 14px;
}

.artis ul.detail li.arti-main {
  background: rgba(25, 25, 25, 0.5);
  font-size: 16px;
  padding: 3px 3px;
  font-weight: bold;
}

.artis ul.detail li.arti-main .title {
  padding-left: 15px;
}

.artis ul.detail li.great span.title {
  color: #ffe699;
}

.artis ul.detail li.nouse span {
  color: #888;
}

/* 评分等级颜色 */
.mark-ACE, .mark-MAX {
  color: #e85656;
  font-weight: bold;
}

.mark-SSS, .mark-SS {
  color: #ffe699;
  font-weight: bold;
}

.mark-S, .mark-A {
  color: #d699ff;
  font-weight: bold;
}

.mark-B, .mark-C {
  color: #fff;
  font-weight: normal;
}

/* 权重表部分 - 保持miao-plugin暗色风格 */
.weight-table-section {
  margin-bottom: 25px;
}

.weight-table {
  background: transparent;
  border-radius: 0;
  overflow: hidden;
  border: none;
}

/* 权重表样式 - 使用miao-plugin暗色主题风格 */
.weight-table-container {
  margin: 20px 0;
  padding: 0;
  background: transparent;
  border-radius: 0;
  border: none;
}

.weight-table-title {
  font-size: 24px;
  color: #d3bc8e;
  font-weight: bold;
  margin-bottom: 20px;
  text-align: left;
  text-shadow: 0 0 3px #000, 1px 1px 2px rgba(0, 0, 0, 0.7);
}

.weight-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: transparent;
  border-radius: 0;
  overflow: hidden;
  box-shadow: none;
}

.weight-table-header {
  display: flex;
  background: transparent;
}

.weight-table-header .weight-cell {
  color: #d3bc8e;
  font-weight: bold;
  text-align: center;
  padding: 12px 8px;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(0, 0, 0, 0.4);
  text-shadow: 0 0 1px rgba(0, 0, 0, 0.8);
}

.weight-table-header .weight-cell:last-child {
  border-right: none;
}

.weight-table-row {
  display: flex;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.weight-table-row:nth-child(even) {
  background: rgba(0, 0, 0, 0.4);
}

.weight-table-row:nth-child(odd) {
  background: rgba(50, 50, 50, 0.4);
}

.weight-table-row .weight-cell {
  color: #fff;
  text-shadow: 0 0 1px rgba(0, 0, 0, 0.8);
}

.weight-cell {
  flex: 1;
  padding: 10px 8px;
  text-align: center;
  font-size: 14px;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.weight-cell:last-child {
  border-right: none;
}

.weight-attr-name {
  font-weight: bold;
  text-align: left !important;
  padding-left: 15px !important;
  color: #d3bc8e !important;
}

.weight-table-footer {
  margin-top: 15px;
  padding: 15px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 8px;
}

.weight-note {
  color: #ccc;
  font-size: 14px;
  line-height: 1.6;
  margin: 5px 0;
  text-shadow: 0 0 1px rgba(0, 0, 0, 0.8);
}

/* 保留原有的权重表样式作为备用 */
.cont {
  border-radius: 10px;
  background: url("../common/cont/card-bg.png") top left repeat-x;
  background-size: auto 100%;
  margin: 5px 0;
  position: relative;
  box-shadow: 0 0 1px 0 #ccc, 2px 2px 4px 0 rgba(50, 50, 50, 0.8);
  overflow: hidden;
  color: #fff;
  font-size: 16px;
}

.cont-title {
  background: rgba(0, 0, 0, 0.4);
  color: #d3bc8e;
  padding: 10px 20px;
  text-align: left;
}

.cont-footer {
  padding: 10px 15px;
  font-size: 12px;
  background: rgba(0, 0, 0, 0.5);
  font-weight: normal;
}

.cont-footer div {
  margin: 5px 0;
}

.dmg-cont {
  display: table;
  margin: 0;
  width: 100%;
}

.cont-table {
  display: table;
  width: 100%;
}

.dmg-cont .tr {
  display: table-row;
}

.dmg-cont .tr:nth-child(even) {
  background: rgba(0, 0, 0, 0.4);
}

.dmg-cont .tr:nth-child(odd) {
  background: rgba(50, 50, 50, 0.4);
}

.dmg-cont .tr > div {
  display: table-cell;
  box-shadow: 0 0 1px 0 #fff;
  padding: 8px 10px;
}

.dmg-cont .tr.thead {
  text-align: center;
}

.dmg-cont .tr.thead > div {
  color: #d3bc8e;
  background: rgba(0, 0, 0, 0.4);
  height: 40px;
  font-size: 15px;
}

.dmg-cont .tr .title {
  color: #d3bc8e;
  padding-right: 15px;
  padding-left: 5px;
  text-align: right;
  background: rgba(0, 0, 0, 0.4);
  min-width: 100px;
  font-size: 15px;
  white-space: nowrap;
}

.dmg-cont .tr .value {
  text-align: center;
  color: #fff;
  font-size: 15px;
}

.table-row:hover {
  background: rgba(102, 126, 234, 0.1);
}

.table-row:last-child {
  border-bottom: none;
}



/* 权重配置说明部分 - 使用miao-plugin暗色主题 */
.weight-config-section {
  margin-bottom: 25px;
}

.config-description {
  font-size: 13px;
  color: #ccc;
  margin-bottom: 15px;
  font-style: italic;
  text-shadow: 0 0 1px rgba(0, 0, 0, 0.8);
}

.weight-config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 10px;
}

.config-item {
  background: rgba(0, 0, 0, 0.4);
  border-radius: 8px;
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.config-prop {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.config-prop .prop-name {
  font-size: 13px;
  color: #d3bc8e;
  font-weight: 500;
  text-shadow: 0 0 1px rgba(0, 0, 0, 0.8);
}

.config-prop .prop-type {
  font-size: 10px;
  color: #aaa;
  font-style: italic;
}

.config-value {
  display: flex;
  align-items: center;
  gap: 6px;
}

.config-value .prop-value {
  font-size: 12px;
  color: #fff;
  font-weight: bold;
  font-family: "tttgbnumber", monospace;
  text-shadow: 0 0 1px rgba(0, 0, 0, 0.8);
}

/* 评分说明部分 - 使用miao-plugin暗色主题 */
.score-explanation {
  margin-top: 20px;
}

.explanation-content {
  background: rgba(0, 0, 0, 0.4);
  border-radius: 8px;
  padding: 15px;
  border-left: 4px solid #d3bc8e;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.explanation-content p {
  margin: 8px 0;
  font-size: 12px;
  color: #ccc;
  line-height: 1.5;
  text-shadow: 0 0 1px rgba(0, 0, 0, 0.8);
}
