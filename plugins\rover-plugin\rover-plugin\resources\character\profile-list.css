body,
.container {
  width: 650px;
}
.container > .cont {
  margin-left: 15px;
}
.head-box {
  margin-top: 10px;
}
.head-box .label {
  font-size: 14px;
}
.char-list {
  display: flex;
  flex-wrap: wrap;
  padding: 10px;
}
.char-item {
  margin: 5px 0;
  width: 75px;
  position: relative;
}
.char-item .name {
  margin-top: 5px;
  display: block;
  font-size: 14px;
  color: #fff;
  text-align: center;
  text-shadow: 0 0 1px #000;
  white-space: nowrap;
}
.char-item .name .cons {
  padding: 1px 4px;
  font-size: 12px;
  transform: scale(0.8);
  margin: -2px 0 0 1px;
  vertical-align: middle;
  opacity: 0.9;
}
.char-item.new-char .name:before {
  content: "";
  display: inline-block;
  width: 8px;
  height: 8px;
  background: #9e9e9e;
  border-radius: 50%;
  margin-right: 3px;
}
.char-item.update-char .name:before {
  background: #90e800;
}
.char-item .group-rank {
  position: absolute;
  background: url('./imgs/dmg-rank-bg.png') left top no-repeat;
  background-size: auto 100%;
  left: 0;
  top: 0;
  margin-top: -5px;
  width: 74px;
  height: 74px;
}
.char-item .group-rank.rank-type-mark {
  background-image: url('./imgs/mark-rank-bg.png');
}
.char-item .group-rank span {
  position: absolute;
  font-size: 12px;
  width: 16px;
  height: 16px;
  bottom: 0;
  left: 50%;
  margin-left: -7px;
  text-align: center;
  transform: scale(0.8);
  display: none;
  text-shadow: 0 0 2px #b26f08;
}
.char-item .group-rank.rank-1 {
  background-position: 25% 0;
}
.char-item .group-rank.rank-2 {
  background-position: 50% 0;
}
.char-item .group-rank.rank-3 {
  background-position: 75% 0;
}
.char-item .group-rank.rank-4 {
  background-position: 100% 0;
}
.char-item .group-rank.rank-4 span {
  display: block;
}
.char-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  border: 2px solid #fff;
  box-shadow: 1px 1px 3px 0 #000;
  overflow: visible;
  margin: 0 5px 0 6px;
}
.char-icon .img {
  background-size: auto 100%;
  background-position: top center;
  overflow: hidden;
  border-radius: 50%;
}
.cont-footer {
  display: flex;
  background: rgba(0, 0, 0, 0.4);
  width: 100%;
}
.cont-footer[data-list-1] span {
  width: calc(100% / 2);
}
.cont-footer[data-list-2] span {
  width: calc(100% / 3);
}
.cont-footer .serv {
  text-align: right;
}
.cont-footer .new-tip:before {
  content: "";
  display: inline-block;
  width: 8px;
  height: 8px;
  background: #9e9e9e;
  border-radius: 50%;
  margin-right: 3px;
}
.cont-footer .update-tip {
  position: relative;
  left: -100px;
}
.cont-footer .update-tip:before {
  background: #90e800;
}
.group-rank-icon {
  width: 16px;
  height: 16px;
  background: url("./imgs/mark-icon.png");
  background-size: auto 100%;
  display: inline-block;
  vertical-align: bottom;
  margin: -1px 3px -1px 0;
}
.group-rank-icon.mark-icon {
  background-position: 100% 0;
}
.cont-title {
  padding: 8px 5px 10px;
  background: rgba(0, 0, 0, 0.4);
}
.cont-title span {
  color: #fff;
  width: 50%;
}
.cont-title .rank-time {
  font-size: 12px;
  color: #aaa;
  display: inline-block;
  transform: scale(0.85);
  transform-origin: 0 50%;
  white-space: nowrap;
}
.no-rank .group-rank,
.no-rank.group-rank-tip {
  display: none;
}
/*# sourceMappingURL=profile-list.css.map */
