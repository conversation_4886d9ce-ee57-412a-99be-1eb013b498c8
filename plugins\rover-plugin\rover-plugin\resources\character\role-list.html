{{extend "./plugins/rover-plugin/resources/common/layout/elem.html"}}

{{block 'css'}}
<link rel="stylesheet" type="text/css" href="{{_res_path}}/character/role-list.css"/>
{{/block}}

{{set playerData = baseData || data.baseData || data}}
{{set roleList = roleList || data.roleList || []}}

{{block 'main'}}
<div class="container">
  <div class="head-box">
    <div class="label">面板列表</div>
  </div>

  <div class="cont">
    <div class="uid-info">
      UID: {{playerData.uid || uid}}
    </div>

    {{if playerData.name}}
    <div class="player-info">
      {{playerData.name}} Lv.{{playerData.level || 1}} 世界等级{{playerData.worldLevel || 1}}
    </div>
    {{/if}}

    <div class="tips">
      你可以使用 #鸣潮角色名面板 命令来查看角色面板信息了
    </div>

    <!-- 角色列表标题 -->
    <div class="cont-title">
      <span>角色列表</span>
      <span class="rank-time">共 {{roleList.length || 0}} 个角色</span>
    </div>

    <!-- 角色列表 -->
    <div class="char-list">
      {{each roleList role idx}}
      <div class="char-item">
        <div class="char-icon">
          <div class="img" style="background-image: url('{{_res_path}}/character-images/{{role.roleName}}.webp')"></div>
        </div>
        <div class="name">
          {{role.roleName}}
          {{if role.chainNum > 0}}
          <span class="cons">{{role.chainNum}}</span>
          {{/if}}
        </div>
      </div>
      {{/each}}
    </div>

    <!-- 底部信息 -->
    <div class="cont-footer">
      <span class="update-time">
        {{if playerData.refreshTime}}
        更新时间：{{playerData.refreshTime}}
        {{else}}
        刚刚更新
        {{/if}}
      </span>
      <span class="serv">鸣潮面板</span>
    </div>
  </div>
</div>
{{/block}}
