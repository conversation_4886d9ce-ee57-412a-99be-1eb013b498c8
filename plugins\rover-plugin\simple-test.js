/**
 * 简单API测试 - 单个请求测试
 */

const TEST_CONFIG = {
  token: "eyJhbGciOiJIUzI1NiJ9.eyJjcmVhdGVkIjoxNzUzNjMxMjg2Njc1LCJ1c2VySWQiOjEwODE2NzMyfQ.Z9BLJ1jtanrNfl1JLvcifr8ypSXEu7513sQvy9I-A1c",
  did: "6F02FE7B671ACA64694F19FB67EBEBAD07659846",
}

async function testSingleAPI() {
  console.log("🧪 简单API测试")
  console.log(`Token: ${TEST_CONFIG.token.substring(0, 20)}...`)
  console.log(`DID: ${TEST_CONFIG.did}`)

  try {
    // 动态导入KuroApi
    const KuroApiModule = await import("./components/api/kuroapi.js")
    const KuroApi = KuroApiModule.default
    const api = new KuroApi()

    console.log("\n📡 测试getRoleList API...")
    
    // 等待5秒，避免请求过于频繁
    console.log("⏳ 等待5秒...")
    await new Promise(resolve => setTimeout(resolve, 5000))
    
    const result = await api.getRoleList(TEST_CONFIG.token, TEST_CONFIG.did)
    
    console.log("\n📋 API响应:")
    console.log("Code:", result.code)
    console.log("Message:", result.msg)
    console.log("Data:", result.data)
    
    if (result.code === 200) {
      console.log("✅ API调用成功！")
      if (result.data && result.data.length > 0) {
        console.log(`📊 获取到 ${result.data.length} 个角色`)
        result.data.forEach((role, index) => {
          console.log(`  ${index + 1}. ${role.roleName} (${role.roleId})`)
        })
      }
    } else {
      console.log("❌ API调用失败")
      if (result.code === 270) {
        console.log("🚨 风险控制检测")
        console.log("建议:")
        console.log("1. 检查网络环境")
        console.log("2. 等待更长时间后重试")
        console.log("3. 验证Token是否有效")
        console.log("4. 检查DID是否正确")
      }
    }

  } catch (error) {
    console.error("❌ 测试失败:", error)
  }
}

// 运行测试
testSingleAPI()
