import fs from "fs"
import { id2NamePath } from "../path.js"

/**
 * ID到名称和名称到ID映射的全局变量
 */
let id2NameData = null
let name2IdData = null

/**
 * 初始化映射数据（只执行一次）
 */
function initMappingData() {
  if (id2NameData !== null && name2IdData !== null) {
    return
  }

  try {
    if (fs.existsSync(id2NamePath)) {
      const rawData = fs.readFileSync(id2NamePath, "utf8")
      id2NameData = JSON.parse(rawData)

      // 构建反向映射
      name2IdData = {}
      for (const [id, name] of Object.entries(id2NameData)) {
        name2IdData[name] = id
      }

      console.log(`[Rover Plugin] 成功加载ID映射数据，共${Object.keys(id2NameData).length}条记录`)
    } else {
      console.warn(`[Rover Plugin] ID映射文件不存在: ${id2NamePath}`)
      id2NameData = {}
      name2IdData = {}
    }
  } catch (error) {
    console.error(`[Rover Plugin] 加载ID映射数据失败:`, error)
    id2NameData = {}
    name2IdData = {}
  }
}

/**
 * 根据ID获取名称
 * @param {string|number} id - 要查询的ID
 * @returns {string} 对应的名称，如果找不到则返回原ID
 */
export function getNameById(id) {
  initMappingData()
  if (typeof id === "number") {
    id = id.toString()
  }
  return id2NameData[id] || id
}

/**
 * 根据名称获取ID
 * @param {string} name - 要查询的名称
 * @returns {string|null} 对应的ID，如果找不到则返回null
 */
export function getIdByName(name) {
  initMappingData()
  return name2IdData[name] || null
}

/**
 * 获取所有ID到名称的映射
 * @returns {Object} 完整的ID到名称映射对象
 */
export function getAllId2Name() {
  initMappingData()
  return id2NameData
}

/**
 * 检查ID是否存在
 * @param {string|number} id - 要检查的ID
 * @returns {boolean} ID是否存在
 */
export function hasId(id) {
  initMappingData()
  return id in id2NameData
}

/**
 * 检查名称是否存在
 * @param {string} name - 要检查的名称
 * @returns {boolean} 名称是否存在
 */
export function hasName(name) {
  initMappingData()
  return name in name2IdData
}

/**
 * 获取所有ID列表
 * @returns {string[]} 所有ID的数组
 */
export function getAllIds() {
  initMappingData()
  return Object.keys(id2NameData)
}

/**
 * 获取所有名称列表
 * @returns {string[]} 所有名称的数组
 */
export function getAllNames() {
  initMappingData()
  return Object.keys(name2IdData)
}

// 预初始化数据
initMappingData()
