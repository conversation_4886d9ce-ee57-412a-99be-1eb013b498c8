import {
  AVATAR_PATH,
  ROLE_PILE_PATH,
  FETTER_PATH,
  MATERIAL_PATH,
  PHANTOM_PATH,
  ROLE_DETAIL_CHAINS_PATH,
  ROLE_DETAIL_SKILL_PATH,
} from "./resourcePath.js"
import path from "path"

/**
 * 获取角色头像
 * @param {number|string} charId - 角色ID
 * @returns {string} 头像文件路径
 */
async function getAvatar(charId) {
  const name = `role_head_${charId}.png`
  return path.join(AVATAR_PATH, name)
}

/**
 * 获取角色立绘
 * @param {number|string} charId - 角色ID
 * @returns {string} 立绘文件路径
 */
async function getRolePile(charId) {
  const name = `role_pile_${charId}.png`
  return path.join(ROLE_PILE_PATH, name)
}

/**
 * 获取武器图标
 * @param {number|string} weaponId - 武器ID
 * @returns {string} 武器图标文件路径
 */
async function getWeapon(weaponId) {
  const name = `weapon_${weaponId}.png`
  return path.join(WEAPON_PATH, name)
}

/**
 * 获取技能图片
 * @param {number|string} charId - 角色ID
 * @param {string} skillName - 技能名称
 * @returns {string} 技能图片文件路径
 */
async function getSkill(charId, skillName) {
  skillName = skillName.strip()
  const name = `skill_${skillName}.png`
  return path.join(ROLE_DETAIL_SKILL_PATH, `${charId}`, name)
}

/**
 * 获取共鸣链图片
 * @param {number|string} charId - 角色ID
 * @param {number|string} orderId - 共鸣链ID
 * @returns {string} 共鸣链图片文件路径
 */
async function getChain(charId, orderId) {
  const name = `chain_${orderId}.png`
  return path.join(ROLE_DETAIL_CHAINS_PATH, `${charId}`, name)
}

/**
 * 获取声骸图片
 * @param {number|string} phantomId - 声骸ID
 * @returns {string} 声骸图片文件路径
 */
async function getPhantom(phantomId) {
  const name = `phantom_${phantomId}.png`
  return path.join(PHANTOM_PATH, name)
}

/**
 * 获取套装图片
 * @param {string} fetterName - 套装名称
 * @returns {string} 套装图片文件路径
 */
async function getFetter(fetterName) {
  const name = `fetter_${fetterName}.png`
  return path.join(FETTER_PATH, name)
}

/**
 * 获取材料图片
 * @param {number|string} materialId - 材料ID
 * @returns {string} 材料图片文件路径
 */
async function getMaterial(materialId) {
  const name = `material_${materialId}.png`
  return path.join(MATERIAL_PATH, name)
}

export { getAvatar, getRolePile, getWeapon, getSkill, getChain, getPhantom, getFetter, getMaterial }
