import { getIdByName } from "../id2name.js"

/**
 * 角色Buff计算
 * 基于WutheringWavesUID的buff.py实现
 */

/**
 * 守岸人buff
 * @param {Object} attr - 伤害属性对象
 * @param {number} chain - 共鸣链等级
 * @param {number} resonLevel - 共鸣等级
 * @param {boolean} isGroup - 是否组队
 */
export function shouanrenBuff(attr, chain, resonLevel, isGroup) {
  // 守岸人buff - 角色ID: 1202
  const characterId = getIdByName("守岸人")
  if (!characterId) return
  
  // 这里可以添加守岸人的具体buff逻辑
  // 例如：衍射伤害加成、队伍buff等
}

/**
 * 散华buff
 * @param {Object} attr - 伤害属性对象
 * @param {number} chain - 共鸣链等级
 * @param {number} resonLevel - 共鸣等级
 * @param {boolean} isGroup - 是否组队
 */
export function sanhuaBuff(attr, chain, resonLevel, isGroup) {
  // 散华buff - 角色ID: 1402
  const characterId = getIdByName("散华")
  if (!characterId) return
  
  // 散华的具体buff逻辑
}

/**
 * 莫特斐buff
 * @param {Object} attr - 伤害属性对象
 * @param {number} chain - 共鸣链等级
 * @param {number} resonLevel - 共鸣等级
 * @param {boolean} isGroup - 是否组队
 */
export function motefeBuff(attr, chain, resonLevel, isGroup) {
  // 莫特斐buff - 角色ID: 1411
  const characterId = getIdByName("莫特斐")
  if (!characterId) return
  
  // 莫特斐的具体buff逻辑
}

/**
 * 维里奈buff
 * @param {Object} attr - 伤害属性对象
 * @param {number} chain - 共鸣链等级
 * @param {number} resonLevel - 共鸣等级
 * @param {boolean} isGroup - 是否组队
 */
export function weilinaBuff(attr, chain, resonLevel, isGroup) {
  // 维里奈buff - 角色ID: 1303
  const characterId = getIdByName("维里奈")
  if (!characterId) return
  
  // 维里奈的具体buff逻辑
}

/**
 * 折枝buff
 * @param {Object} attr - 伤害属性对象
 * @param {number} chain - 共鸣链等级
 * @param {number} resonLevel - 共鸣等级
 * @param {boolean} isGroup - 是否组队
 */
export function zhezhiBuff(attr, chain, resonLevel, isGroup) {
  // 折枝buff - 角色ID: 1201
  const characterId = getIdByName("折枝")
  if (!characterId) return
  
  // 折枝的具体buff逻辑
}

/**
 * 长离buff
 * @param {Object} attr - 伤害属性对象
 * @param {number} chain - 共鸣链等级
 * @param {number} resonLevel - 共鸣等级
 * @param {boolean} isGroup - 是否组队
 */
export function changliBuff(attr, chain, resonLevel, isGroup) {
  // 长离buff - 角色ID: 1205
  const characterId = getIdByName("长离")
  if (!characterId) return
  
  // 长离的具体buff逻辑
  // 这里会调用具体的长离伤害计算器中的doBuff方法
}

/**
 * 吟霖buff
 * @param {Object} attr - 伤害属性对象
 * @param {number} chain - 共鸣链等级
 * @param {number} resonLevel - 共鸣等级
 * @param {boolean} isGroup - 是否组队
 */
export function yinlinBuff(attr, chain, resonLevel, isGroup) {
  // 吟霖buff - 角色ID: 1506
  const characterId = getIdByName("吟霖")
  if (!characterId) return
  
  // 吟霖的具体buff逻辑
}

/**
 * 风主buff (漂泊者·气动)
 * @param {Object} attr - 伤害属性对象
 * @param {number} chain - 共鸣链等级
 * @param {number} resonLevel - 共鸣等级
 * @param {boolean} isGroup - 是否组队
 */
export function fengzhuBuff(attr, chain, resonLevel, isGroup) {
  // 风主buff - 角色ID: 1508
  const characterId = getIdByName("漂泊者·气动")
  if (!characterId) return
  
  // 风主的具体buff逻辑
}

/**
 * 根据角色名称获取对应的buff函数
 * @param {string} characterName - 角色名称
 * @returns {Function|null} 对应的buff函数
 */
export function getCharacterBuffFunction(characterName) {
  const buffMap = {
    "守岸人": shouanrenBuff,
    "散华": sanhuaBuff,
    "莫特斐": motefeBuff,
    "维里奈": weilinaBuff,
    "折枝": zhezhiBuff,
    "长离": changliBuff,
    "吟霖": yinlinBuff,
    "漂泊者·气动": fengzhuBuff
  }
  
  return buffMap[characterName] || null
}

/**
 * 应用角色buff
 * @param {Object} attr - 伤害属性对象
 * @param {string} characterName - 角色名称
 * @param {number} chain - 共鸣链等级
 * @param {number} resonLevel - 共鸣等级
 * @param {boolean} isGroup - 是否组队
 */
export function applyCharacterBuff(attr, characterName, chain, resonLevel, isGroup) {
  const buffFunction = getCharacterBuffFunction(characterName)
  if (buffFunction) {
    buffFunction(attr, chain, resonLevel, isGroup)
  }
}

export default {
  shouanrenBuff,
  sanhuaBuff,
  motefeBuff,
  weilinaBuff,
  zhezhiBuff,
  changliBuff,
  yinlinBuff,
  fengzhuBuff,
  getCharacterBuffFunction,
  applyCharacterBuff
}
