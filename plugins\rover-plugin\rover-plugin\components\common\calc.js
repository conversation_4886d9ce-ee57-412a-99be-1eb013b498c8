import fs from "fs"
import path from "path"
import { charWeightPath } from "../path.js"
import { ATTRIBUTE_SHORT_NAME_LIST } from "./const.js"
import { getNameById } from "./id2name.js"

export function getCalcFile(charId) {
  const charName = getNameById(charId)
  if (!charName) {
    return null
  }
  const charWeightFile = path.join(charWeightPath, charName, "calc.json")
  if (!fs.existsSync(charWeightFile)) {
    return null
  }

  const calcFile = JSON.parse(fs.readFileSync(charWeightFile, "utf-8"))
  if (!calcFile) {
    return null
  }

  return calcFile
}

const fix_max_score = 50
const score_interval = ["C", "B", "A", "S", "SS", "SSS"]
const score_valid_interval = ["", "B", "A", "S"]

// 获取最大评分
function getMaxScore(cost, calc_map) {
  let max_score, props_grade
  if (cost === 1) {
    max_score = calc_map.score_max[0]
    props_grade = calc_map.props_grade[0]
  } else if (cost === 3) {
    max_score = calc_map.score_max[1]
    props_grade = calc_map.props_grade[1]
  } else {
    max_score = calc_map.score_max[2]
    props_grade = calc_map.props_grade[2]
  }
  return [max_score, props_grade]
}

function calcPhantomEntry(index, prop, cost, calc_map) {
  const skill_weight = calc_map.skill_weight ?? [0, 0, 0, 0]
  let score = 0
  const main_props = calc_map.main_props
  const sub_pros = calc_map.sub_props

  let pros_temp
  if (index < 2) {
    // 主属性
    pros_temp = main_props[cost]
  } else {
    pros_temp = sub_pros
  }

  if (!pros_temp) {
    return [0, 0]
  }

  let value = prop.attributeValue
  if (String(value).includes("%")) {
    value = parseFloat(value.replace("%", ""))
  } else {
    value = parseFloat(value)
  }

  if (isNaN(value)) {
    return [0, 0]
  }

  const attributeName = prop.attributeName

  if (attributeName === "攻击") {
    if (String(prop.attributeValue).includes("%")) {
      score += (pros_temp["攻击%"] || 0) * value
    } else {
      score += (pros_temp["攻击"] || 0) * value
    }
  } else if (attributeName === "生命") {
    if (String(prop.attributeValue).includes("%")) {
      score += (pros_temp["生命%"] || 0) * value
    } else {
      score += (pros_temp["生命"] || 0) * value
    }
  } else if (attributeName === "防御") {
    if (String(prop.attributeValue).includes("%")) {
      score += (pros_temp["防御%"] || 0) * value
    } else {
      score += (pros_temp["防御"] || 0) * value
    }
  } else if (attributeName === "普攻伤害加成") {
    score += (pros_temp["技能伤害加成"] || 0) * skill_weight[0] * value
  } else if (attributeName === "重击伤害加成") {
    score += (pros_temp["技能伤害加成"] || 0) * skill_weight[1] * value
  } else if (attributeName === "共鸣技能伤害加成") {
    score += (pros_temp["技能伤害加成"] || 0) * skill_weight[2] * value
  } else if (attributeName === "共鸣解放伤害加成") {
    score += (pros_temp["技能伤害加成"] || 0) * skill_weight[3] * value
  } else if (ATTRIBUTE_SHORT_NAME_LIST.includes(attributeName.substring(0, 2))) {
    score += (pros_temp["属性伤害加成"] || 0) * value
  } else {
    score += (pros_temp[attributeName] || 0) * value
  }

  const [max_score] = getMaxScore(cost, calc_map)
  if (!max_score) {
    return [0, 0]
  }
  const percent_score = score / max_score
  const final_score = Math.floor(percent_score * fix_max_score * 100) / 100
  return [score, final_score]
}

export function calcPhantomScore(prop_list, cost, calc_map) {
  if (!calc_map) {
    return [0, score_interval[0]]
  }

  let score = 0
  for (const [index, prop] of prop_list.entries()) {
    if (!prop) {
      continue
    }
    const [_score] = calcPhantomEntry(index, prop, cost, calc_map)
    score += _score
  }

  const [max_score, props_grade] = getMaxScore(cost, calc_map)
  if (!max_score) {
    return [0, score_interval[0]]
  }
  const percent_score = score / max_score

  let _temp = 0
  if (props_grade) {
    for (const [index, _temp_per] of props_grade.entries()) {
      if (percent_score >= _temp_per) {
        _temp = index
      }
    }
  }

  const final_score = Math.floor(percent_score * fix_max_score * 100) / 100
  const score_level = score_interval[_temp]
  return [final_score, score_level]
}

export function getTotalScoreBackground(score, calc_map) {
  if (!calc_map) {
    return score_interval[0]
  }

  const ratio = score / 250
  let _temp = 0
  if (calc_map.total_grade) {
    for (const [index, _score] of calc_map.total_grade.entries()) {
      if (ratio >= _score) {
        _temp = index
      }
    }
  }
  const score_level = score_interval[_temp]
  return score_level
}

export function getPhantomColorBackground(name, calc_map) {
  if (!calc_map) {
    return score_valid_interval[0]
  }

  const _temp = calc_map.grade
  if (_temp.valid_s && _temp.valid_s.includes(name)) {
    return score_valid_interval[3]
  }
  if (_temp.valid_a && _temp.valid_a.includes(name)) {
    return score_valid_interval[2]
  }
  if (_temp.valid_b && _temp.valid_b.includes(name)) {
    return score_valid_interval[1]
  }

  return score_valid_interval[0]
}
