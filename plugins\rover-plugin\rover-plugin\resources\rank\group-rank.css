/* 群排行榜样式 - 参考miao-plugin设计 */

.font-YS {
  font-family: Number, "汉仪文黑-65W", YS, PingFangSC-Medium, "PingFang SC", sans-serif;
}

body {
  width: 800px;
  margin: 0;
  padding: 0;
}

.container {
  width: 800px;
  padding: 0;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  overflow: hidden;
  border-radius: 12px;
}

.rank-cont {
  padding: 20px;
  color: #fff;
}

/* 排行榜头部 */
.rank-header {
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rank-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 32px;
  filter: drop-shadow(0 0 10px #ffd700);
}

.title-text {
  font-size: 24px;
  font-weight: bold;
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.rank-info {
  text-align: right;
}

.group-info {
  font-size: 14px;
  color: #ccc;
  margin-bottom: 4px;
}

.update-time {
  font-size: 12px;
  color: #4ade80;
  margin-bottom: 4px;
}

.total-count {
  font-size: 14px;
  color: #ffd700;
  font-weight: bold;
}

/* 排行榜列表 */
.rank-list-section {
  margin-bottom: 20px;
}

.rank-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.rank-item {
  background: rgba(255,255,255,0.1);
  border-radius: 12px;
  padding: 15px;
  border: 1px solid rgba(255,255,255,0.2);
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.rank-item:hover {
  background: rgba(255,255,255,0.15);
  transform: translateX(5px);
}

.rank-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(to bottom, #4a9eff, #ffd700);
}

.top-three {
  background: linear-gradient(135deg, rgba(255,215,0,0.1) 0%, rgba(255,255,255,0.1) 100%);
  border-color: rgba(255,215,0,0.3);
}

.top-three::before {
  background: linear-gradient(to bottom, #ffd700, #ffed4e);
}

.rank-1 {
  background: linear-gradient(135deg, rgba(255,215,0,0.2) 0%, rgba(255,255,255,0.1) 100%);
  border-color: rgba(255,215,0,0.5);
  box-shadow: 0 0 20px rgba(255,215,0,0.3);
}

.rank-2 {
  background: linear-gradient(135deg, rgba(192,192,192,0.2) 0%, rgba(255,255,255,0.1) 100%);
  border-color: rgba(192,192,192,0.5);
}

.rank-3 {
  background: linear-gradient(135deg, rgba(205,127,50,0.2) 0%, rgba(255,255,255,0.1) 100%);
  border-color: rgba(205,127,50,0.5);
}

/* 排名标识 */
.rank-number {
  flex-shrink: 0;
  width: 50px;
  text-align: center;
}

.medal {
  font-size: 24px;
  filter: drop-shadow(0 0 5px currentColor);
}

.medal.gold {
  color: #ffd700;
}

.medal.silver {
  color: #c0c0c0;
}

.medal.bronze {
  color: #cd7f32;
}

.rank-num {
  font-size: 20px;
  font-weight: bold;
  color: #ffd700;
  background: rgba(255,215,0,0.2);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(255,215,0,0.3);
}

/* 用户信息 */
.user-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 15px;
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid rgba(255,255,255,0.3);
  flex-shrink: 0;
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-details {
  flex: 1;
}

.user-uid {
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 8px;
}

.user-stats {
  display: flex;
  gap: 20px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: 11px;
  color: #ccc;
  margin-bottom: 2px;
}

.stat-value {
  font-size: 14px;
  font-weight: bold;
}

.stat-value.power {
  color: #ff6b6b;
}

.stat-value.level {
  color: #4ade80;
}

.stat-value.count {
  color: #4a9eff;
}

/* 更新信息 */
.update-info {
  flex-shrink: 0;
  text-align: right;
}

.last-update {
  font-size: 12px;
  color: #ccc;
  background: rgba(255,255,255,0.1);
  padding: 4px 8px;
  border-radius: 6px;
}

/* 空状态 */
.empty-rank {
  text-align: center;
  padding: 60px 20px;
  color: #ccc;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 15px;
  opacity: 0.5;
}

.empty-text {
  font-size: 20px;
  margin-bottom: 10px;
  color: #fff;
}

.empty-hint {
  font-size: 14px;
  opacity: 0.7;
}

/* 底部说明 */
.rank-footer {
  background: rgba(255,255,255,0.05);
  border-radius: 12px;
  padding: 15px;
  border: 1px solid rgba(255,255,255,0.1);
}

.footer-tips {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tip-item {
  font-size: 12px;
  color: #ccc;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  body {
    width: 100%;
    max-width: 600px;
  }
  
  .container {
    width: 100%;
    max-width: 600px;
  }
  
  .rank-header {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }
  
  .user-stats {
    flex-direction: column;
    gap: 8px;
  }
  
  .rank-item {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }
}
