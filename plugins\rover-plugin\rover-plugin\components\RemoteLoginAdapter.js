import fetch from "node-fetch"
import config from "./config.js"

/**
 * 远程登录服务适配器
 * 支持 tyql688/ww-login 外部登录服务
 */
export class RemoteLoginAdapter {
  constructor() {
    this.baseUrl = config.get("webLogin.remote.url", "")
    this.prefix = "/waves" // 固定API路径前缀
    this.pendingLogins = new Map() // 存储待处理的登录，与本地登录服务器保持一致
    this.tokenExpiry = 5 * 60 * 1000 // 5分钟过期，与本地登录服务器一致

    if (!this.baseUrl) {
      throw new Error("远程登录服务URL未配置")
    }
  }

  /**
   * 生成登录token（添加过期逻辑）
   */
  async generateToken(userId, botId) {
    try {
      const response = await fetch(`${this.baseUrl}${this.prefix}/token`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          user_id: userId,
          bot_id: botId,
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      const token = data.token

      // 存储token信息，设置5分钟过期（与本地登录服务器保持一致）
      if (token) {
        this.pendingLogins.set(token, {
          userId,
          botId,
          createTime: Date.now(),
          expireTime: Date.now() + this.tokenExpiry,
        })

        // 5分钟后自动清理过期token
        setTimeout(() => {
          this.cleanupExpiredToken(token)
        }, this.tokenExpiry)
      }

      return token
    } catch (error) {
      console.error("生成登录token失败:", error)
      throw error
    }
  }

  /**
   * 获取登录URL（添加过期检查）
   */
  getLoginUrl(token) {
    // 检查token是否过期
    const tokenData = this.pendingLogins.get(token)
    if (!tokenData || Date.now() > tokenData.expireTime) {
      throw new Error("登录token已过期，请重新生成")
    }

    return `${this.baseUrl}${this.prefix}/i/${token}`
  }

  /**
   * 检查登录结果
   */
  async checkLoginResult(token) {
    try {
      const response = await fetch(`${this.baseUrl}${this.prefix}/get`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          token: token,
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      // 检查是否有登录数据
      if (!data.ck || !data.did) {
        return null // 还未登录或登录失败
      }

      return {
        token: data.ck,
        did: data.did,
        userId: data.user_id,
        mobile: data.mobile,
      }
    } catch (error) {
      console.error("检查登录结果失败:", error)
      throw error
    }
  }

  /**
   * 完整的登录流程
   */
  async createLoginSession(userId, botId) {
    try {
      // 1. 生成token
      const token = await this.generateToken(userId, botId)

      // 2. 生成登录URL
      const loginUrl = this.getLoginUrl(token)

      return {
        token,
        loginUrl,
        checkResult: () => this.checkLoginResult(token),
      }
    } catch (error) {
      console.error("创建登录会话失败:", error)
      throw error
    }
  }
}

export default RemoteLoginAdapter
