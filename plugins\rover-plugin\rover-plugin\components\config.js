import fs from "fs"
import path from "path"
import YAML from "yaml"
import { pluginPath } from "./path.js"

/**
 * 配置管理器 - 完全从配置文件读取，无硬编码默认值
 */
class ConfigManager {
  constructor() {
    this.configDir = path.join(pluginPath, "config")
    this.defSetDir = path.join(pluginPath, "defSet")
    this.configFiles = [
      "config.yaml", // 主配置
      "webLogin.yaml", // 网页登录配置
      "proxy.yaml", // 代理配置
      "command.yaml", // 命令前缀配置
    ]

    this.ensureConfigFiles()
    this.config = this.loadAllConfigs()
  }

  /**
   * 确保配置文件存在，如果不存在则从defSet复制
   */
  ensureConfigFiles() {
    try {
      // 确保config目录存在
      if (!fs.existsSync(this.configDir)) {
        fs.mkdirSync(this.configDir, { recursive: true })
        console.log(`📁 创建配置目录: ${this.configDir}`)
      }

      // 检查并复制每个配置文件
      for (const fileName of this.configFiles) {
        const userConfigPath = path.join(this.configDir, fileName)
        const defConfigPath = path.join(this.defSetDir, fileName)

        if (!fs.existsSync(userConfigPath)) {
          if (fs.existsSync(defConfigPath)) {
            const defContent = fs.readFileSync(defConfigPath, "utf8")
            fs.writeFileSync(userConfigPath, defContent, "utf8")
            console.log(`📄 从defSet复制配置文件: ${fileName}`)
          } else {
            console.warn(`⚠️ 默认配置文件不存在: ${defConfigPath}`)
          }
        }
      }
    } catch (error) {
      console.error("确保配置文件存在时发生错误:", error)
    }
  }

  /**
   * 加载所有配置文件
   */
  loadAllConfigs() {
    try {
      let mergedConfig = {}

      // 加载每个配置文件
      for (const fileName of this.configFiles) {
        const userConfigPath = path.join(this.configDir, fileName)

        if (fs.existsSync(userConfigPath)) {
          const content = fs.readFileSync(userConfigPath, "utf8")
          const config = YAML.parse(content) || {}
          mergedConfig = this.deepMerge(mergedConfig, config)
        }
      }

      return mergedConfig
    } catch (error) {
      console.error("加载配置文件失败:", error)
      return {}
    }
  }

  /**
   * 深度合并配置对象
   */
  deepMerge(target, source) {
    const result = { ...target }

    for (const key in source) {
      if (source[key] && typeof source[key] === "object" && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(result[key] || {}, source[key])
      } else {
        result[key] = source[key]
      }
    }

    return result
  }

  /**
   * 重新加载配置
   */
  reload() {
    this.config = this.loadAllConfigs()
  }

  /**
   * 获取配置值
   */
  get(key, defaultValue = null) {
    const keys = key.split(".")
    let value = this.config

    for (const k of keys) {
      if (value && typeof value === "object" && k in value) {
        value = value[k]
      } else {
        return defaultValue
      }
    }

    return value
  }

  /**
   * 设置配置值
   */
  set(key, value) {
    try {
      const keys = key.split(".")
      let current = this.config

      for (let i = 0; i < keys.length - 1; i++) {
        const k = keys[i]
        if (!current[k] || typeof current[k] !== "object") {
          current[k] = {}
        }
        current = current[k]
      }

      current[keys[keys.length - 1]] = value
      return true
    } catch (error) {
      console.error("设置配置项失败:", error)
      return false
    }
  }

  // ==================== 代理配置 ====================

  getLocalProxyUrl() {
    const localProxy = this.get("proxy.localProxy")
    return localProxy || null
  }

  getKuroUrlProxy() {
    const kuroUrlProxy = this.get("proxy.kuroUrlProxy")
    // 如果配置为空，返回默认的库街区API地址
    return kuroUrlProxy || "https://api.kurobbs.com"
  }

  getNeedProxyFunc() {
    return this.get("proxy.needProxyFunc")
  }

  needProxy(funcName) {
    const proxyFuncs = this.getNeedProxyFunc()

    // 只有获取验证码请求不使用代理
    const excludeFromProxy = ["getSmsCode"]
    if (excludeFromProxy.includes(funcName)) {
      return false
    }

    return proxyFuncs && (proxyFuncs.includes("all") || proxyFuncs.includes(funcName))
  }

  // ==================== 代理轮换配置 ====================

  isProxyRotationEnabled() {
    return this.get("proxy.proxyRotation.enabled") || false
  }

  getProxyRotationList() {
    return this.get("proxy.proxyRotation.proxies") || []
  }

  isKuroProxyRotationEnabled() {
    return this.get("proxy.kuroProxyRotation.enabled") || false
  }

  getKuroProxyRotationList() {
    return this.get("proxy.kuroProxyRotation.proxies") || []
  }

  /**
   * 获取可用的代理列表（包含主代理和轮换代理）
   * @returns {Array} 代理列表
   */
  getAllProxies() {
    const proxies = []

    // 添加主代理
    const localProxy = this.getLocalProxyUrl()
    if (localProxy) {
      proxies.push(localProxy)
    }

    // 如果启用了代理轮换，添加轮换代理
    if (this.isProxyRotationEnabled()) {
      const rotationProxies = this.getProxyRotationList()
      proxies.push(...rotationProxies)
    }

    return proxies
  }

  /**
   * 获取可用的库街区API代理列表
   * @returns {Array} 库街区API代理列表
   */
  getAllKuroUrlProxies() {
    const proxies = []

    // 添加主库街区API代理
    const kuroUrlProxy = this.getKuroUrlProxy()
    if (kuroUrlProxy && kuroUrlProxy !== "https://api.kurobbs.com") {
      proxies.push(kuroUrlProxy)
    }

    // 如果启用了库街区API代理轮换，添加轮换代理
    if (this.isKuroProxyRotationEnabled()) {
      const kuroUrlProxies = this.getKuroProxyRotationList()
      proxies.push(...kuroUrlProxies)
    }

    // 如果没有配置任何代理，返回默认地址
    if (proxies.length === 0) {
      proxies.push("https://api.kurobbs.com")
    }

    return proxies
  }

  // ==================== 网页登录配置 ====================

  getWebLoginMode() {
    return this.get("webLogin.mode")
  }

  isLocalLoginMode() {
    return this.getWebLoginMode() === "local"
  }

  isRemoteLoginMode() {
    return this.getWebLoginMode() === "remote"
  }

  getLocalLoginPort() {
    return this.get("webLogin.local.port")
  }

  getLocalLoginHost() {
    return this.get("webLogin.local.host")
  }

  getRemoteLoginUrl() {
    return this.get("webLogin.remote.url")
  }

  getLocalLoginUrl() {
    // 优先使用配置的登录URL（用于内网穿透等场景）
    const customLoginUrl = this.get("webLogin.local.loginUrl")
    if (customLoginUrl) {
      return customLoginUrl.endsWith("/") ? customLoginUrl.slice(0, -1) : customLoginUrl
    }

    // 否则使用默认的 host:port 格式
    const host = this.getLocalLoginHost()
    const port = this.getLocalLoginPort()
    return `http://${host}:${port}`
  }

  // ==================== 功能开关 ====================

  isFeatureEnabled(feature) {
    return this.get(`features.${feature}`)
  }

  // ==================== 安全配置 ====================

  getMaxBindNum() {
    return this.get("security.maxBindNum")
  }

  isAtCheckEnabled() {
    return this.get("security.atCheck")
  }

  isRoleListQueryEnabled() {
    return this.get("security.roleListQuery")
  }

  isHideUid() {
    return this.get("security.hideUid")
  }

  // ==================== 排行配置 ====================

  getGlobalRankToken() {
    return this.get("ranking.globalToken")
  }

  getGroupDisplayCount() {
    return this.get("ranking.groupDisplayCount")
  }

  // ==================== 刷新面板配置 ====================

  getRefreshConcurrency() {
    return this.get("refreshPanel.concurrency")
  }

  useGlobalSemaphore() {
    return this.get("refreshPanel.useGlobalSemaphore")
  }

  getRefreshInterval() {
    return this.get("refreshPanel.interval")
  }

  getRefreshIntervalNotify() {
    return this.get("refreshPanel.intervalNotify")
  }

  // ==================== 命令前缀配置 ====================

  getDefaultPrefixes() {
    return this.get("command.defaultPrefixes") || []
  }

  getCustomPrefixes() {
    return this.get("command.customPrefixes") || []
  }

  isDefaultPrefixesDisabled() {
    return this.get("command.disableDefaultPrefixes") || false
  }

  isEmptyPrefixAllowed() {
    return this.get("command.allowEmptyPrefix") || false
  }

  getCommandSeparator() {
    return this.get("command.separator") || ""
  }

  /**
   * 获取所有可用的命令前缀
   */
  getAllPrefixes() {
    let prefixes = []

    // 添加自定义前缀
    const customPrefixes = this.getCustomPrefixes()
    if (customPrefixes && customPrefixes.length > 0) {
      prefixes = prefixes.concat(customPrefixes)
    }

    // 如果没有禁用默认前缀，添加默认前缀
    if (!this.isDefaultPrefixesDisabled()) {
      const defaultPrefixes = this.getDefaultPrefixes()
      if (defaultPrefixes && defaultPrefixes.length > 0) {
        prefixes = prefixes.concat(defaultPrefixes)
      }
    }

    // 如果没有任何前缀且不允许空前缀，回退到默认前缀
    if (prefixes.length === 0 && !this.isEmptyPrefixAllowed()) {
      console.warn("⚠️ 配置错误：没有可用的命令前缀，自动回退到默认前缀")
      const defaultPrefixes = this.getDefaultPrefixes()
      if (defaultPrefixes && defaultPrefixes.length > 0) {
        prefixes = prefixes.concat(defaultPrefixes)
      } else {
        // 如果连默认前缀都没有，使用硬编码前缀
        prefixes = ["mc"]
      }
    }

    return prefixes
  }

  /**
   * 生成命令正则表达式
   * @param {string} command - 命令名称（不包含前缀）
   * @returns {string} 正则表达式字符串
   */
  generateCommandRegex(command) {
    const prefixes = this.getAllPrefixes()
    const separator = this.getCommandSeparator()
    const allowEmpty = this.isEmptyPrefixAllowed()

    let regexParts = []

    // 添加前缀选项
    if (prefixes.length > 0) {
      const escapedPrefixes = prefixes.map(prefix => this.escapeRegex(prefix))
      regexParts.push(`(${escapedPrefixes.join("|")})${this.escapeRegex(separator)}`)
    }

    // 如果允许空前缀，添加空前缀选项
    if (allowEmpty) {
      regexParts.push("")
    }

    // 如果没有任何前缀选项，返回不匹配任何内容的正则
    if (regexParts.length === 0) {
      return "^(?!.*)" // 永远不匹配
    }

    // 构建最终正则
    const prefixPattern = regexParts.length > 1 ? `(${regexParts.join("|")})` : regexParts[0]
    // 不对命令部分进行转义，因为命令可能包含正则表达式语法
    return `^${prefixPattern}${command}$`
  }

  /**
   * 转义正则表达式特殊字符
   */
  escapeRegex(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")
  }

  /**
   * 获取用于提示信息的前缀示例
   * 优先返回自定义前缀，如果没有则返回默认前缀
   */
  getExamplePrefix() {
    const customPrefixes = this.getCustomPrefixes()
    if (customPrefixes && customPrefixes.length > 0) {
      return customPrefixes[0]
    }

    if (!this.isDefaultPrefixesDisabled()) {
      const defaultPrefixes = this.getDefaultPrefixes()
      if (defaultPrefixes && defaultPrefixes.length > 0) {
        return defaultPrefixes[0]
      }
    }

    // 如果允许空前缀，返回空字符串
    if (this.isEmptyPrefixAllowed()) {
      return ""
    }

    // 如果用户禁用了默认前缀但没有设置自定义前缀，且不允许空前缀
    // 这种情况下回退到默认前缀，确保插件能正常工作
    console.warn("⚠️ 配置错误：禁用了默认前缀但未设置自定义前缀，且不允许空前缀")
    console.warn(
      "💡 自动回退到默认前缀，请在 config/command.yaml 中设置 customPrefixes 或启用 allowEmptyPrefix",
    )

    // 回退到默认前缀
    const defaultPrefixes = this.getDefaultPrefixes()
    if (defaultPrefixes && defaultPrefixes.length > 0) {
      return defaultPrefixes[0]
    }

    // 如果连默认前缀都没有，返回硬编码的前缀
    return "mc"
  }

  /**
   * 生成带前缀的命令示例
   */
  getCommandExample(command) {
    const prefix = this.getExamplePrefix()
    const separator = this.getCommandSeparator()
    return `${prefix}${separator}${command}`
  }
}

// 创建单例实例
const config = new ConfigManager()

export default config
