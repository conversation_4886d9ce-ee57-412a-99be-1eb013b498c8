<!DOCTYPE html>
<html lang="zh-cn">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width">
  <link rel="shortcut icon" href="#"/>
  <link rel="preload" href="{{_res_path}}/common/font/HYWH-65W.woff" as="font" type="font/woff">
  <link rel="preload" href="{{_res_path}}/common/font/NZBZ.woff" as="font" type="font/woff">
  <link rel="preload" href="{{_res_path}}/common/font/tttgbnumber.woff" as="font" type="font/woff">
  <link rel="stylesheet" type="text/css" href="{{_res_path}}/common/common.css"/>
  <link rel="stylesheet" type="text/css" href="{{_res_path}}/character/training-stat-common.css"/>
  <link rel="stylesheet" type="text/css" href="{{_res_path}}/character/training-stat.css"/>
  {{if background}}{{@background}}{{/if}}
  <title>鸣潮练度统计</title>
</head>
{{set elemCls = {火:'pyro',冰:'cryo',风:'anemo',雷:'electro',量子:'quantum',虚数:'geo',物理:'sr', }[element||elem] || element || elem || 'hydro' }}
<body class="elem-{{elemCls}} {{displayMode || mode || `default`}}-mode {{bodyClass}}" {{sys.scale}}>
<div class="container elem-bg" id="container">

<!-- miao-plugin 风格的练度统计 -->
<div class="info_box">
  <div class="head-box type{{bgType}}">
    {{if mode === "char"}}
    <div class="title">#角色持有率</div>
    <div class="label">全角色综合持有率统计</div>
    {{else}}
    <div class="title">#角色{{conNum == -1 ? "练度" : "等级"}}统计</div>
    <div class="label">统计所有角色练度分布情况</div>
    {{/if}}
    <div class="genshin-logo"></div>
  </div>

  <div class="cont msg-cont">
    <div class="cont-title">角色练度统计说明</div>
    <div class="cont-body">
      <ul class="cont-msg">
        <li>数据来自<strong>Rover Plugin</strong>用户的角色练度信息统计</li>
        <li>百分比基于已收集的用户数据进行统计，能够一定程度上反映角色练度分布情况</li>
        <li>您可以通过<strong>#刷新面板</strong>命令来更新角色数据，帮助我们统计的更加全面</li>
        <li>统计包含<strong>角色等级、共鸣链、武器等级</strong>等练度相关信息</li>
        <li>数据会定期更新，新角色的练度分布会在获得后逐步完善</li>
        <li>{{if totalCount}}统计用户数：<strong>{{totalCount}}</strong>，{{/if}}数据最后更新时间：{{lastUpdate}}</li>
      </ul>
    </div>
  </div>

  <div class="data-box">
    <div class="char-list">
      <div class="avatar th">
        <div class="index">#</div>
        <div class="name">角色</div>
        <div class="lvl">平均等级</div>
        <div class="char-cons">
          <div class="cons-pct">
            <div class="cons-n0">1-20</div>
            <div class="cons-1">21-40</div>
            <div class="cons-2">41-60</div>
            <div class="cons-3">61-70</div>
            <div class="cons-4">71-80</div>
            <div class="cons-5">81-90</div>
            <div class="cons-6">满级</div>
            {{if mode === "char"}}
            <div class="life_bg">未获得</div>
            {{/if}}
          </div>
        </div>
      </div>
      {{each chars char idx}}
      <div class="avatar">
        <div class="index star{{char.star}}">{{idx+1}}</div>
        <div class="name_cont star{{char.star}}">
          <div class="name">
            <div class="avatar_img">
              <img src="{{_res_path}}{{char.face}}" onerror="whenError(this)"/>
            </div>
            <div class="avatar_name">
              {{char.abbr || char.name}}
            </div>
          </div>
        </div>
        <div class="pct">{{char.avgLevel}}</div>
        <div class="char-cons">
          <div class="cons-pct">
            {{each char.levelDist level idx}}
            <div>{{pct(level.value)}}</div>
            {{/each}}
            {{if mode ==="char"}}
            <div>{{pct(1-char.hold)}}</div>
            {{/if}}
          </div>
          <div class="cons-bg">
            {{each char.levelDist level idx}}
            <div class="cons-{{level.id}}" style='{{"width:"+pct(level.value)+"%"}}'></div>
            {{/each}}
          </div>
        </div>
      </div>
      {{/each}}
    </div>
  </div>
</div>

</div>
</body>
</html>
