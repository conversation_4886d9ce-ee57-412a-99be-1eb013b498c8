import { getIdByName, getNameById } from "../id2name.js"

/**
 * 伤害计算注册器
 * 基于WutheringWavesUID的register.py实现
 * 用于注册和管理角色伤害计算器
 */

/**
 * 伤害详情注册器
 */
class DamageDetailRegister {
  constructor() {
    this.damageClasses = new Map()
  }

  /**
   * 注册角色伤害计算类
   * @param {string} characterId - 角色ID
   * @param {Function} damageClass - 伤害计算类
   */
  registerClass(characterId, damageClass) {
    this.damageClasses.set(characterId, damageClass)
    console.log(`[Damage Register] 注册角色伤害计算器: ${characterId}`)
  }

  /**
   * 查找角色伤害计算类
   * @param {string} characterId - 角色ID
   * @returns {Function|null} 伤害计算类
   */
  findClass(characterId) {
    return this.damageClasses.get(characterId) || null
  }

  /**
   * 获取所有已注册的角色ID
   * @returns {Array} 角色ID列表
   */
  getAllCharacterIds() {
    return Array.from(this.damageClasses.keys())
  }

  /**
   * 检查角色是否已注册
   * @param {string} characterId - 角色ID
   * @returns {boolean} 是否已注册
   */
  hasCharacter(characterId) {
    return this.damageClasses.has(characterId)
  }
}

/**
 * 伤害排名注册器
 */
class DamageRankRegister {
  constructor() {
    this.rankClasses = new Map()
  }

  /**
   * 注册角色排名计算类
   * @param {string} characterId - 角色ID
   * @param {Function} rankClass - 排名计算类
   */
  registerClass(characterId, rankClass) {
    this.rankClasses.set(characterId, rankClass)
    console.log(`[Rank Register] 注册角色排名计算器: ${characterId}`)
  }

  /**
   * 查找角色排名计算类
   * @param {string} characterId - 角色ID
   * @returns {Function|null} 排名计算类
   */
  findClass(characterId) {
    return this.rankClasses.get(characterId) || null
  }

  /**
   * 获取所有已注册的角色ID
   * @returns {Array} 角色ID列表
   */
  getAllCharacterIds() {
    return Array.from(this.rankClasses.keys())
  }
}

// 创建全局注册器实例
export const damageDetailRegister = new DamageDetailRegister()
export const damageRankRegister = new DamageRankRegister()

/**
 * 注册所有角色的伤害计算器
 */
export async function registerAllDamage() {
  try {
    // 动态导入所有角色的伤害计算器
    const characterIds = [
      "1205", // 长离
      "1203", // 今汐
      "1204", // 凌阳
      "1102", // 安可
      "1301", // 白芷
      "1302", // 秋水
      "1402", // 散华
      "1403", // 桃祈
      "1404", // 椿
      "1405", // 渊武
      "1502", // 灯灯
      "1503", // 炽霞
      "1504", // 相里要
      "1505", // 秧秧
      "1506", // 吟霖
      // 4星角色
      "1101", // 釉瑚
      "1103", // 赞妮
      "1201", // 折枝
      "1202", // 守岸人
      "1303", // 维里奈
      "1304", // 夏空
      "1401", // 卡卡罗
      "1406", // 卡提希娅
      "1407", // 坎特蕾拉
      "1408", // 布兰特
      "1409", // 忌炎
      "1410", // 洛可可
      "1411", // 莫特斐
      "1412", // 菲比
      "1413", // 鉴心
      "1414", // 露帕
      "1415", // 丹瑾
      "1416", // 珂莱塔
      // 漂泊者
      "1501", // 漂泊者·湮灭
      "1507", // 漂泊者·衍射
      "1508"  // 漂泊者·气动
    ]

    for (const characterId of characterIds) {
      try {
        // 动态导入角色伤害计算器
        const module = await import(`./${characterId}/damage.js`)
        if (module.damageDetail) {
          damageDetailRegister.registerClass(characterId, module.damageDetail)
        }
        if (module.damageRank) {
          damageRankRegister.registerClass(characterId, module.damageRank)
        }
      } catch (error) {
        console.warn(`[Damage Register] 无法加载角色 ${characterId} 的伤害计算器:`, error.message)
      }
    }

    console.log(`[Damage Register] 伤害计算器注册完成，共注册 ${damageDetailRegister.getAllCharacterIds().length} 个角色`)
  } catch (error) {
    console.error("[Damage Register] 注册伤害计算器失败:", error)
  }
}

/**
 * 根据角色名称获取伤害计算器
 * @param {string} characterName - 角色名称
 * @returns {Function|null} 伤害计算器
 */
export function getDamageCalculatorByName(characterName) {
  const characterId = getIdByName(characterName)
  if (!characterId) {
    console.warn(`[Damage Register] 未找到角色 ${characterName} 的ID`)
    return null
  }
  
  return damageDetailRegister.findClass(characterId.toString())
}

/**
 * 根据角色ID获取伤害计算器
 * @param {string} characterId - 角色ID
 * @returns {Function|null} 伤害计算器
 */
export function getDamageCalculatorById(characterId) {
  return damageDetailRegister.findClass(characterId.toString())
}

/**
 * 检查角色是否支持伤害计算
 * @param {string} characterName - 角色名称
 * @returns {boolean} 是否支持
 */
export function isCharacterSupported(characterName) {
  const characterId = getIdByName(characterName)
  if (!characterId) return false
  
  return damageDetailRegister.hasCharacter(characterId.toString())
}

/**
 * 获取所有支持伤害计算的角色
 * @returns {Array} 角色信息列表 [{id, name}, ...]
 */
export function getSupportedCharacters() {
  const supportedIds = damageDetailRegister.getAllCharacterIds()
  return supportedIds.map(id => ({
    id,
    name: getNameById(parseInt(id)) || id
  })).filter(char => char.name !== char.id)
}

/**
 * 伤害计算器工厂
 */
export class DamageCalculatorFactory {
  /**
   * 创建角色伤害计算器实例
   * @param {string} characterName - 角色名称
   * @returns {Object|null} 伤害计算器实例
   */
  static create(characterName) {
    const DamageClass = getDamageCalculatorByName(characterName)
    if (!DamageClass) {
      console.warn(`[Damage Factory] 角色 ${characterName} 不支持伤害计算`)
      return null
    }
    
    try {
      return new DamageClass()
    } catch (error) {
      console.error(`[Damage Factory] 创建角色 ${characterName} 伤害计算器失败:`, error)
      return null
    }
  }

  /**
   * 根据角色ID创建伤害计算器实例
   * @param {string} characterId - 角色ID
   * @returns {Object|null} 伤害计算器实例
   */
  static createById(characterId) {
    const DamageClass = getDamageCalculatorById(characterId.toString())
    if (!DamageClass) {
      console.warn(`[Damage Factory] 角色ID ${characterId} 不支持伤害计算`)
      return null
    }
    
    try {
      return new DamageClass()
    } catch (error) {
      console.error(`[Damage Factory] 创建角色ID ${characterId} 伤害计算器失败:`, error)
      return null
    }
  }
}

export default {
  damageDetailRegister,
  damageRankRegister,
  registerAllDamage,
  getDamageCalculatorByName,
  getDamageCalculatorById,
  isCharacterSupported,
  getSupportedCharacters,
  DamageCalculatorFactory
}
