import { WavesDamageCalculator } from "../../wavesDamageCalculator.js"
import { DamageCalculatorFactory, isCharacterSupported } from "./register.js"
import { getIdByName, getNameById } from "../id2name.js"

/**
 * 角色面板伤害计算集成
 * 专门为角色面板提供伤害计算数据
 */
export class PanelDamageCalculator {
  constructor() {
    this.calculator = new WavesDamageCalculator()
  }

  /**
   * 为角色面板计算伤害数据
   * @param {Object} params - 计算参数
   * @param {Object} params.characterInfo - 角色基础信息
   * @param {Object} params.roleDetail - 角色详细数据
   * @param {Object} params.weaponInfo - 武器信息
   * @param {Array} params.echoList - 声骸列表
   * @param {Object} params.options - 计算选项
   * @returns {Object|null} 面板伤害数据
   */
  async calculatePanelDamage(params) {
    const {
      characterInfo,
      roleDetail,
      weaponInfo,
      echoList = [],
      options = {}
    } = params

    try {
      // 获取角色ID和名称
      const characterId = this.getCharacterId(characterInfo)
      const characterName = this.getCharacterName(characterInfo)
      
      if (!characterId || !characterName) {
        console.warn(`无法识别角色: ${characterInfo.name || characterInfo.roleId}`)
        return null
      }

      // 检查是否支持伤害计算
      if (!isCharacterSupported(characterName)) {
        console.log(`角色 ${characterName} 暂不支持伤害计算`)
        return {
          available: false,
          characterId,
          characterName,
          reason: "暂不支持伤害计算"
        }
      }

      console.log(`🔄 开始计算 ${characterName} 的面板伤害数据...`)

      // 创建角色伤害计算器
      const damageCalculator = DamageCalculatorFactory.create(characterName)
      if (!damageCalculator) {
        return {
          available: false,
          characterId,
          characterName,
          reason: "无法创建伤害计算器"
        }
      }

      // 提取计算参数
      const calculationParams = this.extractCalculationParams({
        characterInfo,
        roleDetail,
        weaponInfo,
        echoList,
        options
      })

      // 创建伤害属性对象
      const attr = this.calculator.createDamageAttribute(calculationParams)

      // 计算各种技能伤害
      const damageResults = await this.calculateAllSkillDamage(
        damageCalculator,
        attr,
        calculationParams
      )

      // 格式化面板数据
      const panelData = this.formatPanelData({
        characterId,
        characterName,
        calculationParams,
        damageResults
      })

      console.log(`✅ ${characterName} 面板伤害数据计算完成`)
      return panelData

    } catch (error) {
      console.error("计算面板伤害数据失败:", error)
      return {
        available: false,
        reason: `计算错误: ${error.message}`
      }
    }
  }

  /**
   * 获取角色ID
   * @param {Object} characterInfo - 角色信息
   * @returns {string|null} 角色ID
   */
  getCharacterId(characterInfo) {
    return characterInfo.roleId?.toString() || 
           getIdByName(characterInfo.name)?.toString() || 
           null
  }

  /**
   * 获取角色名称
   * @param {Object} characterInfo - 角色信息
   * @returns {string|null} 角色名称
   */
  getCharacterName(characterInfo) {
    return characterInfo.name || 
           (characterInfo.roleId && getNameById(characterInfo.roleId)) || 
           null
  }

  /**
   * 提取计算参数
   * @param {Object} params - 原始参数
   * @returns {Object} 计算参数
   */
  extractCalculationParams(params) {
    const { characterInfo, roleDetail, weaponInfo, echoList, options } = params

    // 提取技能等级
    const skillLevels = this.extractSkillLevels(roleDetail)
    
    // 提取共鸣链等级
    const chainLevel = roleDetail.chainLevel || 0
    
    // 计算选项
    const {
      enemyLevel = 90,
      enemyResistance = 0.1,
      isGroup = false
    } = options

    return {
      characterData: characterInfo,
      roleData: {
        ...roleDetail,
        enemyLevel,
        enemyResistance
      },
      weaponData: weaponInfo,
      echoData: echoList,
      chainLevel,
      skillLevels,
      isGroup
    }
  }

  /**
   * 提取技能等级
   * @param {Object} roleDetail - 角色详细数据
   * @returns {Object} 技能等级对象
   */
  extractSkillLevels(roleDetail) {
    const skillLevels = {}
    
    // 从角色数据中提取技能等级
    if (roleDetail.skillList) {
      for (const skill of roleDetail.skillList) {
        const skillType = this.mapSkillIdToType(skill.skillId)
        if (skillType) {
          skillLevels[skillType] = skill.level || 1
        }
      }
    }

    // 设置默认值
    const defaultSkillLevels = { a: 1, e: 1, r: 1, q: 1, a2: 1 }
    return { ...defaultSkillLevels, ...skillLevels }
  }

  /**
   * 映射技能ID到技能类型
   * @param {number} skillId - 技能ID
   * @returns {string|null} 技能类型
   */
  mapSkillIdToType(skillId) {
    // 根据技能ID的规律映射到技能类型
    const lastDigit = skillId % 10
    switch (lastDigit) {
      case 1: return "a"   // 普攻
      case 2: return "e"   // 共鸣技能
      case 3: return "r"   // 共鸣解放
      case 6: return "q"   // 变奏技能
      case 7: return "a2"  // 共鸣回路
      default: return null
    }
  }

  /**
   * 计算所有技能伤害
   * @param {Object} damageCalculator - 角色伤害计算器
   * @param {Object} attr - 伤害属性对象
   * @param {Object} params - 计算参数
   * @returns {Object} 伤害计算结果
   */
  async calculateAllSkillDamage(damageCalculator, attr, params) {
    const { skillLevels, isGroup } = params
    const results = {}

    // 定义技能类型和对应的伤害函数
    const skillConfigs = [
      { type: "a", damageFunc: ["attack"], name: "普攻" },
      { type: "e", damageFunc: ["skill"], name: "共鸣技能" },
      { type: "r", damageFunc: ["liberation"], name: "共鸣解放" },
      { type: "q", damageFunc: ["outro"], name: "变奏技能" },
      { type: "a2", damageFunc: ["hit", "a2"], name: "共鸣回路" }
    ]

    for (const config of skillConfigs) {
      try {
        // 复制属性对象避免相互影响
        const skillAttr = { ...attr }
        skillAttr.skillLevels = { [config.type]: skillLevels[config.type] || 1 }

        // 计算技能伤害
        const result = await damageCalculator.damageDetail(
          skillAttr,
          config.damageFunc,
          isGroup
        )

        results[config.type] = {
          ...result,
          skillType: config.type,
          skillName: config.name,
          skillLevel: skillLevels[config.type] || 1,
          available: true
        }

      } catch (error) {
        console.warn(`计算技能 ${config.type} 失败:`, error.message)
        results[config.type] = {
          skillType: config.type,
          skillName: config.name,
          available: false,
          error: error.message
        }
      }
    }

    return results
  }

  /**
   * 格式化面板数据
   * @param {Object} params - 格式化参数
   * @returns {Object} 格式化后的面板数据
   */
  formatPanelData(params) {
    const { characterId, characterName, calculationParams, damageResults } = params

    const panelData = {
      available: true,
      characterId,
      characterName,
      chainLevel: calculationParams.chainLevel,
      skillLevels: calculationParams.skillLevels,
      calculationTime: new Date().toISOString(),
      skills: {},
      summary: {}
    }

    // 处理技能伤害数据
    let maxDamage = 0
    let maxSkill = null
    let availableSkills = 0

    for (const [skillType, result] of Object.entries(damageResults)) {
      if (!result.available) {
        panelData.skills[skillType] = {
          available: false,
          error: result.error,
          skillName: result.skillName
        }
        continue
      }

      availableSkills++

      // 计算技能的最高伤害（这里需要根据具体的伤害计算结果调整）
      const skillDamage = this.extractMaxDamageFromResult(result)
      
      panelData.skills[skillType] = {
        available: true,
        skillName: result.skillName,
        skillLevel: result.skillLevel,
        maxDamage: skillDamage,
        effects: result.effects || [],
        display: {
          maxDamage: this.formatDamageNumber(skillDamage)
        }
      }

      if (skillDamage > maxDamage) {
        maxDamage = skillDamage
        maxSkill = {
          type: skillType,
          name: result.skillName,
          damage: skillDamage
        }
      }
    }

    // 生成伤害摘要
    panelData.summary = {
      maxDamage,
      maxSkill,
      availableSkills,
      display: {
        maxDamage: this.formatDamageNumber(maxDamage)
      }
    }

    return panelData
  }

  /**
   * 从伤害计算结果中提取最高伤害
   * @param {Object} result - 伤害计算结果
   * @returns {number} 最高伤害值
   */
  extractMaxDamageFromResult(result) {
    // 这里需要根据具体的伤害计算结果结构来提取最高伤害
    // 暂时返回一个示例值
    if (result.totalDamage && result.totalDamage.maxSingleHit) {
      return result.totalDamage.maxSingleHit
    }
    
    // 如果有技能倍率结果，可以基于倍率估算伤害
    if (result.skillResults) {
      // 这里可以根据技能倍率和角色属性计算实际伤害
      return 100000 // 示例值
    }
    
    return 0
  }

  /**
   * 格式化伤害数字
   * @param {number} damage - 伤害数值
   * @returns {string} 格式化后的伤害字符串
   */
  formatDamageNumber(damage) {
    if (damage >= 1000000) {
      return `${(damage / 1000000).toFixed(1)}M`
    } else if (damage >= 1000) {
      return `${(damage / 1000).toFixed(1)}K`
    }
    return damage.toLocaleString()
  }
}

// 创建单例实例
export const panelDamageCalculator = new PanelDamageCalculator()

/**
 * 便捷函数：为角色面板计算伤害数据
 * @param {Object} params - 计算参数
 * @returns {Object|null} 面板伤害数据
 */
export async function calculatePanelDamageData(params) {
  return await panelDamageCalculator.calculatePanelDamage(params)
}

export default panelDamageCalculator
