import plugin from "../../../lib/plugins/plugin.js"
import config from "../components/config.js"
import axios from "axios"

export class RedeemCode extends plugin {
  constructor() {
    super({
      name: "鸣潮-兑换码",
      event: "message",
      priority: 1007,
      rule: [
        {
          reg: config.generateCommandRegex("兑换码"),
          fnc: "getRedeemCodes",
        },
        {
          reg: config.generateCommandRegex("code"),
          fnc: "getRedeemCodes",
        },
      ],
    })
  }

  async getRedeemCodes(e) {
    try {
      logger.info("[鸣潮兑换码] 开始获取兑换码...")

      const codeList = await this.fetchCodeList()
      if (!codeList || codeList.length === 0) {
        await e.reply(" 获取兑换码失败，请稍后再试")
        return true
      }

      const validCodes = this.filterValidCodes(codeList)
      if (validCodes.length === 0) {
        await e.reply(" 暂无可用兑换码")
        return true
      }

      const message = this.formatCodeMessage(validCodes)
      await e.reply(message)

      logger.info(`[鸣潮兑换码] 成功获取 ${validCodes.length} 个兑换码`)
      return true
    } catch (error) {
      logger.error("[鸣潮兑换码] 获取兑换码失败:", error)
      await e.reply(" 获取兑换码失败，请稍后再试")
      return true
    }
  }

  async fetchCodeList() {
    try {
      const now = new Date()
      const timeString = `${now.getFullYear() - 1900}${now.getMonth()}${now.getDate()}${now.getHours()}${now.getMinutes()}`
      const nowTime = Date.now()

      const url = `https://newsimg.5054399.com/comm/mlcxqcommon/static/wap/js/data_102.js?${timeString}&callback=?&_=${nowTime}`

      const response = await axios.get(url, {
        timeout: 10000,
        headers: {
          "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        },
      })

      const jsonData = response.data.split("=", 2)[1].trim().replace(/;$/, "")
      return JSON.parse(jsonData)
    } catch (error) {
      logger.error("[鸣潮兑换码] 请求失败:", error)
      throw error
    }
  }

  filterValidCodes(codeList) {
    const invalidCodeList = ["MINGCHAO"]
    const validCodes = []

    for (const code of codeList) {
      if (code.is_fail === "1") continue

      const order = code.order || ""
      if (!order || invalidCodeList.includes(order)) continue

      if (this.isCodeExpired(code.label || "")) continue

      validCodes.push(code)
    }

    return validCodes
  }

  isCodeExpired(label) {
    if (!label) return false

    const pattern = /(\d{1,2})月(\d{1,2})日(\d{1,2})点/
    const match = label.match(pattern)

    if (!match) return false

    const expireMonth = parseInt(match[1])
    const expireDay = parseInt(match[2])
    let expireHour = parseInt(match[3])

    const now = new Date()
    const currentMonth = now.getMonth() + 1
    let expireYear = now.getFullYear()

    if (currentMonth < expireMonth) {
      expireYear -= 1
    } else if (currentMonth === expireMonth) {
      if (now.getDate() > expireDay) {
        expireYear += 1
      }
    }

    if (expireHour === 24) expireHour = 23

    const expireDate = new Date(expireYear, expireMonth - 1, expireDay, expireHour, 0, 0)
    return now > expireDate
  }

  formatCodeMessage(validCodes) {
    let message = " 鸣潮兑换码\n\n"

    validCodes.forEach((code, index) => {
      const order = code.order || ""
      const reward = code.reward || "未知奖励"
      const label = code.label || ""

      message += ` 兑换码 ${index + 1}\n`
      message += ` 代码: ${order}\n`
      message += ` 奖励: ${reward}\n`

      if (label) {
        message += ` 有效期: ${label}\n`
      }

      message += "\n"
    })

    return message.trim()
  }
}
