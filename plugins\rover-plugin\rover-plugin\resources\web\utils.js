/**
 * Rover Plugin Web Login Utils
 * 基于 WutheringWavesUID 设计的工具函数
 */

// 全局工具对象
window.RoverUtils = {
  
  /**
   * 显示消息
   * @param {string} text 消息内容
   * @param {string} type 消息类型 (success, error, warning, info)
   * @param {string} containerId 容器ID，默认为 'message'
   */
  showMessage(text, type = 'info', containerId = 'message') {
    const messageEl = document.getElementById(containerId);
    if (!messageEl) {
      console.warn(`Message container #${containerId} not found`);
      return;
    }
    
    messageEl.textContent = text;
    messageEl.className = `message message-${type}`;
    messageEl.style.display = 'block';
    
    // 添加淡入动画
    messageEl.classList.add('fade-in');
    
    // 自动隐藏错误和警告消息
    if (type === 'error' || type === 'warning') {
      setTimeout(() => {
        this.hideMessage(containerId);
      }, 5000);
    }
  },

  /**
   * 隐藏消息
   * @param {string} containerId 容器ID
   */
  hideMessage(containerId = 'message') {
    const messageEl = document.getElementById(containerId);
    if (messageEl) {
      messageEl.style.display = 'none';
      messageEl.classList.remove('fade-in');
    }
  },

  /**
   * 验证手机号
   * @param {string} phone 手机号
   * @returns {boolean} 是否有效
   */
  isValidPhone(phone) {
    const pattern = /^1[3-9]\d{9}$/;
    return pattern.test(phone);
  },

  /**
   * 验证验证码
   * @param {string} code 验证码
   * @returns {boolean} 是否有效
   */
  isValidCode(code) {
    const pattern = /^\d{6}$/;
    return pattern.test(code);
  },

  /**
   * 启动倒计时
   * @param {HTMLElement} button 按钮元素
   * @param {number} seconds 倒计时秒数
   * @param {string} originalText 原始文本
   */
  startCountdown(button, seconds = 60, originalText = '获取验证码') {
    let countdown = seconds;
    button.disabled = true;
    
    const timer = setInterval(() => {
      button.textContent = `${countdown}s后重发`;
      countdown--;
      
      if (countdown < 0) {
        clearInterval(timer);
        button.textContent = originalText;
        button.disabled = false;
      }
    }, 1000);
    
    return timer;
  },

  /**
   * 发送API请求
   * @param {string} url 请求URL
   * @param {Object} data 请求数据
   * @param {string} method 请求方法
   * @returns {Promise<Object>} 响应数据
   */
  async apiRequest(url, data = null, method = 'GET') {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
    };
    
    if (data && (method === 'POST' || method === 'PUT')) {
      options.body = JSON.stringify(data);
    }
    
    try {
      const response = await fetch(url, options);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        return await response.json();
      } else {
        return await response.text();
      }
    } catch (error) {
      console.error('API请求失败:', error);
      throw error;
    }
  },

  /**
   * 设置按钮加载状态
   * @param {HTMLElement} button 按钮元素
   * @param {boolean} loading 是否加载中
   * @param {string} loadingText 加载中的文本
   */
  setButtonLoading(button, loading, loadingText = '处理中...') {
    if (loading) {
      button.dataset.originalText = button.textContent;
      button.textContent = loadingText;
      button.disabled = true;
    } else {
      button.textContent = button.dataset.originalText || button.textContent;
      button.disabled = false;
    }
  },

  /**
   * 格式化时间
   * @param {Date|string|number} date 日期
   * @returns {string} 格式化后的时间
   */
  formatTime(date) {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const seconds = String(d.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  },

  /**
   * 复制文本到剪贴板
   * @param {string} text 要复制的文本
   * @returns {Promise<boolean>} 是否成功
   */
  async copyToClipboard(text) {
    try {
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(text);
        return true;
      } else {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        const result = document.execCommand('copy');
        document.body.removeChild(textArea);
        return result;
      }
    } catch (error) {
      console.error('复制失败:', error);
      return false;
    }
  },

  /**
   * 防抖函数
   * @param {Function} func 要防抖的函数
   * @param {number} wait 等待时间（毫秒）
   * @returns {Function} 防抖后的函数
   */
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },

  /**
   * 节流函数
   * @param {Function} func 要节流的函数
   * @param {number} limit 限制时间（毫秒）
   * @returns {Function} 节流后的函数
   */
  throttle(func, limit) {
    let inThrottle;
    return function executedFunction(...args) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  },

  /**
   * 生成随机ID
   * @param {number} length ID长度
   * @returns {string} 随机ID
   */
  generateId(length = 8) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },

  /**
   * 检查是否为移动设备
   * @returns {boolean} 是否为移动设备
   */
  isMobile() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  },

  /**
   * 获取URL参数
   * @param {string} name 参数名
   * @returns {string|null} 参数值
   */
  getUrlParam(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
  },

  /**
   * 设置页面标题
   * @param {string} title 标题
   */
  setTitle(title) {
    document.title = title;
  },

  /**
   * 添加CSS类
   * @param {HTMLElement} element 元素
   * @param {string} className 类名
   */
  addClass(element, className) {
    if (element && className) {
      element.classList.add(className);
    }
  },

  /**
   * 移除CSS类
   * @param {HTMLElement} element 元素
   * @param {string} className 类名
   */
  removeClass(element, className) {
    if (element && className) {
      element.classList.remove(className);
    }
  },

  /**
   * 切换CSS类
   * @param {HTMLElement} element 元素
   * @param {string} className 类名
   */
  toggleClass(element, className) {
    if (element && className) {
      element.classList.toggle(className);
    }
  }
};

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
  console.log('🌊 Rover Plugin Web Login Utils 已加载');
  
  // 添加全局错误处理
  window.addEventListener('error', function(e) {
    console.error('页面错误:', e.error);
    RoverUtils.showMessage('页面发生错误，请刷新重试', 'error');
  });
  
  // 添加网络错误处理
  window.addEventListener('unhandledrejection', function(e) {
    console.error('未处理的Promise错误:', e.reason);
    if (e.reason && e.reason.message && e.reason.message.includes('fetch')) {
      RoverUtils.showMessage('网络请求失败，请检查网络连接', 'error');
    }
  });
});
