/**
 * 手动绑定用户数据脚本
 * 用于绕过风险控制，直接绑定用户数据到数据库
 */

import { Database } from "./lib/api.js"

// 测试配置 - 请替换为您的真实数据
const BIND_CONFIG = {
  userId: "test_user_12345",
  uid: "请在这里输入您的真实UID", // ⚠️ 必须替换为您的真实UID
  token:
    "eyJhbGciOiJIUzI1NiJ9.eyJjcmVhdGVkIjoxNzUzNjMxMjg2Njc1LCJ1c2VySWQiOjEwODE2NzMyfQ.Z9BLJ1jtanrNfl1JLvcifr8ypSXEu7513sQvy9I-A1c",
  did: "6F02FE7B671ACA64694F19FB67EBEBAD07659846",
}

async function manualBind() {
  console.log("🔗 === 手动绑定用户数据 ===")
  console.log(`用户ID: ${BIND_CONFIG.userId}`)
  console.log(`UID: ${BIND_CONFIG.uid}`)
  console.log(`Token: ${BIND_CONFIG.token.substring(0, 20)}...`)
  console.log(`DID: ${BIND_CONFIG.did}`)

  try {
    const { WavesUser, WavesBind } = Database

    // 删除旧数据（如果存在）
    console.log("\n🗑️ 清理旧数据...")
    await WavesUser.destroy({
      where: { user_id: BIND_CONFIG.userId },
    })
    await WavesBind.destroy({
      where: { user_id: BIND_CONFIG.userId },
    })

    // 创建用户记录
    console.log("\n👤 创建用户记录...")
    const userRecord = await WavesUser.create({
      user_id: BIND_CONFIG.userId,
      uid: BIND_CONFIG.uid,
      cookie: BIND_CONFIG.token,
      did: BIND_CONFIG.did,
      platform: "ios",
      status: "正常",
    })
    console.log("✅ 用户记录创建成功:", userRecord.toJSON())

    // 创建绑定记录
    console.log("\n🔗 创建绑定记录...")
    const bindRecord = await WavesBind.create({
      user_id: BIND_CONFIG.userId,
      uid: BIND_CONFIG.uid,
      is_default: 1,
    })
    console.log("✅ 绑定记录创建成功:", bindRecord.toJSON())

    console.log("\n🎉 手动绑定完成！")
    console.log("现在可以运行API测试了")
  } catch (error) {
    console.error("❌ 手动绑定失败:", error)
  }
}

// 运行绑定
manualBind()
