/**
 * 伤害计算核心模块
 * 基于WutheringWavesUID的damage.py实现
 * 包含武器、声骸、套装效果计算
 */

/**
 * 武器伤害计算
 * @param {Object} attr - 伤害属性对象
 * @param {Object} weaponData - 武器数据
 * @param {Array|string} damageFunc - 伤害函数类型
 * @param {boolean} isGroup - 是否组队
 */
export function weaponDamage(attr, weaponData, damageFunc, isGroup) {
  // 武器谐振效果计算
  // 这里需要根据具体武器ID实现不同的武器效果
  
  if (!weaponData || !weaponData.weapon) return
  
  const weaponId = weaponData.weapon.weaponId
  const weaponLevel = weaponData.weapon.level || 1
  const weaponRank = weaponData.weapon.rank || 1
  
  // 根据武器ID应用不同的武器效果
  // 这里可以参考WutheringWavesUID的武器注册系统
  console.log(`应用武器效果: ${weaponId}, 等级: ${weaponLevel}, 精炼: ${weaponRank}`)
}

/**
 * 声骸伤害计算
 * @param {Object} attr - 伤害属性对象
 * @param {Array} echoData - 声骸数据
 * @param {Array|string} damageFunc - 伤害函数类型
 * @param {boolean} isGroup - 是否组队
 */
export function echoDamage(attr, echoData, damageFunc, isGroup) {
  // 声骸效果计算
  if (!echoData || !Array.isArray(echoData)) return
  
  for (const echo of echoData) {
    if (!echo || !echo.echo) continue
    
    const echoId = echo.echo.echoId
    const echoLevel = echo.echo.level || 1
    const echoRank = echo.echo.rank || 1
    
    // 根据声骸ID应用不同的声骸效果
    console.log(`应用声骸效果: ${echoId}, 等级: ${echoLevel}, 品质: ${echoRank}`)
  }
}

/**
 * 套装效果计算
 * @param {Object} attr - 伤害属性对象
 * @param {Array} echoData - 声骸数据
 * @param {Array|string} damageFunc - 伤害函数类型
 * @param {boolean} isGroup - 是否组队
 */
export function phaseDamage(attr, echoData, damageFunc, isGroup) {
  // 套装效果计算
  if (!echoData || !Array.isArray(echoData)) return
  
  // 统计套装数量
  const sonataCount = {}
  
  for (const echo of echoData) {
    if (!echo || !echo.echo || !echo.echo.sonataId) continue
    
    const sonataId = echo.echo.sonataId
    sonataCount[sonataId] = (sonataCount[sonataId] || 0) + 1
  }
  
  // 应用套装效果
  for (const [sonataId, count] of Object.entries(sonataCount)) {
    applySonataEffect(attr, sonataId, count, damageFunc, isGroup)
  }
}

/**
 * 应用套装效果
 * @param {Object} attr - 伤害属性对象
 * @param {string} sonataId - 套装ID
 * @param {number} count - 套装件数
 * @param {Array|string} damageFunc - 伤害函数类型
 * @param {boolean} isGroup - 是否组队
 */
function applySonataEffect(attr, sonataId, count, damageFunc, isGroup) {
  // 根据套装ID和件数应用不同的套装效果
  
  // 示例：奔狼燎原之焰 (热熔套装)
  if (sonataId === "43000001" && count >= 5) {
    // 5件套效果：施放共鸣解放时，队伍中角色热熔伤害提升15%，自身共鸣解放伤害提升20%
    if (attr.char_attr === "pyro") { // 热熔属性
      const title = "奔狼燎原之焰-5件套"
      const msg = "队伍中角色热熔伤害提升15%"
      attr.addDmgBonus(0.15, title, msg)
    }
    
    if (damageFunc.includes("liberation")) {
      const title = "奔狼燎原之焰-5件套"
      const msg = "自身共鸣解放伤害提升20%"
      attr.addDmgBonus(0.2, title, msg)
    }
  }
  
  // 示例：凝夜白霜 (冷凝套装)
  if (sonataId === "43000002" && count >= 5) {
    // 5件套效果：施放共鸣技能时，冷凝伤害提升12%
    if (attr.char_attr === "glacio" && damageFunc.includes("skill")) {
      const title = "凝夜白霜-5件套"
      const msg = "施放共鸣技能时，冷凝伤害提升12%"
      attr.addDmgBonus(0.12, title, msg)
    }
  }
  
  // 示例：彻空冥雷 (导电套装)
  if (sonataId === "43000003" && count >= 5) {
    // 5件套效果：施放重击或共鸣解放时，导电伤害提升15%
    if (attr.char_attr === "electro" && (damageFunc.includes("hit") || damageFunc.includes("liberation"))) {
      const title = "彻空冥雷-5件套"
      const msg = "施放重击或共鸣解放时，导电伤害提升15%"
      attr.addDmgBonus(0.15, title, msg)
    }
  }
  
  // 示例：浮星祛暗 (衍射套装)
  if (sonataId === "43000004" && count >= 5) {
    // 5件套效果：施放延奏技能时，衍射伤害提升15%
    if (attr.char_attr === "spectro" && damageFunc.includes("outro")) {
      const title = "浮星祛暗-5件套"
      const msg = "施放延奏技能时，衍射伤害提升15%"
      attr.addDmgBonus(0.15, title, msg)
    }
  }
  
  // 示例：啸谷长风 (气动套装)
  if (sonataId === "43000005" && count >= 5) {
    // 5件套效果：施放普攻时，气动伤害提升7.5%
    if (attr.char_attr === "aero" && damageFunc.includes("attack")) {
      const title = "啸谷长风-5件套"
      const msg = "施放普攻时，气动伤害提升7.5%"
      attr.addDmgBonus(0.075, title, msg)
    }
  }
  
  // 示例：沉日劫明 (湮灭套装)
  if (sonataId === "43000006" && count >= 5) {
    // 5件套效果：施放共鸣技能时，湮灭伤害提升7.5%
    if (attr.char_attr === "havoc" && damageFunc.includes("skill")) {
      const title = "沉日劫明-5件套"
      const msg = "施放共鸣技能时，湮灭伤害提升7.5%"
      attr.addDmgBonus(0.075, title, msg)
    }
  }
  
  // 示例：熔山裂谷 (聚变套装)
  if (sonataId === "43000007" && count >= 5) {
    // 5件套效果：施放重击时，聚变伤害提升7.5%
    if (attr.char_attr === "fusion" && damageFunc.includes("hit")) {
      const title = "熔山裂谷-5件套"
      const msg = "施放重击时，聚变伤害提升7.5%"
      attr.addDmgBonus(0.075, title, msg)
    }
  }
  
  console.log(`应用套装效果: ${sonataId}, 件数: ${count}`)
}

/**
 * 检查角色ID是否匹配
 * @param {string} characterId - 角色ID
 * @param {Array} targetIds - 目标ID列表
 * @returns {boolean} 是否匹配
 */
export function checkCharId(characterId, targetIds) {
  if (!Array.isArray(targetIds)) {
    targetIds = [targetIds]
  }
  return targetIds.includes(characterId)
}

/**
 * 获取伤害类型常量
 */
export const DAMAGE_TYPE = {
  ATTACK: "attack_damage",      // 普攻伤害
  HIT: "hit_damage",           // 重击伤害
  SKILL: "skill_damage",       // 共鸣技能伤害
  LIBERATION: "liberation_damage", // 共鸣解放伤害
  OUTRO: "outro_damage"        // 延奏技能伤害
}

/**
 * 获取角色属性常量
 */
export const CHAR_ATTR = {
  PYRO: "pyro",        // 热熔
  GLACIO: "glacio",    // 冷凝
  ELECTRO: "electro",  // 导电
  SPECTRO: "spectro",  // 衍射
  AERO: "aero",        // 气动
  HAVOC: "havoc",      // 湮灭
  FUSION: "fusion"     // 聚变
}

/**
 * 获取套装ID常量
 */
export const SONATA_ID = {
  MOLTEN: "43000001",      // 奔狼燎原之焰 (热熔)
  FREEZING: "43000002",    // 凝夜白霜 (冷凝)
  ELECTRO: "43000003",     // 彻空冥雷 (导电)
  SPECTRO: "43000004",     // 浮星祛暗 (衍射)
  AERO: "43000005",        // 啸谷长风 (气动)
  HAVOC: "43000006",       // 沉日劫明 (湮灭)
  FUSION: "43000007"       // 熔山裂谷 (聚变)
}

export default {
  weaponDamage,
  echoDamage,
  phaseDamage,
  checkCharId,
  DAMAGE_TYPE,
  CHAR_ATTR,
  SONATA_ID
}
