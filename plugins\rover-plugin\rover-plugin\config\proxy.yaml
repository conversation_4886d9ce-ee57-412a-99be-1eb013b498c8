# 代理配置
proxy:
  # 本地代理地址 (支持http和socks5)
  # 例如: "http://127.0.0.1:7890" 或 "socks5://127.0.0.1:1080"
  localProxy: ""
  
  # 库街区API代理地址
  kuroUrlProxy: ""
  
  # 需要代理的函数: ["all"] 或 []
  # "all" - 除获取验证码外的所有请求使用代理（包括登录、获取token、bat等）
  # [] - 不使用代理
  needProxyFunc: ["all"]

  # 代理轮换配置（可选）
  # 启用后会轮询使用所有代理，失效时自动切换到下一个
  proxyRotation:
    # 是否启用本地代理轮换
    enabled: false

    # 备用代理列表（不包括主代理localProxy）
    # 轮询顺序：localProxy -> proxies[0] -> proxies[1] -> ...
    proxies: []

    # 示例配置：
    # enabled: true
    # proxies:
    #   - "http://backup-proxy1:port"
    #   - "http://backup-proxy2:port"
    #   - "socks5://backup-proxy3:port"

  # 库街区API代理轮换配置（可选）
  kuroProxyRotation:
    # 是否启用库街区API代理轮换
    enabled: false

    # 备用库街区API代理列表（不包括主代理kuroUrlProxy）
    # 轮询顺序：kuroUrlProxy -> proxies[0] -> proxies[1] -> ...
    proxies: []

    # 示例配置：
    # enabled: true
    # mode: "round_robin"
    # proxies:
    #   - "https://api-backup1.kurobbs.com"
    #   - "https://api-backup2.kurobbs.com"
