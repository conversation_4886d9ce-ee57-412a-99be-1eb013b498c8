/**
 * Token验证测试脚本 - 验证token和DID的有效性
 */

import <PERSON><PERSON><PERSON><PERSON> from "./components/api/kuroapi.js"

// 测试配置
const TEST_CONFIG = {
  token: "eyJhbGciOiJIUzI1NiJ9.eyJjcmVhdGVkIjoxNzUzNjMxMjg2Njc1LCJ1c2VySWQiOjEwODE2NzMyfQ.Z9BLJ1jtanrNfl1JLvcifr8ypSXEu7513sQvy9I-A1c",
  did: "6F02FE7B671ACA64694F19FB67EBEBAD07659846",
}

async function validateTokenAndDid() {
  console.log("🧪 开始Token和DID验证测试...")
  console.log(`Token: ${TEST_CONFIG.token.substring(0, 30)}...`)
  console.log(`DID: ${TEST_CONFIG.did}`)

  try {
    const api = new <PERSON><PERSON><PERSON><PERSON>()

    // 测试1: 登录日志验证
    console.log("\n📋 测试1: 登录日志验证...")
    try {
      const loginLogResult = await api.getLoginLog("100000001", TEST_CONFIG.token, TEST_CONFIG.did)
      console.log(`登录日志: code=${loginLogResult.code}, msg=${loginLogResult.msg}`)
      
      if (loginLogResult.code === 200) {
        console.log("✅ Token和DID验证成功 - 登录日志正常")
      } else if (loginLogResult.code === 270) {
        console.log("⚠️ 风险控制检测 - Token和DID可能有效，但环境被限制")
      } else {
        console.log("❌ Token或DID可能无效")
      }
    } catch (error) {
      console.error("登录日志测试失败:", error.message)
    }

    // 测试2: 角色列表获取
    console.log("\n📋 测试2: 角色列表获取...")
    try {
      const roleListResult = await api.getRoleList(TEST_CONFIG.token, TEST_CONFIG.did)
      console.log(`角色列表: code=${roleListResult.code}, msg=${roleListResult.msg}`)
      
      if (roleListResult.code === 200 && roleListResult.data) {
        console.log("✅ 角色列表获取成功")
        console.log(`角色数量: ${roleListResult.data.length}`)
        if (roleListResult.data.length > 0) {
          const firstRole = roleListResult.data[0]
          console.log(`第一个角色: ${firstRole.roleName} (UID: ${firstRole.roleId})`)
          
          // 测试3: 使用真实UID测试bat token获取
          console.log("\n🔑 测试3: 使用真实UID测试bat token获取...")
          const batToken = await api.getRequestToken(
            firstRole.roleId, 
            TEST_CONFIG.token, 
            TEST_CONFIG.did, 
            null, 
            true
          )
          
          if (batToken) {
            console.log(`✅ bat token获取成功: ${batToken.substring(0, 20)}...`)
            
            // 测试4: 使用bat token测试API调用
            console.log("\n🎮 测试4: 使用bat token测试API调用...")
            const baseData = await api.getBaseData(firstRole.roleId, TEST_CONFIG.token, batToken)
            console.log(`基础数据: code=${baseData.code}, msg=${baseData.msg}`)
            
          } else {
            console.log("❌ bat token获取失败")
          }
        }
      } else if (roleListResult.code === 270) {
        console.log("⚠️ 风险控制检测 - 无法获取角色列表")
      } else {
        console.log("❌ 角色列表获取失败")
      }
    } catch (error) {
      console.error("角色列表测试失败:", error.message)
    }

    // 测试5: 直接测试bat token获取（使用测试UID）
    console.log("\n🔑 测试5: 直接测试bat token获取（测试UID）...")
    try {
      const testUid = "100000001"
      const batToken = await api.getRequestToken(testUid, TEST_CONFIG.token, TEST_CONFIG.did, null, true)
      
      if (batToken) {
        console.log(`✅ bat token获取成功: ${batToken.substring(0, 20)}...`)
        console.log("🎉 您的token和DID是有效的！")
        
        // 测试使用bat token的API
        console.log("\n🎮 测试6: 使用bat token测试API调用...")
        const baseData = await api.getBaseData(testUid, TEST_CONFIG.token, batToken)
        console.log(`基础数据: code=${baseData.code}, msg=${baseData.msg}`)
        
        const roleData = await api.getRoleData(testUid, batToken)
        console.log(`角色数据: code=${roleData.code}, msg=${roleData.msg}`)
        
      } else {
        console.log("❌ bat token获取失败")
      }
    } catch (error) {
      console.error("bat token测试失败:", error.message)
    }

    // 总结
    console.log("\n📊 测试总结:")
    console.log("1. 如果所有测试都返回code=270，说明当前网络环境被风险控制")
    console.log("2. 如果登录日志成功但其他失败，说明token有效但可能需要更换网络")
    console.log("3. 如果bat token获取成功，说明您的凭据完全有效")
    console.log("4. 建议：尝试更换代理IP或等待一段时间后重试")

  } catch (error) {
    console.error("❌ 验证测试失败:", error)
  }
}

// 运行验证
validateTokenAndDid()
