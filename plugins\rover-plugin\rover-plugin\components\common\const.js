export const ATTRIBUTE_ID_MAP = { 1: "冷凝", 2: "热熔", 3: "导电", 4: "气动", 5: "衍射", 6: "湮灭" }
export const WEAPON_TYPE_ID_MAP = { 1: "长刃", 2: "迅刀", 3: "佩枪", 4: "臂铠", 5: "音感仪" }

export const ATTRIBUTE_NAME_LIST = [
  "冷凝伤害加成",
  "热熔伤害加成",
  "导电伤害加成",
  "气动伤害加成",
  "衍射伤害加成",
  "湮灭伤害加成",
]

export const ATTRIBUTE_SHORT_NAME_LIST = ["冷凝", "热熔", "导电", "气动", "衍射", "湮灭"]

export const SKILL_ORDER = ["常态攻击", "共鸣技能", "共鸣回路", "共鸣解放", "变奏技能"]

export const ROLE_ATTRIBUTE_LIST_ORDER = [
  "生命",
  "攻击",
  "防御",
  "暴击",
  "暴击伤害",
  "共鸣效率",
  "普攻伤害加成",
  "重击伤害加成",
  "共鸣技能伤害加成",
  "共鸣解放伤害加成",
  "冷凝伤害加成",
  "热熔伤害加成",
  "导电伤害加成",
  "气动伤害加成",
  "衍射伤害加成",
  "湮灭伤害加成",
  "治疗效果加成",
]

// 声骸副词条
export const PHANTOM_SUB_VALUE = [
  { name: "攻击", values: ["30", "40", "50", "60"] },
  {
    name: "攻击%",
    values: ["6%", "6.4%", "7.1%", "7.9%", "8.6%", "9.4%", "10.1%", "10.9%", "11.6%"],
  },
  {
    name: "生命",
    values: ["320", "360", "390", "430", "470", "510", "540", "580"],
  },
  {
    name: "生命%",
    values: ["6%", "6.4%", "7.1%", "7.9%", "8.6%", "9.4%", "10.1%", "10.9%", "11.6%"],
  },
  { name: "防御", values: ["40", "50", "60", "70"] },
  {
    name: "防御%",
    values: ["8.1%", "9%", "10%", "10.9%", "11.8%", "12.8%", "13.8%", "14.7%"],
  },
  {
    name: "暴击",
    values: ["6.3%", "6.9%", "7.5%", "8.1%", "8.7%", "9.3%", "9.9%", "10.5%"],
  },
  {
    name: "暴击伤害",
    values: ["12.6%", "13.8%", "15%", "16.2%", "17.4%", "18.6%", "19.8%", "21.0%"],
  },
  {
    name: "普攻伤害加成",
    values: ["6%", "6.4%", "7.1%", "7.9%", "8.6%", "9.4%", "10.1%", "10.9%", "11.6%"],
  },
  {
    name: "重击伤害加成",
    values: ["6%", "6.4%", "7.1%", "7.9%", "8.6%", "9.4%", "10.1%", "10.9%", "11.6%"],
  },
  {
    name: "共鸣技能伤害加成",
    values: ["6%", "6.4%", "7.1%", "7.9%", "8.6%", "9.4%", "10.1%", "10.9%", "11.6%"],
  },
  {
    name: "共鸣解放伤害加成",
    values: ["6%", "6.4%", "7.1%", "7.9%", "8.6%", "9.4%", "10.1%", "10.9%", "11.6%"],
  },
  {
    name: "技能伤害加成",
    values: ["6%", "6.4%", "7.1%", "7.9%", "8.6%", "9.4%", "10.1%", "10.9%", "11.6%"],
  },
  {
    name: "共鸣效率",
    values: ["6.8%", "7.6%", "8.4%", "9.2%", "10%", "10.8%", "11.6%", "12.4%"],
  },
]

export const PHANTOM_SUB_VALUE_MAP = Object.fromEntries(
  PHANTOM_SUB_VALUE.map(item => [item.name, item.values]),
)

// 获取声骸副词条最大值
export function getPhantomSubMaxValue(name, value) {
  let valueList = PHANTOM_SUB_VALUE_MAP[name]

  // 处理百分比属性的特殊情况
  // 如果属性名是基础属性（攻击、防御、生命）但值是百分比格式，则查找对应的百分比配置
  if (["攻击", "防御", "生命"].includes(name) && value && value.toString().endsWith("%")) {
    valueList = PHANTOM_SUB_VALUE_MAP[name + "%"]
  }

  // 如果找不到对应的属性，返回当前值
  if (!valueList || valueList.length === 0) {
    // 调试时输出警告
    if (global.debugPhantom) {
      console.warn(`⚠️ 未找到属性 "${name}" (值: ${value}) 的最大值配置`)
    }
    return value
  }

  return valueList[valueList.length - 1]
}

// 判断是否为满值属性
export function isPhantomSubMaxValue(name, value) {
  if (!name || !value) return false

  const maxValue = getPhantomSubMaxValue(name, value)
  const currentValue = value.toString().trim()
  const maxValueStr = maxValue.toString().trim()

  // 精确字符串比较
  if (currentValue === maxValueStr) {
    return true
  }

  // 如果是数值，尝试数值比较
  if (!currentValue.includes("%")) {
    const currentNum = parseFloat(currentValue)
    const maxNum = parseFloat(maxValueStr)
    if (!isNaN(currentNum) && !isNaN(maxNum)) {
      return Math.abs(currentNum - maxNum) < 0.01 // 允许小的精度误差
    }
  } else {
    // 百分比比较，移除%后比较数值
    const currentPercent = parseFloat(currentValue.replace("%", ""))
    const maxPercent = parseFloat(maxValueStr.replace("%", ""))
    if (!isNaN(currentPercent) && !isNaN(maxPercent)) {
      return Math.abs(currentPercent - maxPercent) < 0.01 // 允许小的精度误差
    }
  }

  return false
}
