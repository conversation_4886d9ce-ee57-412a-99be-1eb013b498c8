import plugin from "../../../lib/plugins/plugin.js"
import wutheringWavesAPI from "../components/WutheringWavesAPI.js"
import User from "../components/User.js"
import config from "../components/config.js"
import Background from "../components/Background.js"
import { CharacterCache } from "../components/cache.js"
import { ImageManager } from "../utils/imageManager.js"
import { resourceDownloader } from "../utils/resourceDownloader.js"
import { renderer } from "../utils/renderer.js"

// 创建实例
const imageMapper = new ImageManager()

export class Refresh extends plugin {
  constructor() {
    super({
      name: "鸣潮-面板刷新",
      event: "message",
      priority: 400,
      rule: [
        {
          reg: config.generateCommandRegex("(刷新|更新)面板"),
          fnc: "refreshAllPanel",
        },
        {
          reg: config.generateCommandRegex("(刷新|更新)(.+)面板"),
          fnc: "refreshCharacterPanel",
        },
      ],
    })
  }

  // 刷新所有角色面板数据
  async refreshAllPanel(e) {
    try {
      const user = User.fromEvent(e)
      if (!user) {
        await e.reply("❌ 获取用户信息失败")
        return false
      }

      // 获取用户数据
      const uid = await user.getUid()
      const token = await user.getToken()
      const did = await user.getDid()

      if (!uid) {
        await e.reply(
          `❌ 您还未绑定UID\n请先使用：${config.getCommandExample("绑定uid 你的UID")}\n或使用：${config.getCommandExample("添加token token,did")}`,
        )
        return false
      }

      if (!token) {
        await e.reply(
          `❌ 您还未绑定token\n请先使用：${config.getCommandExample("添加token token,did")}`,
        )
        return false
      }

      if (!did) {
        await e.reply(
          `❌ 缺少设备ID\n请重新使用：${config.getCommandExample("添加token token,did")}`,
        )
        return false
      }

      // 检查缓存完整性，预估请求量
      const hasRoleCache = wutheringWavesAPI.roleDataCache.has(`role_info_${uid}`)
      const hasBatCache = wutheringWavesAPI.batTokenCache.has(`bat_${uid}`)

      // 检查角色详情缓存完整性（估算）
      const cacheKeys = Array.from(wutheringWavesAPI.roleDataCache.keys())
      const roleDetailCacheCount = cacheKeys.filter(key =>
        key.startsWith(`role_detail_${uid}_`),
      ).length

      // 如果没有基础缓存或角色详情缓存很少，说明需要大量请求
      const needsFullRefresh = !hasRoleCache || !hasBatCache || roleDetailCacheCount < 10

      if (needsFullRefresh) {
        await e.reply("🔄 正在刷新面板数据，请稍候...\n💡 需要获取完整数据，可能需要10-15秒")
      } else {
        await e.reply("🔄 正在刷新面板数据，请稍候...")
      }

      // 使用WutheringWavesAPI进行完整刷新
      const refreshResult = await wutheringWavesAPI.refreshCharDetail(uid, token, did, false)

      if (refreshResult.success) {
        // 渲染刷新面板图片
        await this.renderRefreshPanel(e, uid, refreshResult.data)
      } else {
        await e.reply(
          `❌ 面板刷新失败\n\n错误信息：${refreshResult.message}\n\n💡 可能原因：\n• token已过期\n• 网络连接问题\n• 服务器繁忙`,
        )
      }

      return true
    } catch (error) {
      console.error("刷新面板失败:", error)
      await e.reply("❌ 刷新面板失败，请稍后重试")
      return false
    }
  }

  // 刷新指定角色面板数据
  async refreshCharacterPanel(e) {
    console.log(`🔍 [Refresh] refreshCharacterPanel 被调用`)
    console.log(`🔍 [Refresh] 消息内容: ${e.msg}`)

    // 立即回复，确认命令被接收
    await e.reply("🔍 收到刷新角色面板命令，正在处理...")

    const match = e.msg.match(/(刷新|更新)(.+)面板$/)
    const charName = match ? match[2].trim() : ""

    console.log(`🔍 [Refresh] 匹配结果: ${JSON.stringify(match)}`)
    console.log(`🔍 [Refresh] 角色名: ${charName}`)

    if (!charName) {
      await e.reply(`❌ 请指定角色名\n使用方法：${config.getCommandExample("刷新维拉面板")}`)
      return false
    }

    const user = User.fromEvent(e)
    if (!user) {
      await e.reply("❌ 获取用户信息失败")
      return false
    }

    const uid = await user.getUid()
    const token = await user.getToken()
    const did = await user.getDid()

    if (!uid || !token || !did) {
      await e.reply(
        `❌ 请先绑定UID和token\n使用：${config.getCommandExample("添加token token,did")}`,
      )
      return false
    }

    await e.reply(`🔄 正在刷新 ${charName} 的面板数据...`)

    try {
      // 刷新所有角色数据（包含指定角色）
      const refreshResult = await wutheringWavesAPI.refreshCharDetail(uid, token, did)

      if (refreshResult.success) {
        await e.reply(
          `✅ ${charName} 面板数据刷新成功！\n\n📊 数据统计：\n• 总角色数: ${refreshResult.data.total || 0}\n• 更新角色: ${refreshResult.data.updated || 0}\n\n现在可以使用：${config.getCommandExample(`${charName}面板`)} 查看详细信息`,
        )
      } else {
        await e.reply(`❌ ${charName} 面板数据刷新失败\n错误信息：${refreshResult.message}`)
      }
    } catch (error) {
      console.error("刷新角色面板错误:", error)
      await e.reply(`❌ ${charName} 面板数据刷新失败`)
    }

    return true
  }

  // 渲染刷新面板图片
  async renderRefreshPanel(e, uid, refreshData) {
    try {
      // 获取角色数据
      const roleDetailList = CharacterCache.getRoleDetailList(uid)
      if (!roleDetailList || !Array.isArray(roleDetailList) || roleDetailList.length === 0) {
        await e.reply(
          `✅ 面板刷新成功！\n\n📊 数据统计：\n• 总角色数: ${refreshData.total || 0}\n• 更新角色: ${refreshData.updated || 0}\n• 未变化: ${refreshData.unchanged || 0}\n\n现在可以使用：\n• ${config.getCommandExample("面板")} - 查看角色面板\n• ${config.getCommandExample("角色名面板")} - 查看指定角色面板`,
        )
        return
      }

      // 智能资源检查：只下载缺失的资源
      console.log(`📊 处理 ${roleDetailList.length} 个角色的面板数据...`)

      // 快速检查是否有缺失的关键资源（角色头像）
      const missingAvatars = roleDetailList.filter(roleDetail => {
        const role = roleDetail.role || roleDetail
        if (!role.roleIconUrl) return false
        const localPath = imageMapper.checkLocalImageExists(role.roleIconUrl)
        return !localPath
      })

      if (missingAvatars.length > 0) {
        console.log(`📥 检测到 ${missingAvatars.length} 个角色缺少头像资源，开始下载...`)
        await resourceDownloader.downloadMultipleCharacterResources(missingAvatars)
      } else {
        console.log(`✅ 所有角色头像资源完整，无需下载`)
      }

      // 构建图片映射表
      const imageMap = new Map()
      roleDetailList.forEach(roleDetail => {
        const role = roleDetail.role || roleDetail
        if (role.roleIconUrl) {
          const localPath = imageMapper.checkLocalImageExists(role.roleIconUrl)
          imageMap.set(role.roleId, {
            roleId: role.roleId,
            localPath: localPath || role.roleIconUrl,
            originalUrl: role.roleIconUrl,
          })
        }
      })

      // 处理角色数据，转换为模板需要的格式
      const chars = roleDetailList.map((roleDetail, index) => {
        const role = roleDetail.role || roleDetail
        const roleName = role.roleName || "未知角色"

        // 获取角色头像路径
        let faceImage =
          imageMapper.getLocalPath(role.roleIconUrl) || "/character-images/default-avatar.webp"

        // 判断角色是否为本次更新的角色
        const isUpdated =
          refreshData.updatedRoleIds && refreshData.updatedRoleIds.includes(role.roleId)

        return {
          id: role.roleId || index,
          name: roleName,
          abbr: roleName,
          star: role.starLevel || 5,
          level: role.level || 1,
          cons: role.chainUnlockNum || 0,
          face: faceImage,
          isNew: isUpdated, // 基于实际更新状态
          isUpdate: isUpdated,
          roleId: role.roleId,
        }
      })

      // 对角色进行排序：已更新的角色排前面，五星优先
      chars.sort((a, b) => {
        // 首先按更新状态排序（已更新的在前）
        if (a.isNew !== b.isNew) {
          return b.isNew - a.isNew
        }

        // 然后按星级排序（五星在前）
        if (a.star !== b.star) {
          return b.star - a.star
        }

        // 最后按等级排序（高等级在前）
        return b.level - a.level
      })

      console.log(
        `📊 角色排序完成: 已更新角色 ${chars.filter(c => c.isNew).length} 个，五星角色 ${chars.filter(c => c.star === 5).length} 个`,
      )

      // 资源已在刷新数据时下载完成，无需等待

      // 获取背景图
      const background = await Background.getBackground("list")

      // 渲染数据
      const renderData = {
        uid,
        chars,
        servName: "库街区",
        hasNew: refreshData.updated > 0,
        msg: `本次刷新获取了 ${refreshData.total || 0} 个角色`,
        groupRank: false,
        background: background?.text,
        updateTime: { profile: new Date().toLocaleString() },
        allowRank: false,
        rankCfg: { limitTxt: "暂不支持排名" },
        game: "ww",
      }

      // 使用统一渲染工具
      const img = await renderer.render(e, "character/profile-list", renderData)

      if (img) {
        await e.reply(img)
      } else {
        // 如果渲染失败，发送文字消息
        await e.reply(
          `✅ 面板刷新成功！\n\n📊 数据统计：\n• 总角色数: ${refreshData.total || 0}\n• 更新角色: ${refreshData.updated || 0}\n• 未变化: ${refreshData.unchanged || 0}\n\n现在可以使用：\n• ${config.getCommandExample("面板")} - 查看角色面板\n• ${config.getCommandExample("角色名面板")} - 查看指定角色面板`,
        )
      }
    } catch (error) {
      console.error("渲染刷新面板图片失败:", error)
      // 如果渲染失败，发送文字消息
      await e.reply(
        `✅ 面板刷新成功！\n\n📊 数据统计：\n• 总角色数: ${refreshData.total || 0}\n• 更新角色: ${refreshData.updated || 0}\n• 未变化: ${refreshData.unchanged || 0}\n\n现在可以使用：\n• ${config.getCommandExample("面板")} - 查看角色面板\n• ${config.getCommandExample("角色名面板")} - 查看指定角色面板`,
      )
    }
  }
}
