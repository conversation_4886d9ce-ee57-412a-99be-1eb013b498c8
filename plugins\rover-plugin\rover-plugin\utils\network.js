import ky from "ky"

let cachedIp = null

/**
 * Get public IP with caching.
 * It tries multiple services and returns a default on failure.
 * @param {string} host Default host to return on failure
 * @returns {Promise<string>} The public IP address
 */
export async function getPublicIp(host = "***************", isCache = true) {
  if (isCache && cachedIp) return cachedIp

  try {
    // kurobbs
    const r = await ky.get("https://event.kurobbs.com/event/ip")
    if (r.ok) {
      const ip = await r.text()
      if (ip) return (cachedIp = ip.trim())
    }
  } catch {}

  try {
    // ipify
    const r = await ky.get("https://api.ipify.org/?format=json")
    if (r.ok) {
      const data = await r.json()
      if (data.ip) return (cachedIp = data.ip)
    }
  } catch {}

  try {
    // httpbin
    const r = await ky.get("https://httpbin.org/ip")
    if (r.ok) {
      const data = await r.json()
      if (data.origin) return (cachedIp = data.origin)
    }
  } catch {}

  return host
}
