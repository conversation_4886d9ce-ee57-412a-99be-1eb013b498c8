/* miao-plugin 风格的练度统计样式 */

.weapon_mode .for_talent {
  display: none !important;
}

.talent_mode .for_weapon {
  display: none !important;
}

.data-box {
  border-radius: 15px;
  margin-top: 20px;
  margin-bottom: 20px;
  padding: 0px 15px 5px 15px;
  overflow: hidden;
  background: #f5f5f5;
  box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.15);
  position: relative;
}

.tab_lable {
  position: absolute;
  top: -10px;
  left: -8px;
  background: #a98242;
  color: #fff;
  font-size: 14px;
  padding: 3px 10px;
  border-radius: 15px 0px 15px 15px;
  z-index: 20;
}

.data_line {
  display: flex;
  justify-content: space-around;
  margin-bottom: 14px;
}

.data_line_item {
  width: 100px;
  text-align: center;
}

.num {
  font-size: 24px;
}

.num .unit {
  font-size: 12px;
}

.char-list {
  display: table;
  border-collapse: collapse;
  width: calc(100% + 30px);
  margin: 0 -15px -5px;
  font-size: 12px;
  overflow: hidden;
}

.char-list .avatar {
  display: table-row;
  overflow: visible;
}

.char-list .avatar > div {
  box-shadow: 0 0 1px 0 #555 inset;
}

.char-list .avatar:nth-child(odd) {
  background: #e0e0e0;
}

.char-list .avatar:nth-child(1) {
  background: #ccc;
}

.char-list .avatar > div {
  display: table-cell;
  text-align: center;
  height: 30px;
  vertical-align: middle;
  line-height: 30px;
}

.char-list .avatar .index {
  color: #333;
  width: 30px;
  padding-left: 5px;
}

.char-list .avatar .name_cont {
  width: 80px;
}

.char-list .avatar .star4 {
  background: rgba(137, 189, 233, 0.6);
}

.char-list .avatar .star5 {
  background: rgba(239, 214, 137, 0.6);
}

.char-list .avatar .star3 {
  background: rgba(150, 150, 150, 0.6);
}

.char-list .avatar .name_cont {
  width: 80px;
}

.char-list .avatar .name {
  text-align: left;
  display: flex;
  width: 80px;
}

.char-list .th,
.char-list .th div {
  font-weight: bold;
  height: 40px;
  line-height: 40px;
  overflow: hidden;
}

.char-list .th .name {
  justify-content: center;
}

.char-list .avatar .name .avatar_img {
  width: 26px;
  height: 26px;
  position: relative;
  margin-right: 3px;
}

.char-list .avatar .name img {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 2px;
  margin-left: 3px;
  border-radius: 50%;
  object-fit: cover;
}

.char-list .avatar .name .avatar_name {
  white-space: nowrap;
  overflow: hidden;
  width: 48px;
}

.char-list .avatar .res {
  font-size: 12px;
  width: 90px;
}

.char-list .avatar .res img {
  width: 20px;
  height: 20px;
  vertical-align: middle;
}

.char-list .avatar > div.fetter10 {
  background: url("./hart.png") center center no-repeat;
  background-size: contain;
  color: #fff;
}

.char-list .char-cons {
  width: 400px;
  position: relative;
  z-index: 98;
  overflow: visible;
}

.char-list .cons-pct,
.char-list .cons-bg {
  display: flex;
}

.char-list .th .cons-pct {
  margin: 0;
  color: #fff;
  font-weight: bold;
}

.char-list .th .cons-pct > div:first-child {
  padding-left: 10px;
}

.char-list .th .cons-pct > div:last-child {
  padding-right: 10px;
}

.char-list .cons-pct {
  margin: 0 10px;
  z-index: 100;
  position: relative;
  color: #fff;
}

.char-list .cons-pct > div {
  flex: 1;
  font-size: 12px;
  mix-blend-mode: difference;
  font-weight: normal;
  text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.7);
}

.char-list .life_bg {
  background: #888;
}

.char-list .cons-bg {
  position: absolute;
  left: 5px;
  right: 5px;
  bottom: 5px;
  height: 20px;
  z-index: 99;
  border-radius: 3px;
  overflow: hidden;
  background: #888;
}

.char-list .cons-bg > div {
  height: 20px;
}

.char-list .cons-bg > div:last-child {
  border-radius: 0 3px 3px 0;
}

/* 练度等级颜色 */
.char-list .cons-bg .cons-n0 {
  background: #95a5a6; /* 1-20级 - 灰色 */
}

.char-list .cons-bg .cons-1 {
  background: #3498db; /* 21-40级 - 蓝色 */
}

.char-list .cons-bg .cons-2 {
  background: #2ecc71; /* 41-60级 - 绿色 */
}

.char-list .cons-bg .cons-3 {
  background: #f39c12; /* 61-70级 - 橙色 */
}

.char-list .cons-bg .cons-4 {
  background: #e74c3c; /* 71-80级 - 红色 */
}

.char-list .cons-bg .cons-5 {
  background: #9b59b6; /* 81-90级 - 紫色 */
}

.char-list .cons-bg .cons-6 {
  background: #f1c40f; /* 满级 - 金色 */
}

/* 表头颜色 */
.char-list .th .cons-pct .cons-n0 {
  background: #95a5a6;
}

.char-list .th .cons-pct .cons-1 {
  background: #3498db;
}

.char-list .th .cons-pct .cons-2 {
  background: #2ecc71;
}

.char-list .th .cons-pct .cons-3 {
  background: #f39c12;
}

.char-list .th .cons-pct .cons-4 {
  background: #e74c3c;
}

.char-list .th .cons-pct .cons-5 {
  background: #9b59b6;
}

.char-list .th .cons-pct .cons-6 {
  background: #f1c40f;
}

/* 平均等级显示 */
.char-list .avatar .pct {
  width: 60px;
  font-weight: bold;
  color: #2c3e50;
}

.char-list .avatar .lvl {
  width: 60px;
  font-weight: bold;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .char-list .char-cons {
    width: 300px;
  }
  
  .char-list .cons-pct > div {
    font-size: 10px;
  }
}
