# ===== 依赖和包管理 =====
# Node.js 依赖包（用户需要自己安装）
node_modules/

# 包管理器锁文件（避免版本冲突，用户自己生成）
package-lock.json
yarn.lock
pnpm-lock.yaml

# 包管理器日志
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# ===== 用户配置和数据 =====
# 用户自定义配置文件（保留默认配置模板）
config/*.yaml

# 数据库文件（包含用户隐私数据）
*.db
*.db-shm
*.db-wal
*.sqlite
*.sqlite3

# 用户数据目录（包含用户个人游戏数据）
data/

# ===== 运行时文件 =====
# 日志文件
logs/
*.log

# 临时文件和缓存
temp/
.tmp/
.temp/
.cache/
cache/

# 进程文件
*.pid
*.seed
*.pid.lock

# ===== 开发工具 =====
# 编辑器配置
.vscode/
.idea/
*.swp
*.swo
*~

# 测试文件（插件发布不需要测试代码）
test/
tests/
*.test.js
*.spec.js
test-*.js
debug-*.js
temp-*.js

# 测试覆盖率
coverage/
.nyc_output/

# ===== 构建和部署 =====
# 构建输出
dist/
build/
out/

# ===== 环境变量和敏感信息 =====
# 环境变量文件（可能包含API密钥等敏感信息）
.env
.env.*
secrets/
private/

# ===== 系统文件 =====
# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# ===== 备份和临时文件 =====
# 备份文件
*.bak
*.backup
*.old
*.orig

# 临时文件
*.tmp
*.temp

# 压缩文件（通常是临时的）
*.zip
*.tar.gz
*.rar