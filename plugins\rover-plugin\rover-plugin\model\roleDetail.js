import {
  ATTRIBUTE_NAME_LIST,
  SKILL_ORDER,
  ROLE_ATTRIBUTE_LIST_ORDER,
} from "../components/common/const.js"
import { isPhantomSubMaxValue } from "../components/common/const.js"
import {
  getCalcFile,
  calcPhantomScore,
  getPhantomColorBackground,
} from "../components/common/calc.js"
import { scoreCalculator } from "../utils/scoreCalculator.js"

class RoleDetail {
  constructor(data) {
    if (typeof data === "string") {
      data = JSON.parse(data)
    }

    const role = data.role || {}
    const weaponData = data.weaponData || {}
    // 获取计算文件
    this.calcFile = getCalcFile(role.roleId)

    // 角色基础信息
    this.level = role.level // 等级
    this.charName = role.roleName // 角色名
    this.charId = role.roleId // 角色ID
    this.pic = role.roleIconUrl // 角色头像
    this.pile = role.rolePicUrl // 角色立绘
    this.starLevel = role.starLevel // 星级
    this.breach = role.breach // 突破
    this.chainUnlockNum = role.chainUnlockNum // 共鸣链解锁数量
    this.isMainRole = role.isMainRole // 是否为主角
    this.attributeName = role.attributeName // 属性名称 冷凝, 热熔, 导电, 气动, 衍射, 湮灭
    this.attributeId = role.attributeId // 属性ID

    // 武器信息
    this.weapon = {
      id: weaponData.weapon?.weaponId, // 武器ID
      type: weaponData.weapon?.weaponType, // 武器类型
      typeName: role.weaponTypeName, // 武器类型名称
      level: weaponData.level, // 武器等级
      breach: weaponData.breach, // 突破等级
      starLevel: weaponData.weapon?.weaponStarLevel, // 星级
      name: weaponData.weapon?.weaponName, // 武器名称
      effect: weaponData.weapon?.effectDescription, // 武器效果
      pic: weaponData.weapon?.weaponIcon, // 武器图标
      resonLevel: weaponData.resonLevel, // 精炼等级
      mainPropList: weaponData.mainPropList?.map(p => ({
        attributeName: p.attributeName,
        attributeValue: p.attributeValue,
        pic: p.iconUrl,
      })), // 主属性
    }

    // 角色属性 - 动态显示
    // 去掉非attributeName的在ATTRIBUTE_NAME_LIST的伤害加成
    // 按照 ROLE_ATTRIBUTE_LIST_ORDER 排序
    this.roleAttributeList = data.roleAttributeList
      ?.filter(
        a =>
          !ATTRIBUTE_NAME_LIST.includes(a.attributeName) ||
          a.attributeName === `${this.attributeName}伤害加成`,
      )
      .map(a => ({
        name: a.attributeName,
        value: a.attributeValue,
        pic: a.iconUrl,
        valid: getPhantomColorBackground(a.attributeName, this.calcFile),
      }))
      .sort(
        (a, b) =>
          ROLE_ATTRIBUTE_LIST_ORDER.indexOf(a.name) - ROLE_ATTRIBUTE_LIST_ORDER.indexOf(b.name),
      )

    // 技能信息 - 按指定顺序排序
    const skillMap = new Map()
    // 先将技能按类型分组
    data.skillList?.forEach(s => {
      if (s.skill?.type && SKILL_ORDER.includes(s.skill.type)) {
        skillMap.set(s.skill.type, {
          type: s.skill.type, // 技能类型
          name: s.skill.name, // 技能名字
          pic: s.skill.iconUrl, // 技能图标
          level: s.level, // 技能等级
        })
      }
    })

    // 按指定顺序排列技能
    this.skillList = SKILL_ORDER.map(type => skillMap.get(type)).filter(Boolean)

    // 共鸣链信息
    this.chainList = data.chainList?.map(c => ({
      name: c.name, // 共鸣链名称
      pic: c.iconUrl, // 共鸣链图标
      unlocked: c.unlocked, // 是否解锁
    }))

    // 声骸属性
    // 排序：按照 ROLE_ATTRIBUTE_LIST_ORDER 排序
    this.equipPhantomAddPropList = data.equipPhantomAddPropList
      ?.map(p => ({
        name: p.attributeName, // 属性名称
        value: p.attributeValue, // 属性值
        pic: p.iconUrl, // 属性图标
        valid: getPhantomColorBackground(p.attributeName, this.calcFile), // 是否有效
      }))
      .sort(
        (a, b) =>
          ROLE_ATTRIBUTE_LIST_ORDER.indexOf(a.name) - ROLE_ATTRIBUTE_LIST_ORDER.indexOf(b.name),
      )

    // 声骸属性
    this.equipPhantomAttributeList = data.equipPhantomAttributeList?.map(a => ({
      name: a.attributeName, // 属性名称
      value: a.attributeValue, // 属性值
      pic: a.iconUrl, // 属性图标
      valid: getPhantomColorBackground(a.attributeName, this.calcFile), // 是否有效
    }))

    // 声骸消耗
    this.cost = data.phantomData?.cost

    // 声骸信息
    this.equipPhantomList = data.phantomData?.equipPhantomList
      ?.filter(p => p && p.phantomProp)
      ?.map(p => ({
        fetterDetail: p.fetterDetail
          ? {
              name: p.fetterDetail.name, // 套装名称
              pic: p.fetterDetail.iconUrl, // 套装图标
              firstDescription: p.fetterDetail.firstDescription, // 第一描述
              secondDescription: p.fetterDetail.secondDescription, // 第二描述
              num: p.fetterDetail.num, // 套装数量
            }
          : null,
        cost: p.cost, // 声骸消耗
        id: p.phantomProp?.phantomId, // 声骸ID
        level: p.level, // 声骸等级
        name: p.phantomProp?.name, // 声骸名称
        pic: p.phantomProp?.iconUrl, // 声骸图标
        quality: p.phantomProp?.quality, // 声骸品质
        // 主属性 - 解析所有主属性（通常是2行）
        mainPropList:
          p.mainProps?.map(mainProp => ({
            attributeName: mainProp.attributeName,
            attributeValue: mainProp.attributeValue,
            pic: mainProp.iconUrl,
          })) || [],
        // 副属性列表
        subPropList:
          p.subProps?.map(sub => {
            const isMax = isPhantomSubMaxValue(sub.attributeName, sub.attributeValue)
            // 调试日志：输出实际的副属性数据
            if (process.env.NODE_ENV === "development" || global.debugPhantom) {
              console.log(
                `🔍 副属性调试: ${sub.attributeName} = ${sub.attributeValue}, 满值判断: ${isMax}`,
              )
            }
            return {
              attributeName: sub.attributeName,
              attributeValue: sub.attributeValue,
              pic: sub.iconUrl,
              isMax: isMax,
              valid: getPhantomColorBackground(sub.attributeName, this.calcFile),
            }
          }) || [],
        score: calcPhantomScore(
          [...(p.mainProps || [null, null]), ...(p.subProps || [])],
          p.cost,
          this.calcFile,
        ),
      }))

    // 总评分 - 使用统一的评分计算器
    this.totalScore = scoreCalculator.calculateTotalScore(this)

    // 评级 - 使用统一的评分计算器
    this.totalScoreBackground = scoreCalculator.getScoreGrade(this.totalScore, this.calcFile)
  }
}

export default RoleDetail
