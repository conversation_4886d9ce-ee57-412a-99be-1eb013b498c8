import plugin from "../../../lib/plugins/plugin.js"
import User from "../components/User.js"
import config from "../components/config.js"
import { CharacterCache } from "../components/cache.js"
import { ImageManager } from "../utils/imageManager.js"
import { renderer } from "../utils/renderer.js"
import Background from "../components/Background.js"
import { DEFAULT_CONFIG, ERROR_MESSAGES, TEMPLATE_PATHS, GAME_CONFIG } from "../utils/constants.js"

// 创建实例
const imageMapper = new ImageManager()

/**
 * 角色卡片功能
 * 参考 WutheringWavesUID 的 wutheringwaves_roleinfo 实现
 * 使用 miao-plugin 风格的 UI 设计
 */
export class RoleInfo extends plugin {
  constructor() {
    super({
      name: "鸣潮-角色卡片",
      event: "message",
      priority: 600,
      rule: [
        {
          reg: config.generateCommandRegex("卡片"),
          fnc: "roleCards",
        },
      ],
    })
  }

  /**
   * 角色卡片列表 - miao风格
   */
  async roleCards(e) {
    try {
      await e.reply("🔄 正在生成角色卡片，请稍候...")

      // 获取用户信息
      const user = User.fromEvent(e)
      if (!user) {
        await e.reply(ERROR_MESSAGES.USER_NOT_FOUND)
        return false
      }

      // 获取用户数据
      const userData = await user.getAllUserData()
      const { uid } = userData

      if (!uid) {
        await e.reply(
          `❌ 您还未绑定UID\n请先使用：${config.getCommandExample("绑定uid 你的UID")}\n或使用：${config.getCommandExample("添加token token,did")}`,
        )
        return false
      }

      // 获取角色数据
      const roleDetailList = CharacterCache.getRoleDetailList(uid)
      if (!roleDetailList || !Array.isArray(roleDetailList) || roleDetailList.length === 0) {
        await e.reply(
          `❌ 未找到角色数据\n请先使用 ${config.getCommandExample("刷新面板")} 获取角色数据`,
        )
        return false
      }

      // 渲染角色卡片
      await this.renderRoleCards(e, uid, roleDetailList)
      return true
    } catch (error) {
      console.error("角色卡片生成失败:", error)
      await e.reply("❌ 角色卡片生成失败，请稍后重试")
      return false
    }
  }

  /**
   * 渲染角色卡片 - miao风格
   */
  async renderRoleCards(e, uid, roleDetailList) {
    try {
      // 处理角色数据
      const processedChars = await this.processCharacterData(roleDetailList)

      // 对角色进行排序：五星优先，然后按等级排序
      processedChars.sort((a, b) => {
        // 首先按星级排序（五星在前）
        if (a.star !== b.star) {
          return b.star - a.star
        }
        // 然后按等级排序（高等级在前）
        return b.level - a.level
      })

      // 获取背景图
      const background = await Background.getBackground("list")

      // 渲染数据
      const renderData = {
        uid,
        chars: processedChars,
        servName: GAME_CONFIG.DEFAULT_SERVER,
        hasNew: false,
        msg: `共 ${processedChars.length} 个角色`,
        groupRank: false,
        background: background?.text,
        updateTime: { profile: new Date().toLocaleString() },
        allowRank: false,
        rankCfg: { limitTxt: `使用 ${config.getCommandExample("角色名面板")} 查看详细面板` },
        game: GAME_CONFIG.GAME_ID,
        element: DEFAULT_CONFIG.ELEMENT, // 默认元素，用于主题色
      }

      // 渲染图片 - 使用 WutheringWavesUID 风格
      const img = await renderer.render(e, TEMPLATE_PATHS.WW_STYLE, renderData)

      if (img) {
        await e.reply(img)
      } else {
        await e.reply("❌ 角色卡片渲染失败，请稍后重试")
      }
    } catch (error) {
      console.error("渲染角色卡片失败:", error)
      await e.reply("❌ 渲染角色卡片失败，请稍后重试")
    }
  }

  /**
   * 处理角色数据，转换为模板需要的格式
   * 参考 card.js 的实现方式
   */
  async processCharacterData(roleDetailList) {
    // 先检查所有角色的头像是否存在
    const existingImageMap = new Map()
    roleDetailList.forEach(roleDetail => {
      const role = roleDetail.role || roleDetail
      if (role.roleIconUrl) {
        const existingPath = imageMapper.checkLocalImageExists(role.roleIconUrl)
        if (existingPath) {
          existingImageMap.set(role.roleId, {
            roleId: role.roleId,
            localPath: existingPath,
            originalUrl: role.roleIconUrl,
          })
        }
      }
    })

    // 处理角色数据，转换为模板需要的格式
    const processedChars = roleDetailList.map((roleDetail, index) => {
      const role = roleDetail.role || roleDetail
      const roleName = role.roleName || "未知角色"

      // 从已存在的图片中获取头像路径
      let faceImage = "/character-images/default-avatar.webp"
      const imageResult = existingImageMap.get(role.roleId)
      if (
        imageResult &&
        imageResult.localPath &&
        imageResult.localPath !== imageResult.originalUrl
      ) {
        faceImage = imageMapper.getLocalPath(imageResult.originalUrl)
      }

      return {
        id: role.roleId || index,
        name: roleName,
        abbr: roleName,
        star: role.starLevel || 5,
        starDisplay: "★".repeat(role.starLevel || 5), // 预生成星级字符串
        level: role.level || 1,
        cons: role.chainUnlockNum || 0,
        face: faceImage,
        isNew: false,
        isUpdate: false,
        roleId: role.roleId,
      }
    })

    return processedChars
  }
}
