/**
 * 功能测试脚本 - 测试插件的绑定、删除等功能
 * 包括：绑定多UID、绑定多token、删除UID、删除所有UID、删除token
 * 测试完成后会自动删除此脚本
 */

import { Database } from "./lib/api.js"
import fs from "fs"
import path from "path"

// 测试配置
const TEST_CONFIG = {
  userId: "test_user_12345",
  token: "eyJhbGciOiJIUzI1NiJ9.eyJjcmVhdGVkIjoxNzUzNjMxMjg2Njc1LCJ1c2VySWQiOjEwODE2NzMyfQ.Z9BLJ1jtanrNfl1JLvcifr8ypSXEu7513sQvy9I-A1c",
  did: "6F02FE7B671ACA64694F19FB67EBEBAD07659846",
  testUids: ["100000001", "100000002", "100000003"], // 测试用的多个UID
  testTokens: [
    "test_token_1",
    "test_token_2", 
    "test_token_3"
  ]
}

// 测试结果统计
let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: []
}

// 测试工具函数
function logTest(name, success, message = "") {
  testResults.total++
  if (success) {
    testResults.passed++
    console.log(`✅ ${name}: 通过 ${message}`)
  } else {
    testResults.failed++
    testResults.errors.push(`${name}: ${message}`)
    console.log(`❌ ${name}: 失败 ${message}`)
  }
}

async function safeTest(name, testFn) {
  try {
    const result = await testFn()
    logTest(name, true, result || "")
    return result
  } catch (error) {
    logTest(name, false, error.message)
    return null
  }
}

// 清理测试数据
async function cleanupTestData() {
  console.log("\n🧹 === 清理测试数据 ===")
  
  try {
    // 删除测试用户的所有数据
    await Database.WavesUser.destroy({
      where: { user_id: TEST_CONFIG.userId }
    })
    
    await Database.WavesBind.destroy({
      where: { user_id: TEST_CONFIG.userId }
    })
    
    console.log("✅ 测试数据清理完成")
  } catch (error) {
    console.error("❌ 清理测试数据失败:", error)
  }
}

// 测试绑定单个UID
async function testBindSingleUid() {
  console.log("\n🔗 === 测试绑定单个UID ===")
  
  await safeTest("绑定单个UID", async () => {
    const uid = TEST_CONFIG.testUids[0]
    
    // 创建绑定记录
    await Database.WavesBind.create({
      user_id: TEST_CONFIG.userId,
      bot_id: "default",
      uid: uid
    })
    
    // 验证绑定
    const result = await Database.WavesBind.getUidByUser(TEST_CONFIG.userId)
    return result === uid ? `成功绑定UID: ${uid}` : "绑定失败"
  })
}

// 测试绑定多个UID
async function testBindMultipleUids() {
  console.log("\n🔗 === 测试绑定多个UID ===")
  
  await safeTest("绑定多个UID", async () => {
    // 先清理现有绑定
    await Database.WavesBind.destroy({
      where: { user_id: TEST_CONFIG.userId }
    })
    
    // 绑定多个UID（用_分隔）
    const multiUid = TEST_CONFIG.testUids.join("_")
    
    await Database.WavesBind.create({
      user_id: TEST_CONFIG.userId,
      bot_id: "default", 
      uid: multiUid
    })
    
    // 验证绑定
    const result = await Database.WavesBind.getUidByUser(TEST_CONFIG.userId)
    return result === TEST_CONFIG.testUids[0] ? `成功绑定多个UID: ${multiUid}` : "多UID绑定失败"
  })
}

// 测试绑定多个token
async function testBindMultipleTokens() {
  console.log("\n🔑 === 测试绑定多个Token ===")
  
  await safeTest("绑定多个Token", async () => {
    // 先清理现有token
    await Database.WavesUser.destroy({
      where: { user_id: TEST_CONFIG.userId }
    })
    
    // 绑定多个token
    for (let i = 0; i < TEST_CONFIG.testTokens.length; i++) {
      await Database.WavesUser.create({
        user_id: TEST_CONFIG.userId,
        bot_id: "default",
        uid: TEST_CONFIG.testUids[i] || "",
        cookie: TEST_CONFIG.testTokens[i],
        did: TEST_CONFIG.did,
        status: ""
      })
    }
    
    // 验证绑定
    const accounts = await Database.WavesUser.findAll({
      where: { user_id: TEST_CONFIG.userId }
    })
    
    return accounts.length === TEST_CONFIG.testTokens.length ? 
      `成功绑定${accounts.length}个Token` : "多Token绑定失败"
  })
}

// 测试删除单个UID
async function testDeleteSingleUid() {
  console.log("\n🗑️ === 测试删除单个UID ===")
  
  await safeTest("删除单个UID", async () => {
    // 获取当前绑定的UID
    const currentBind = await Database.WavesBind.findOne({
      where: { user_id: TEST_CONFIG.userId }
    })
    
    if (!currentBind) {
      return "没有找到绑定记录"
    }
    
    const uids = currentBind.uid.split("_").filter(Boolean)
    if (uids.length <= 1) {
      return "只有一个UID，无法测试删除单个"
    }
    
    // 删除第一个UID
    const remainingUids = uids.slice(1)
    await Database.WavesBind.update(
      { uid: remainingUids.join("_") },
      { where: { user_id: TEST_CONFIG.userId } }
    )
    
    // 验证删除
    const result = await Database.WavesBind.getUidByUser(TEST_CONFIG.userId)
    return result === remainingUids[0] ? `成功删除一个UID，剩余: ${remainingUids.join("_")}` : "删除UID失败"
  })
}

// 测试删除所有UID
async function testDeleteAllUids() {
  console.log("\n🗑️ === 测试删除所有UID ===")
  
  await safeTest("删除所有UID", async () => {
    // 删除所有UID绑定
    const deletedCount = await Database.WavesBind.destroy({
      where: { user_id: TEST_CONFIG.userId }
    })
    
    // 验证删除
    const result = await Database.WavesBind.getUidByUser(TEST_CONFIG.userId)
    return result === null ? `成功删除所有UID绑定，删除了${deletedCount}条记录` : "删除所有UID失败"
  })
}

// 测试删除token
async function testDeleteToken() {
  console.log("\n🗑️ === 测试删除Token ===")
  
  await safeTest("删除单个Token", async () => {
    // 删除第一个token
    const deletedCount = await Database.WavesUser.destroy({
      where: { 
        user_id: TEST_CONFIG.userId,
        cookie: TEST_CONFIG.testTokens[0]
      }
    })
    
    // 验证删除
    const remainingAccounts = await Database.WavesUser.findAll({
      where: { user_id: TEST_CONFIG.userId }
    })
    
    return deletedCount > 0 ? 
      `成功删除Token，剩余${remainingAccounts.length}个账号` : "删除Token失败"
  })
  
  await safeTest("删除所有Token", async () => {
    // 删除所有token
    const deletedCount = await Database.WavesUser.destroy({
      where: { user_id: TEST_CONFIG.userId }
    })
    
    // 验证删除
    const result = await Database.WavesUser.getPrimaryAccount(TEST_CONFIG.userId)
    return result === null ? `成功删除所有Token，删除了${deletedCount}条记录` : "删除所有Token失败"
  })
}

async function printTestSummary() {
  console.log("\n📊 === 功能测试结果汇总 ===")
  console.log(`总测试数: ${testResults.total}`)
  console.log(`通过: ${testResults.passed}`)
  console.log(`失败: ${testResults.failed}`)
  console.log(`成功率: ${((testResults.passed / testResults.total) * 100).toFixed(2)}%`)
  
  if (testResults.errors.length > 0) {
    console.log("\n❌ 失败的测试:")
    testResults.errors.forEach(error => console.log(`  - ${error}`))
  }
  
  console.log(testResults.failed === 0 ? "\n🎉 所有功能测试通过！" : "\n⚠️ 部分功能测试失败，请检查上述错误")
}

// 自动删除测试脚本
async function deleteSelf() {
  try {
    const scriptPath = path.resolve("plugins/rover-plugin/function-test.js")
    fs.unlinkSync(scriptPath)
    console.log("\n🗑️ 功能测试脚本已自动删除")
  } catch (error) {
    console.log("\n⚠️ 无法自动删除测试脚本，请手动删除 function-test.js")
  }
}

// 主测试函数
async function runFunctionTests() {
  console.log("🧪 开始Rover Plugin功能测试...")
  console.log(`测试用户ID: ${TEST_CONFIG.userId}`)
  console.log(`测试UID: ${TEST_CONFIG.testUids.join(", ")}`)
  console.log(`测试Token数量: ${TEST_CONFIG.testTokens.length}`)
  
  try {
    // 清理旧数据
    await cleanupTestData()
    
    // 运行功能测试
    await testBindSingleUid()
    await testBindMultipleUids()
    await testBindMultipleTokens()
    await testDeleteSingleUid()
    await testDeleteAllUids()
    await testDeleteToken()
    
    await printTestSummary()
    
    // 最终清理
    await cleanupTestData()
    
    // 如果所有测试通过，自动删除脚本
    if (testResults.failed === 0) {
      console.log("\n✨ 所有功能测试通过，准备删除测试脚本...")
      setTimeout(deleteSelf, 2000) // 2秒后删除
    }
    
  } catch (error) {
    console.error("❌ 功能测试过程中发生严重错误:", error)
  }
}

// 运行测试
runFunctionTests()
