<!DOCTYPE html>
<html lang="zh-cn">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width">
  <link rel="shortcut icon" href="#"/>
  <link rel="preload" href="{{_res_path}}/common/font/HYWH-65W.woff" as="font" type="font/woff">
  <link rel="preload" href="{{_res_path}}/common/font/NZBZ.woff" as="font" type="font/woff">
  <link rel="preload" href="{{_res_path}}/common/font/tttgbnumber.woff" as="font" type="font/woff">
  <link rel="stylesheet" type="text/css" href="{{_res_path}}/common/common.css"/>
  <link rel="stylesheet" type="text/css" href="{{_res_path}}/character/ww-style.css"/>
  {{if background}}{{@background}}{{/if}}
  <title>鸣潮角色卡片</title>
</head>
{{set elemCls = {火:'pyro',冰:'cryo',风:'anemo',雷:'electro',量子:'quantum',虚数:'geo',物理:'sr', }[element||elem] || element || elem || 'hydro' }}
<body class="elem-{{elemCls}} {{displayMode || mode || `default`}}-mode {{bodyClass}}" {{sys.scale}}>
<div class="container elem-bg" id="container">
{{set demo = chars[0]?.abbr || "长离" }}

<!-- WutheringWavesUID 风格的角色卡片 -->
<div class="ww-card-container">
  <!-- 头部信息区域 -->
  <div class="header-section">
    <!-- 用户信息背景 -->
    <div class="base-info-bg">
      <div class="user-info">
        <div class="user-name">{{uid}}</div>
        <div class="user-id">特征码: {{uid}}</div>
      </div>
      <!-- 用户头像区域 -->
      <div class="user-avatar">
        <div class="avatar-ring"></div>
        <div class="avatar-img"></div>
      </div>
    </div>
    
    <!-- 标题栏 -->
    {{if updateTime.profile}}
    <div class="title-bar">
      <div class="level-info">
        <div class="level-label">更新时间</div>
        <div class="level-value">{{updateTime.profile}}</div>
      </div>
      <div class="level-info">
        <div class="level-label">角色数量</div>
        <div class="level-value">{{chars.length}}</div>
      </div>
    </div>
    {{/if}}
  </div>

  <!-- 基本信息区域 -->
  {{if servName}}
  <div class="basic-info-section">
    <div class="line-title">
      <span>基本信息</span>
    </div>
    <div class="info-blocks">
      <div class="info-block color-y">
        <div class="info-value">{{chars.length}}</div>
        <div class="info-key">角色数量</div>
      </div>
      <div class="info-block">
        <div class="info-value">{{servName}}</div>
        <div class="info-key">服务器</div>
      </div>
      {{if updateTime.profile}}
      <div class="info-block color-g">
        <div class="info-value">已更新</div>
        <div class="info-key">数据状态</div>
      </div>
      {{/if}}
    </div>
  </div>
  {{/if}}

  <!-- 角色信息区域 -->
  <div class="character-section">
    <div class="line-title">
      <span>角色信息</span>
    </div>
    
    <div class="character-grid">
      {{each chars char}}
      <div class="char-card">
        <!-- 角色背景 -->
        <div class="char-bg">
          <!-- 角色头像 -->
          <div class="char-avatar">
            <img src="{{_res_path}}{{char.face}}" alt="{{char.name}}" class="avatar-img">
          </div>
          
          <!-- 属性图标 -->
          <div class="attribute-icon">
            <!-- 这里可以根据角色属性显示对应图标 -->
          </div>
          
          <!-- 等级信息 -->
          <div class="char-level">LV.{{char.level}}</div>
          
          <!-- 武器背景区域 -->
          <div class="weapon-bg">
            <!-- 武器图标位置 -->
            <div class="weapon-icon"></div>
          </div>
          
          <!-- 命座信息 -->
          {{if char.cons > 0}}
          <div class="chain-info">
            <span>{{char.cons}}命</span>
          </div>
          {{/if}}
          
          <!-- 星级显示 -->
          <div class="star-display rarity-{{char.star}}">
            {{char.starDisplay}}
          </div>
        </div>
      </div>
      {{/each}}
    </div>
  </div>

  <!-- 右侧装饰 -->
  <div class="right-decoration">
    <!-- 装饰图案 -->
  </div>

  <!-- 底部信息 -->
  <div class="footer-info">
    <div class="footer-text">{{@copyright || sys?.copyright}}</div>
  </div>
</div>

</div>
</body>
</html>
