@font-face {
  font-family: 'Number';
  src: url("./font/tttgbnumber.woff") format('woff'), url("./font/tttgbnumber.ttf") format('truetype');
}
@font-face {
  font-family: 'NZBZ';
  src: url("./font/NZBZ.woff") format('woff'), url("./font/NZBZ.ttf") format('truetype');
}
@font-face {
  font-family: 'YS';
  src: url("./font/HYWH-65W.woff") format('woff'), url("./font/HYWH-65W.ttf") format('truetype');
}
.font-YS {
  font-family: Number, "汉仪文黑-65W", YS, PingFangSC-Medium, "PingFang SC", sans-serif;
}
.font-NZBZ {
  font-family: Number, "印品南征北战NZBZ体", NZBZ, "汉仪文黑-65W", YS, PingFangSC-Medium, "PingFang SC", sans-serif;
}
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  -webkit-user-select: none;
  user-select: none;
}
body {
  font-size: 18px;
  color: #1e1f20;
  font-family: Number, "汉仪文黑-65W", YS, PingFangSC-Medium, "PingFang SC", sans-serif;
  transform: scale(1.4);
  transform-origin: 0 0;
  width: 600px;
}
.container {
  width: 600px;
  padding: 20px 15px 10px 15px;
  background-size: contain;
}
.head-box {
  border-radius: 15px;
  padding: 10px 20px;
  position: relative;
  color: #fff;
  margin-top: 30px;
}
.head-box .title {
  font-family: Number, "印品南征北战NZBZ体", NZBZ, "汉仪文黑-65W", YS, PingFangSC-Medium, "PingFang SC", sans-serif;
  font-size: 36px;
  text-shadow: 0 0 1px #000, 1px 1px 3px rgba(0, 0, 0, 0.9);
}
.head-box .title .label {
  display: inline-block;
  margin-left: 10px;
}
.head-box .genshin_logo {
  position: absolute;
  top: 1px;
  right: 15px;
  width: 97px;
}
.head-box .label {
  font-size: 16px;
  text-shadow: 0 0 1px #000, 1px 1px 3px rgba(0, 0, 0, 0.9);
}
.head-box .label span {
  color: #d3bc8e;
  padding: 0 2px;
}
.notice {
  color: #888;
  font-size: 12px;
  text-align: right;
  padding: 12px 5px 5px;
}
.notice-center {
  color: #fff;
  text-align: center;
  margin-bottom: 10px;
  text-shadow: 1px 1px 1px #333;
}
.copyright {
  font-size: 14px;
  text-align: center;
  color: #fff;
  position: relative;
  padding-left: 10px;
  text-shadow: 1px 1px 1px #000;
  margin: 10px 0;
}
.copyright .version {
  color: #d3bc8e;
  display: inline-block;
  padding: 0 3px;
}
/*  */
.cons {
  display: inline-block;
  vertical-align: middle;
  padding: 0 5px;
  border-radius: 4px;
}
.cons-0 {
  background: #666;
  color: #fff;
}
.cons-n0 {
  background: #404949;
  color: #fff;
}
.cons-1 {
  background: #5cbac2;
  color: #fff;
}
.cons-2 {
  background: #339d61;
  color: #fff;
}
.cons-3 {
  background: #3e95b9;
  color: #fff;
}
.cons-4 {
  background: #3955b7;
  color: #fff;
}
.cons-5 {
  background: #531ba9cf;
  color: #fff;
}
.cons-6 {
  background: #ff5722;
  color: #fff;
}
.cons2-0 {
  border-radius: 4px;
  background: #666;
  color: #fff;
}
.cons2-1 {
  border-radius: 4px;
  background: #71b1b7;
  color: #fff;
}
.cons2-2 {
  border-radius: 4px;
  background: #369961;
  color: #fff;
}
.cons2-3 {
  border-radius: 4px;
  background: #4596b9;
  color: #fff;
}
.cons2-4 {
  border-radius: 4px;
  background: #4560b9;
  color: #fff;
}
.cons2-5 {
  border-radius: 4px;
  background: #531ba9cf;
  color: #fff;
}
.cons2-6 {
  border-radius: 4px;
  background: #ff5722;
  color: #fff;
}
/********  Fetter  ********/
.fetter {
  width: 50px;
  height: 50px;
  display: inline-block;
  background: url('./item/fetter.png');
  background-size: auto 100%;
}
.fetter.fetter1 {
  background-position: 0% 0;
}
.fetter.fetter2 {
  background-position: 11.11111111% 0;
}
.fetter.fetter3 {
  background-position: 22.22222222% 0;
}
.fetter.fetter4 {
  background-position: 33.33333333% 0;
}
.fetter.fetter5 {
  background-position: 44.44444444% 0;
}
.fetter.fetter6 {
  background-position: 55.55555556% 0;
}
.fetter.fetter7 {
  background-position: 66.66666667% 0;
}
.fetter.fetter8 {
  background-position: 77.77777778% 0;
}
.fetter.fetter9 {
  background-position: 88.88888889% 0;
}
.fetter.fetter10 {
  background-position: 100% 0;
}
/********  ELEM  ********/
.elem-hydro .talent-icon {
  background-image: url("./bg/talent-hydro.webp");
}
.elem-hydro .elem-bg,
.hydro-bg {
  background-image: url("./bg/bg-hydro.webp");
}
.elem-anemo .talent-icon {
  background-image: url("./bg/talent-anemo.webp");
}
.elem-anemo .elem-bg,
.anemo-bg {
  background-image: url("./bg/bg-anemo.webp");
}
.elem-cryo .talent-icon {
  background-image: url("./bg/talent-cryo.webp");
}
.elem-cryo .elem-bg,
.cryo-bg {
  background-image: url("./bg/bg-cryo.webp");
}
.elem-electro .talent-icon {
  background-image: url("./bg/talent-electro.webp");
}
.elem-electro .elem-bg,
.electro-bg {
  background-image: url("./bg/bg-electro.webp");
}
.elem-geo .talent-icon {
  background-image: url("./bg/talent-geo.webp");
}
.elem-geo .elem-bg,
.geo-bg {
  background-image: url("./bg/bg-geo.webp");
}
.elem-pyro .talent-icon {
  background-image: url("./bg/talent-pyro.webp");
}
.elem-pyro .elem-bg,
.pyro-bg {
  background-image: url("./bg/bg-pyro.webp");
}
.elem-dendro .talent-icon {
  background-image: url("./bg/talent-dendro.webp");
}
.elem-dendro .elem-bg,
.dendro-bg {
  background-image: url("./bg/bg-dendro.webp");
}
.elem-quantum .talent-icon {
  background-image: url("./bg/talent-quantum.webp");
}
.elem-quantum .elem-bg,
.quantum-bg {
  background-image: url("./bg/bg-quantum.webp");
}
.elem-sr .talent-icon {
  background-image: url("./bg/talent-sr.webp");
}
.elem-sr .elem-bg,
.sr-bg {
  background-image: url("./bg/bg-sr.webp");
}
/* cont */
.cont {
  border-radius: 10px;
  background: url("../common/cont/card-bg.png") top left repeat-x;
  background-size: auto 100%;
  margin: 5px 15px 5px 10px;
  position: relative;
  box-shadow: 0 0 1px 0 #ccc, 2px 2px 4px 0 rgba(50, 50, 50, 0.8);
  overflow: hidden;
  color: #fff;
  font-size: 16px;
}
.cont-title {
  background: rgba(0, 0, 0, 0.4);
  box-shadow: 0 0 1px 0 #fff;
  color: #d3bc8e;
  padding: 10px 20px;
  text-align: left;
  border-radius: 10px 10px 0 0;
}
.cont-title span {
  font-size: 12px;
  color: #aaa;
  margin-left: 10px;
  font-weight: normal;
}
.cont-title.border-less {
  background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
  box-shadow: none;
  padding-bottom: 5px;
}
.cont-body {
  padding: 10px 15px;
  font-size: 12px;
  background: rgba(0, 0, 0, 0.5);
  box-shadow: 0 0 1px 0 #fff;
  font-weight: normal;
}
.cont-footer {
  padding: 10px 15px;
  font-size: 12px;
  background: rgba(0, 0, 0, 0.5);
  font-weight: normal;
}
.cont > ul.cont-msg {
  display: block;
  padding: 5px 10px;
  background: rgba(0, 0, 0, 0.5);
}
ul.cont-msg,
.cont-footer ul {
  padding-left: 15px;
}
ul.cont-msg li,
.cont-footer ul li {
  margin: 5px 0;
  margin-left: 15px;
}
ul.cont-msg li strong,
.cont-footer ul li strong {
  font-weight: normal;
  margin: 0 2px;
  color: #d3bc8e;
}
.cont-table {
  display: table;
  width: 100%;
}
.cont-table .tr {
  display: table-row;
}
.cont-table .tr:nth-child(even) {
  background: rgba(0, 0, 0, 0.4);
}
.cont-table .tr:nth-child(odd) {
  background: rgba(50, 50, 50, 0.4);
}
.cont-table .tr > div,
.cont-table .tr > td {
  display: table-cell;
  box-shadow: 0 0 1px 0 #fff;
}
.cont-table .tr > div.value-full {
  display: table;
  width: 200%;
}
.cont-table .tr > div.value-none {
  box-shadow: none;
}
.cont-table .thead {
  text-align: center;
}
.cont-table .thead > div,
.cont-table .thead > td {
  color: #d3bc8e;
  background: rgba(0, 0, 0, 0.4);
  line-height: 40px;
  height: 40px;
}
.cont-table .title,
.cont-table .th {
  color: #d3bc8e;
  padding-right: 15px;
  text-align: right;
  background: rgba(0, 0, 0, 0.4);
  min-width: 100px;
  vertical-align: middle;
}
.logo {
  font-size: 18px;
  text-align: center;
  color: #fff;
  margin: 20px 0 10px 0;
}
/* item-icon */
.item-icon {
  width: 100%;
  height: 100%;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
}
.item-icon .img {
  width: 100%;
  height: 100%;
  display: block;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}
.item-icon.artis .img {
  width: 84%;
  height: 84%;
  margin: 8%;
}
.item-icon.star1 {
  background-image: url("../common/item/bg1.png");
}
.item-icon.opacity-bg.star1 {
  background-image: url("../common/item/bg1-o.png");
}
.item-icon.star2 {
  background-image: url("../common/item/bg2.png");
}
.item-icon.opacity-bg.star2 {
  background-image: url("../common/item/bg2-o.png");
}
.item-icon.star3 {
  background-image: url("../common/item/bg3.png");
}
.item-icon.opacity-bg.star3 {
  background-image: url("../common/item/bg3-o.png");
}
.item-icon.star4 {
  background-image: url("../common/item/bg4.png");
}
.item-icon.opacity-bg.star4 {
  background-image: url("../common/item/bg4-o.png");
}
.item-icon.star5 {
  background-image: url("../common/item/bg5.png");
}
.item-icon.opacity-bg.star5 {
  background-image: url("../common/item/bg5-o.png");
}
.item-icon.star-w {
  background: #fff;
}
.item-list {
  display: flex;
}
.item-list .item-card {
  width: 70px;
  background: #e7e5d9;
}
.item-list .item-icon {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 12px;
}
.item-list .item-title {
  color: #222;
  font-size: 13px;
  text-align: center;
  padding: 2px;
  white-space: nowrap;
  overflow: hidden;
}
.item-list .item-icon {
  height: initial;
}
.item-list .item-badge {
  position: absolute;
  display: block;
  left: 0;
  top: 0;
  background: rgba(0, 0, 0, 0.6);
  font-size: 12px;
  color: #fff;
  padding: 4px 5px 3px;
  border-radius: 0 0 6px 0;
}
.icon {
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  display: block;
}
/*# sourceMappingURL=common.css.map */