import plugin from "../../../lib/plugins/plugin.js"
import KuroApi from "../components/api/kuroapi.js"
import User from "../components/User.js"
import config from "../components/config.js"
import webLoginServer from "../components/WebLoginServer.js"
import RemoteLoginClient from "../components/RemoteLoginClient.js"
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "../utils/constants.js"

export class Login extends plugin {
  constructor() {
    super({
      name: "鸣潮-登录管理",
      event: "message",
      priority: 1006,
      rule: [
        {
          reg: config.generateCommandRegex("登录\\s*(\\d{11})\\s*(\\d{4,6})"),
          fnc: "loginWithCode",
        },
        {
          reg: config.generateCommandRegex("登录"),
          fnc: "webLogin",
        },
      ],
    })
  }

  /**
   * 验证码登录
   */
  async loginWithCode(e) {
    const parts = e.msg.split(/\s+/)
    const mobile = parts[1]
    const code = parts[2]

    if (!mobile || !code) {
      const example = config.getCommandExample("登录 手机号 验证码")
      await e.reply(`❌ 参数错误\n使用方法：${example}`)
      return false
    }

    try {
      await e.reply("🔐 正在登录...")

      const kuroApi = new KuroApi()
      const loginResponse = await kuroApi.sdkLogin(mobile, code)

      if (loginResponse.code === 200 && loginResponse.data) {
        const { token, userId, userName } = loginResponse.data

        // 获取用户绑定的游戏角色
        const roleResponse = await kuroApi.getRoleList(token, 3) // 3=鸣潮

        if (roleResponse.code === 200 && roleResponse.data && roleResponse.data.length > 0) {
          const user = User.fromEvent(e)
          if (!user) {
            await e.reply(ERROR_MESSAGES.USER_NOT_FOUND)
            return false
          }

          // 保存登录信息
          const success = user.saveLoginInfo({
            token: token,
            userId: userId,
            userName: userName,
            roles: roleResponse.data,
            loginTime: new Date().toISOString(),
          })

          if (success) {
            // 自动绑定第一个角色
            const firstRole = roleResponse.data[0]
            user.bindUid(firstRole.roleId)
            user.bindServerId(firstRole.serverId)

            let message = `${SUCCESS_MESSAGES.LOGIN_SUCCESS}！\n`
            message += `用户：${userName}\n`
            message += `已自动绑定角色：${firstRole.roleName} (${firstRole.roleId})\n`
            message += `等级：${firstRole.gameLevel}\n\n`

            if (roleResponse.data.length > 1) {
              message += `📋 检测到多个角色：\n`
              roleResponse.data.forEach((role, index) => {
                message += `${index + 1}. ${role.roleName} (Lv.${role.gameLevel})\n`
              })
              const switchExample = config.getCommandExample("切换 UID")
              message += `\n使用 ${switchExample} 来切换角色`
            }

            await e.reply(message)
          } else {
            await e.reply("❌ 保存登录信息失败")
          }
        } else {
          await e.reply("❌ 未找到绑定的鸣潮角色\n请先在库街区APP中绑定鸣潮账号")
        }
      } else if (loginResponse.code === -130) {
        await e.reply("❌ 验证码错误或已过期，请重新获取验证码")
      } else {
        await e.reply(`❌ 登录失败：${loginResponse.msg || "未知错误"}`)
      }
    } catch (error) {
      console.error("登录错误:", error)
      await e.reply("❌ 登录失败，请检查网络连接或稍后重试")
    }

    return true
  }

  /**
   * 网页登录
   */
  async webLogin(e) {
    try {
      const userId = e.user_id
      const botId = e.bot_id || "default"

      if (config.isLocalLoginMode()) {
        // 本地模式：启动本地服务器
        if (!webLoginServer.isServerRunning()) {
          await e.reply("🔄 正在启动本地登录服务器...")

          const started = await webLoginServer.startLocalServer()
          if (!started) {
            await e.reply("❌ 启动本地登录服务器失败，请检查端口是否被占用")
            return false
          }
        }

        const loginUrl = webLoginServer.getLoginUrl(userId, botId)

        let message = `🌐 网页登录已准备就绪\n\n`
        message += `📱 请在浏览器中打开以下链接进行登录：\n`
        message += `${loginUrl}\n\n`
        message += `💡 提示：\n`
        message += `• 请确保手机号已在库街区注册\n`
        message += `• 登录成功后会自动保存到数据库\n`
        message += `• 登录完成后即可使用面板功能`

        await e.reply(message)
      } else {
        // 远程模式：使用远程登录服务
        const remoteClient = new RemoteLoginClient()
        const loginUrl = await remoteClient.getLoginUrl(userId, botId)

        if (loginUrl) {
          let message = `🌐 网页登录已准备就绪\n\n`
          message += `📱 请在浏览器中打开以下链接进行登录：\n`
          message += `${loginUrl}\n\n`
          message += `💡 提示：\n`
          message += `• 请确保手机号已在库街区注册\n`
          message += `• 登录成功后会自动保存到数据库\n`
          message += `• 登录完成后即可使用面板功能`

          await e.reply(message)
        } else {
          await e.reply("❌ 获取远程登录链接失败，请稍后重试")
        }
      }

      return true
    } catch (error) {
      console.error("网页登录失败:", error)
      await e.reply("❌ 网页登录失败，请稍后重试")
      return false
    }
  }
}
