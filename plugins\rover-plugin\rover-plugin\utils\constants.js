/**
 * 项目常量定义
 * 统一管理所有硬编码值
 */

// 默认配置
export const DEFAULT_CONFIG = {
  // 默认元素类型
  ELEMENT: "hydro",

  // 渲染配置
  RENDER_TYPE: "base64",
  RENDER_SCALE: 1.6,

  // 缓存配置
  CACHE_EXPIRE_TIME: 24 * 60 * 60 * 1000, // 24小时
  MAX_CACHE_SIZE: 100, // 最大缓存数量

  // 网络配置
  REQUEST_TIMEOUT: 30000, // 30秒
  RETRY_COUNT: 3,
  RETRY_DELAY: 1000, // 1秒

  // 文件路径
  DATA_DIR: "data",
  RESOURCES_DIR: "resources",
  CACHE_DIR: "cache",

  // 服务器配置
  DEFAULT_PORT: 19075,
  DEFAULT_HOST: "127.0.0.1",
}

// 游戏相关常量
export const GAME_CONFIG = {
  // 游戏标识
  GAME_ID: "ww",
  GAME_NAME: "鸣潮",

  // 服务器信息
  DEFAULT_SERVER: "库街区",

  // 角色等级限制
  MAX_LEVEL: 90,
  MIN_LEVEL: 1,

  // 武器等级限制
  MAX_WEAPON_LEVEL: 90,
  MIN_WEAPON_LEVEL: 1,

  // 声骸等级限制
  MAX_PHANTOM_LEVEL: 25,
  MIN_PHANTOM_LEVEL: 0,

  // 星级范围
  MAX_STAR: 5,
  MIN_STAR: 1,
}

// API 相关常量
export const API_CONFIG = {
  // 基础URL
  BASE_URL: "https://api.kurobbs.com",

  // 库洛游戏相关
  SERVER_ID: "76402e5b20be2c39f095a152090afddc",
  GAME_ID: 3,
  SOURCE: "ios",
  CONTENT_TYPE: "application/x-www-form-urlencoded; charset=utf-8",
  USER_AGENT:
    "Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) KuroGameBox/2.5.0",
  VERSION: "2.5.0",

  // 请求头
  DEFAULT_HEADERS: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    Accept: "application/json, text/plain, */*",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
  },

  // 状态码
  SUCCESS_CODE: 200,
  ERROR_CODES: {
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    RATE_LIMIT: 429,
    SERVER_ERROR: 500,
  },
}

// 错误消息
export const ERROR_MESSAGES = {
  // 用户相关
  USER_NOT_FOUND: "❌ 获取用户信息失败",
  UID_NOT_BOUND: "❌ 您还未绑定UID",
  LOGIN_REQUIRED: "❌ 您还未登录",

  // 数据相关
  CHARACTER_NOT_FOUND: "❌ 未找到角色数据",
  DATA_FETCH_FAILED: "❌ 获取数据失败，请稍后重试",
  INVALID_INPUT: "❌ 输入参数无效",

  // 网络相关
  NETWORK_ERROR: "❌ 网络连接失败",
  TIMEOUT_ERROR: "❌ 请求超时，请稍后重试",
  RATE_LIMIT_ERROR: "❌ 请求过于频繁，请稍后再试",

  // 系统相关
  SYSTEM_ERROR: "❌ 系统错误，请联系管理员",
  FILE_NOT_FOUND: "❌ 文件不存在",
  PERMISSION_DENIED: "❌ 权限不足",
}

// 成功消息
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: "✅ 登录成功",
  BIND_SUCCESS: "✅ 绑定成功",
  UPDATE_SUCCESS: "✅ 更新成功",
  REFRESH_SUCCESS: "✅ 刷新成功",
}

// 正则表达式
export const REGEX_PATTERNS = {
  // UID格式验证
  UID: /^\d{9}$/,

  // 手机号格式验证
  PHONE: /^1[3-9]\d{9}$/,

  // 验证码格式验证
  VERIFY_CODE: /^\d{4,6}$/,

  // Token格式验证
  TOKEN: /^[a-zA-Z0-9]{32,}$/,

  // DID格式验证
  DID: /^[a-zA-Z0-9]{32}$/,
}

// 显示模式
export const DISPLAY_MODES = {
  DEFAULT: "default",
  ARTIS: "artis", // 权重查询模式
  TRAINING: "training", // 练度统计模式
  CARD: "card", // 卡片模式
}

// 元素类型
export const ELEMENT_TYPES = {
  SPECTRO: "spectro", // 衍射
  HAVOC: "havoc", // 湮灭
  FUSION: "fusion", // 热熔
  ELECTRO: "electro", // 导电
  AERO: "aero", // 气动
  GLACIO: "glacio", // 凝固
  HYDRO: "hydro", // 默认
}

// 评分等级
export const SCORE_GRADES = {
  S: "S",
  A: "A",
  B: "B",
  C: "C",
}

// 文件扩展名
export const FILE_EXTENSIONS = {
  IMAGE: [".png", ".jpg", ".jpeg", ".webp"],
  DATA: [".json", ".yaml", ".yml"],
  CONFIG: [".js", ".json", ".yaml"],
}

// 缓存键前缀
export const CACHE_KEYS = {
  USER_DATA: "user_data:",
  CHARACTER_DATA: "char_data:",
  PHANTOM_DATA: "phantom_data:",
  IMAGE_CACHE: "image_cache:",
  API_CACHE: "api_cache:",
}

// 日志级别
export const LOG_LEVELS = {
  ERROR: "error",
  WARN: "warn",
  INFO: "info",
  DEBUG: "debug",
}

// 模板路径
export const TEMPLATE_PATHS = {
  // 角色相关模板
  CHARACTER_DETAIL: "character/profile-detail",
  CHARACTER_LIST: "character/profile-list",
  ECHO_LIST: "character/echo-list",
  WW_STYLE: "character/ww-style",
  TRAINING_STAT: "character/training-stat",

  // 排行榜模板
  GROUP_RANK: "rank/group-rank",

  // 练度模板
  ALL_CHARACTER_TRAINING: "training/all-character-training",
}

// 命令前缀
export const COMMAND_PREFIXES = {
  DEFAULT: ["#", ""],
  CUSTOM: [], // 用户自定义前缀
}

// 权重查询相关
export const WEIGHT_CONFIG = {
  // 默认权重文件路径
  DEFAULT_PATH: "resources/meta/character",

  // 支持的权重文件格式
  SUPPORTED_FORMATS: [".js", ".json"],

  // 默认权重值
  DEFAULT_WEIGHTS: {
    攻击力: 0.5,
    "攻击力%": 1.0,
    暴击: 1.0,
    暴击伤害: 1.0,
  },
}

export default {
  DEFAULT_CONFIG,
  GAME_CONFIG,
  API_CONFIG,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  REGEX_PATTERNS,
  DISPLAY_MODES,
  ELEMENT_TYPES,
  SCORE_GRADES,
  FILE_EXTENSIONS,
  CACHE_KEYS,
  LOG_LEVELS,
  TEMPLATE_PATHS,
  COMMAND_PREFIXES,
  WEIGHT_CONFIG,
}
