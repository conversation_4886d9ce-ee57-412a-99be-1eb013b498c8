/* 完整的 miao-plugin artis-list 样式 */

.container {
  width: 790px;
}

.uid {
  margin: 20px 10px 10px;
  text-shadow: 0 0 3px #000, 2px 2px 4px rgba(0, 0, 0, 0.7);
  text-align: right;
  color: #fff;
}

/*** artis***/
/* .artis 样式在文件末尾统一定义 */

.artis .item {
  width: 185px;
  min-height: 220px; /* 适当增加高度以容纳声骸的多主属性 */
  border-radius: 10px;
  background: url("../common/cont/card-bg.png") top left repeat-x;
  background-size: auto 100%;
  margin: 5px;
  position: relative;
  box-shadow: 0 0 1px 0 #ccc, 2px 2px 4px 0 rgba(50, 50, 50, 0.8);
  overflow: hidden;
}

.artis .item .arti-icon {
  width: 60px;
  height: 60px;
  position: absolute;
  left: 2px;
  top: 3px;
}

.artis .item .arti-icon span {
  position: absolute;
  right: 2px;
  bottom: 0;
  margin-left: 5px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 5px;
  height: 18px;
  line-height: 18px;
  padding: 0 3px;
  color: #fff;
  font-size: 12px;
  display: block;
}

.artis .item .arti-icon .img {
  width: 50px;
  height: 50px;
  margin: 5px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.artis .head {
  color: #fff;
  padding: 12px 0 8px 68px;
}

.artis .head strong {
  font-size: 15px;
  display: block;
  white-space: nowrap;
  overflow: hidden;
}

.artis .head span {
  font-size: 14px;
}

.mark-ACE,
.mark-MAX {
  color: #e85656;
  font-weight: bold;
}

.mark-SSS,
.mark-SS {
  color: #ffe699;
  font-weight: bold;
}

.mark-S,
.mark-A {
  color: #d699ff;
  font-weight: bold;
}

/* 移除单独的 .arti-main 样式，让主属性和副属性使用统一样式 */

.artis ul.detail {
  width: 100%;
  padding: 0;
  position: initial;
  display: table;
}

.artis ul.detail li {
  padding: 0 3px;
  font-size: 14px;
  position: relative;
  width: 100%;
  display: table-row;
  line-height: 26px;
  height: 26px;
  white-space: nowrap;
}

.artis ul.detail li span {
  position: initial;
  display: table-cell;
  color: #fff;
}

.artis ul.detail li span.title {
  text-align: left;
  padding-left: 30px;
  font-size: 14px;
  max-width: 110px; /* 限制标题宽度防止超出卡片 */
  overflow: hidden;
  text-overflow: ellipsis;
}

.artis ul.detail li span.title i.eff {
  position: absolute;
  display: block;
  left: 3px;
  top: 4px;
  font-size: 12px;
  font-style: normal;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 5px;
  height: 18px;
  line-height: 18px;
  width: 23px;
  text-align: center;
}

/* 满值标记样式 - 红色文字无背景 */
.artis ul.detail li span.title i.max-mark {
  position: absolute;
  display: block;
  left: 3px;
  top: 6px;
  font-size: 8px;
  font-style: normal;
  background: transparent;
  color: #e85656;
  border-radius: 2px;
  height: 12px;
  line-height: 12px;
  width: 20px;
  text-align: center;
  font-weight: bold;
  z-index: 2;
  border: none;
}

.artis ul.detail li span.title i.up-num {
  position: absolute;
  display: block;
  left: 91px;
  top: 9px;
  height: 8px;
  width: 50px;
  background-image: url('./imgs/up-num-icon1.png');
  background-position: 0 0;
  background-repeat: no-repeat;
  background-size: auto 500%;
}

.artis ul.detail li span.val {
  text-align: right;
  padding-right: 10px;
  font-size: 14px;
  max-width: 60px; /* 限制数值宽度防止超出卡片 */
  overflow: hidden;
  text-overflow: ellipsis;
}

.artis ul.detail li.great span.title {
  color: #ffe699;
}

.artis ul.detail li.great span.title i.up-num {
  background-image: url("./imgs/up-num-icon2.png");
  background-size: auto 500%;
}

.artis ul.detail li.useful span.title {
  color: #d699ff; /* 有用属性的颜色 */
}

.artis ul.detail li.useful span.title i.up-num {
  background-image: url("./imgs/up-num-icon1.png");
  background-size: auto 500%;
}

.artis ul.detail li.nouse span {
  color: #888;
}

.artis ul.detail li.nouse span i.up-num {
  background-image: url("./imgs/up-num-icon0.png");
  background-size: auto 500%;
}

.artis ul.detail li.arti-main {
  /* 完全继承基础 li 样式，只添加区分性的样式 */
  font-weight: bold; /* 保持加粗以区分主属性 */
}

/* arti 特定样式 */
.artis .item.arti {
  overflow: visible;
}

.artis .item.arti .head {
  position: relative;
  border-radius: 10px 10px 0 0;
  text-shadow: 0 0 1px #000, 1px 1px 2px rgba(0, 0, 0, 0.7);
  padding: 15px 10px 5px 15px;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7), rgba(25, 25, 25, 0.3), rgba(25, 25, 25, 0), rgba(25, 25, 25, 0));
}

.artis .item.arti .arti-icon {
  left: auto;
  right: 0;
  top: -12px;
  width: 90px;
  height: 90px;
}

.artis .item.arti .arti-icon .img {
  width: 100%;
  height: 100%;
  margin: 0;
  mask: linear-gradient(45deg, #0000 0, #0005 30%, #000 50%);
  -webkit-mask: linear-gradient(45deg, #0000 0, #0005 30%, #000 50%);
}

.artis .item.arti .arti-icon span {
  top: 50px;
  right: 8px;
  background: rgba(0, 0, 0, 0.8);
}

.artis ul.detail {
  backdrop-filter: blur(2px);
  border-radius: 0 0 10px 10px;
  overflow: visible;
  min-height: 120px;
}

.artis ul.detail li.arti-main {
  background: rgba(25, 25, 25, 0.3); /* 稍微减淡背景，与副属性更协调 */
}

.artis ul.detail li.arti-main .title {
  padding-left: 30px; /* 与副属性保持一致的左边距 */
}

/* 角色头像 */
.artis .item .avatar {
  position: absolute;
  right: 5px;
  top: 5px;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  overflow: hidden;
  z-index: 3;
}

.artis .item .avatar img {
  max-width: 100%;
  max-height: 100%;
}

/* 声骸列表特定样式 - 覆盖容器宽度以适应更多声骸 */
.container {
  width: 790px;
}

.artis {
  width: 790px; /* 保持更宽的容器以适应更多声骸 */
  /* 保持 flexbox 布局 */
  display: flex;
  flex-wrap: wrap;
  margin: 10px -5px 5px;
}

/* 声骸列表特定样式 - 覆盖容器宽度以适应更多声骸 */
.container {
  width: 790px;
}

.artis {
  width: 790px;
  /* 保持 flexbox 布局 */
  display: flex;
  flex-wrap: wrap;
  margin: 10px -5px 5px;
}

.artis .item {
  /* 调整卡片宽度以适应 790px 容器 */
  width: 185px;
  margin: 5px;
}
