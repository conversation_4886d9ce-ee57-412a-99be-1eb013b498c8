import fs from "fs"
import { appPath } from "./components/path.js"

logger.info("-------------------------------------")
logger.info("rover-plugin 加载中")
logger.info("仓库地址 https://github.com/tyql688/rover-plugin")
logger.info("-------------------------------------")

// 初始化配置文件 - 从defSet目录复制默认配置
try {
  const config = await import("./components/config.js")
  // 配置管理器会在构造时自动检查并创建配置文件
  logger.info("✅ 配置文件初始化完成")
} catch (error) {
  logger.error("❌ 配置文件初始化失败:", error)
}

// 移除资源预加载器，改为按需下载模式
// 资源将在实际使用时进行下载，避免启动时的性能问题

// 初始化网页登录服务器
try {
  const { default: config } = await import("./components/config.js")
  const { default: webLoginServer } = await import("./components/WebLoginServer.js")

  // 如果启用了网页登录功能，根据模式启动相应服务
  if (config.isFeatureEnabled("webLogin")) {
    // 延迟启动，避免与其他初始化冲突
    setTimeout(async () => {
      try {
        if (config.isLocalLoginMode()) {
          // 本地模式：启动本地服务器
          const started = await webLoginServer.startLocalServer()
          if (started) {
            const loginUrl = config.getLocalLoginUrl()
            logger.info(`🌐 本地网页登录服务器已启动: ${loginUrl}`)
          }
        } else {
          // 远程模式：启动本地服务器接收回调，但不提供登录页面
          const started = await webLoginServer.startLocalServer()
          if (started) {
            const localPort = config.getLocalLoginPort()
            const remoteUrl = config.getRemoteLoginUrl()
            logger.info(`🌐 远程登录模式已启用`)
            logger.info(`🌐 远程登录服务器: ${remoteUrl}`)
            logger.info(`🌐 本地回调服务器已启动 (端口: ${localPort})`)
            logger.info(`💡 远程服务器登录成功后将回调到本地端口 ${localPort}`)
          }
        }
      } catch (error) {
        logger.warn("⚠️ 网页登录初始化失败:", error.message)
        if (config.isLocalLoginMode()) {
          logger.info("💡 请检查端口是否被占用，或修改配置文件中的端口设置")
        }
      }
    }, 2000)
  }
} catch (error) {
  logger.warn("⚠️ 网页登录服务器初始化失败:", error.message)
}

// global.segment 由 Yunzai 框架自动设置，无需手动导入

const files = fs.readdirSync(appPath).filter(file => file.endsWith(".js"))

const ret = await Promise.allSettled(files.map(file => import(`./apps/${file}`)))

const apps = {}
for (let i in files) {
  let name = files[i].replace(".js", "")

  if (ret[i].status != "fulfilled") {
    logger.error(`[rover-plugin] 载入模块${logger.red(name)}错误`)
    logger.error(ret[i].reason)
    continue
  }

  apps[name] = ret[i].value[Object.keys(ret[i].value)[0]]
}

export { apps }
