import plugin from "../../../lib/plugins/plugin.js"
import User from "../components/User.js"
import config from "../components/config.js"
import { CharacterCache } from "../components/cache.js"
import { renderer } from "../utils/renderer.js"
import Background from "../components/Background.js"

/**
 * 练度统计功能
 * 完全使用 miao-plugin 的 UI 风格
 */
export class TrainingStat extends plugin {
  constructor() {
    super({
      name: "鸣潮-练度统计",
      event: "message",
      priority: 600,
      rule: [
        {
          reg: config.generateCommandRegex("练度统计"),
          fnc: "trainingStat",
        },
        {
          reg: config.generateCommandRegex("角色练度"),
          fnc: "trainingStat",
        },
      ],
    })
  }

  /**
   * 练度统计主功能
   */
  async trainingStat(e) {
    try {
      await e.reply("🔄 正在生成练度统计，请稍候...")

      // 获取用户信息
      const user = User.fromEvent(e)
      if (!user) {
        await e.reply("❌ 获取用户信息失败")
        return false
      }

      // 获取用户数据
      const userData = await user.getAllUserData()
      const { uid } = userData

      if (!uid) {
        await e.reply(
          `❌ 您还未绑定UID\n请先使用：${config.getCommandExample("绑定uid 你的UID")}\n或使用：${config.getCommandExample("添加token token,did")}`,
        )
        return false
      }

      // 获取角色数据
      const roleDetailList = CharacterCache.getRoleDetailList(uid)
      if (!roleDetailList || !Array.isArray(roleDetailList) || roleDetailList.length === 0) {
        await e.reply(
          `❌ 未找到角色数据\n请先使用 ${config.getCommandExample("刷新面板")} 获取角色数据`,
        )
        return false
      }

      // 渲染练度统计
      await this.renderTrainingStat(e, uid, roleDetailList)
      return true
    } catch (error) {
      console.error("练度统计生成失败:", error)
      await e.reply("❌ 练度统计生成失败，请稍后重试")
      return false
    }
  }

  /**
   * 渲染练度统计 - miao-plugin 风格
   */
  async renderTrainingStat(e, uid, roleDetailList) {
    try {
      // 处理角色数据
      const processedChars = await this.processTrainingData(roleDetailList)

      // 对角色进行排序：按平均等级排序
      processedChars.sort((a, b) => {
        // 首先按星级排序（五星在前）
        if (a.star !== b.star) {
          return b.star - a.star
        }
        // 然后按平均等级排序（高等级在前）
        return b.avgLevel - a.avgLevel
      })

      // 获取背景图
      const background = await Background.getBackground("list")

      // 渲染数据
      const renderData = {
        uid,
        chars: processedChars,
        mode: "training", // 练度模式
        conNum: -1,
        totalCount: 1, // 当前只有一个用户的数据
        lastUpdate: new Date().toLocaleString(),
        bgType: 1,
        background: background?.text,
        pct: function(num) {
          return (num * 100).toFixed(1)
        }
      }

      // 渲染图片 - 使用 miao-plugin 风格
      const img = await renderer.render(e, "character/training-stat", renderData)

      if (img) {
        await e.reply(img)
      } else {
        await e.reply("❌ 练度统计渲染失败，请稍后重试")
      }
    } catch (error) {
      console.error("渲染练度统计失败:", error)
      await e.reply("❌ 渲染练度统计失败，请稍后重试")
    }
  }

  /**
   * 处理练度数据，转换为 miao-plugin 格式
   */
  async processTrainingData(roleDetailList) {
    const processedChars = []

    roleDetailList.forEach((roleDetail, index) => {
      const role = roleDetail.role || roleDetail
      const roleName = role.roleName || "未知角色"
      const level = role.level || 1

      // 计算等级分布
      const levelDist = this.calculateLevelDistribution(level)

      // 获取角色头像路径
      let faceImage = "/character-images/default-avatar.webp"
      if (role.roleIconUrl) {
        // 这里可以添加图片路径处理逻辑
        faceImage = role.roleIconUrl.replace(/^.*\//, "/character-images/")
      }

      processedChars.push({
        name: roleName,
        abbr: roleName,
        star: role.starLevel || 5,
        side: faceImage, // miao-plugin 使用 side 字段作为头像路径
        face: faceImage,
        hold: 1.0, // 持有率 100%（因为是用户自己的角色）
        avgLevel: level,
        levelDist: levelDist,
        roleId: role.roleId,
      })
    })

    return processedChars
  }

  /**
   * 计算等级分布
   * 将单个等级转换为分布数据，模拟 miao-plugin 的命座分布格式
   */
  calculateLevelDistribution(level) {
    const distributions = [
      { id: "n0", value: 0 }, // 1-20级
      { id: "1", value: 0 },  // 21-40级
      { id: "2", value: 0 },  // 41-60级
      { id: "3", value: 0 },  // 61-70级
      { id: "4", value: 0 },  // 71-80级
      { id: "5", value: 0 },  // 81-90级
      { id: "6", value: 0 },  // 满级
    ]

    // 根据等级设置对应区间为 100%
    if (level <= 20) {
      distributions[0].value = 1.0
    } else if (level <= 40) {
      distributions[1].value = 1.0
    } else if (level <= 60) {
      distributions[2].value = 1.0
    } else if (level <= 70) {
      distributions[3].value = 1.0
    } else if (level <= 80) {
      distributions[4].value = 1.0
    } else if (level <= 89) {
      distributions[5].value = 1.0
    } else {
      distributions[6].value = 1.0 // 90级满级
    }

    return distributions
  }

  /**
   * 获取等级区间描述
   */
  getLevelRangeDesc(level) {
    if (level <= 20) return "1-20级"
    if (level <= 40) return "21-40级"
    if (level <= 60) return "41-60级"
    if (level <= 70) return "61-70级"
    if (level <= 80) return "71-80级"
    if (level <= 89) return "81-90级"
    return "满级"
  }

  /**
   * 获取等级颜色
   */
  getLevelColor(level) {
    if (level <= 20) return "#95a5a6" // 灰色
    if (level <= 40) return "#3498db" // 蓝色
    if (level <= 60) return "#2ecc71" // 绿色
    if (level <= 70) return "#f39c12" // 橙色
    if (level <= 80) return "#e74c3c" // 红色
    if (level <= 89) return "#9b59b6" // 紫色
    return "#f1c40f" // 金色
  }
}
