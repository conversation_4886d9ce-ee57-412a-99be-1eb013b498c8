/**
 * Redis管理器
 * 使用Yunzai-Bot提供的全局redis对象
 * 用于群排行等功能的数据缓存
 */
class RedisManager {
  constructor() {
    this.keyPrefix = 'rover:'
  }

  /**
   * 检查Redis是否可用
   * @returns {boolean} Redis是否可用
   */
  isAvailable() {
    return typeof global.redis !== 'undefined' && global.redis !== null
  }

  /**
   * 初始化Redis（使用全局redis对象）
   * @returns {Promise<boolean>} 是否可用
   */
  async connect() {
    if (this.isAvailable()) {
      console.log('✅ 使用Yunzai全局Redis对象')
      return true
    } else {
      console.log('📋 Redis不可用，将使用内存缓存模式')
      return false
    }
  }

  /**
   * 断开Redis连接（全局redis对象由Yunzai管理，无需手动断开）
   */
  async disconnect() {
    // 全局redis对象由Yunzai-Bot管理，插件无需手动断开
    console.log('📋 Redis连接由Yunzai-Bot管理')
  }

  /**
   * 检查是否已连接
   * @returns {boolean} 是否已连接
   */
  isConnected() {
    return this.isAvailable()
  }

  /**
   * 生成带前缀的键名
   * @param {string} key - 原始键名
   * @returns {string} 带前缀的键名
   */
  getKey(key) {
    return `${this.keyPrefix}${key}`
  }

  /**
   * 设置键值
   * @param {string} key - 键
   * @param {string} value - 值
   * @param {number} expire - 过期时间（秒）
   * @returns {Promise<boolean>} 是否设置成功
   */
  async set(key, value, expire = null) {
    if (!this.isAvailable()) {
      return false
    }

    try {
      const fullKey = this.getKey(key)
      if (expire) {
        await global.redis.setEx(fullKey, expire, value)
      } else {
        await global.redis.set(fullKey, value)
      }
      return true
    } catch (error) {
      console.error('Redis设置失败:', error)
      return false
    }
  }

  /**
   * 获取值
   * @param {string} key - 键
   * @returns {Promise<string|null>} 值
   */
  async get(key) {
    if (!this.isAvailable()) {
      return null
    }

    try {
      const fullKey = this.getKey(key)
      return await global.redis.get(fullKey)
    } catch (error) {
      console.error('Redis获取失败:', error)
      return null
    }
  }

  /**
   * 删除键
   * @param {string} key - 键
   * @returns {Promise<boolean>} 是否删除成功
   */
  async del(key) {
    if (!this.isAvailable()) {
      return false
    }

    try {
      const fullKey = this.getKey(key)
      await global.redis.del(fullKey)
      return true
    } catch (error) {
      console.error('Redis删除失败:', error)
      return false
    }
  }

  /**
   * 检查键是否存在
   * @param {string} key - 键
   * @returns {Promise<boolean>} 是否存在
   */
  async exists(key) {
    if (!this.isAvailable()) {
      return false
    }

    try {
      const fullKey = this.getKey(key)
      const result = await global.redis.exists(fullKey)
      return result === 1
    } catch (error) {
      console.error('Redis检查存在失败:', error)
      return false
    }
  }

  /**
   * 设置哈希字段
   * @param {string} key - 键
   * @param {string} field - 字段
   * @param {string} value - 值
   * @returns {Promise<boolean>} 是否设置成功
   */
  async hSet(key, field, value) {
    if (!this.isAvailable()) {
      return false
    }

    try {
      const fullKey = this.getKey(key)
      await global.redis.hSet(fullKey, field, value)
      return true
    } catch (error) {
      console.error('Redis哈希设置失败:', error)
      return false
    }
  }

  /**
   * 获取哈希字段
   * @param {string} key - 键
   * @param {string} field - 字段
   * @returns {Promise<string|null>} 值
   */
  async hGet(key, field) {
    if (!this.isAvailable()) {
      return null
    }

    try {
      const fullKey = this.getKey(key)
      return await global.redis.hGet(fullKey, field)
    } catch (error) {
      console.error('Redis哈希获取失败:', error)
      return null
    }
  }

  /**
   * 获取哈希所有字段
   * @param {string} key - 键
   * @returns {Promise<Object|null>} 哈希对象
   */
  async hGetAll(key) {
    if (!this.isAvailable()) {
      return null
    }

    try {
      const fullKey = this.getKey(key)
      return await global.redis.hGetAll(fullKey)
    } catch (error) {
      console.error('Redis哈希获取所有失败:', error)
      return null
    }
  }

  /**
   * 添加到有序集合
   * @param {string} key - 键
   * @param {number|Object} scoreOrData - 分数或数据对象
   * @param {string} member - 成员（当第二个参数是分数时使用）
   * @returns {Promise<boolean>} 是否添加成功
   */
  async zAdd(key, scoreOrData, member) {
    if (!this.isAvailable()) {
      return false
    }

    try {
      const fullKey = this.getKey(key)

      // 支持两种调用方式
      if (typeof scoreOrData === 'object' && scoreOrData.score !== undefined && scoreOrData.value !== undefined) {
        // 方式1: zAdd(key, { score: 100, value: 'member' })
        await global.redis.zAdd(fullKey, { score: scoreOrData.score, value: scoreOrData.value })
      } else {
        // 方式2: zAdd(key, 100, 'member')
        await global.redis.zAdd(fullKey, { score: scoreOrData, value: member })
      }

      return true
    } catch (error) {
      console.error('Redis有序集合添加失败:', error)
      return false
    }
  }

  /**
   * 获取有序集合排名（降序）
   * @param {string} key - 键
   * @param {number} start - 开始位置
   * @param {number} stop - 结束位置
   * @returns {Promise<Array|null>} 排名列表
   */
  async zRevRange(key, start, stop) {
    if (!this.isAvailable()) {
      return null
    }

    try {
      const fullKey = this.getKey(key)
      return await global.redis.zRevRange(fullKey, start, stop, { WITHSCORES: true })
    } catch (error) {
      console.error('Redis有序集合获取失败:', error)
      return null
    }
  }

  /**
   * 获取成员在有序集合中的排名
   * @param {string} key - 键
   * @param {string} member - 成员
   * @returns {Promise<number|null>} 排名（从0开始）
   */
  async zRevRank(key, member) {
    if (!this.isAvailable()) {
      return null
    }

    try {
      const fullKey = this.getKey(key)
      return await global.redis.zRevRank(fullKey, member)
    } catch (error) {
      console.error('Redis获取排名失败:', error)
      return null
    }
  }

  /**
   * 设置键的过期时间
   * @param {string} key - 键
   * @param {number} seconds - 过期时间（秒）
   * @returns {Promise<boolean>} 是否设置成功
   */
  async expire(key, seconds) {
    if (!this.isAvailable()) {
      return false
    }

    try {
      const fullKey = this.getKey(key)
      await global.redis.expire(fullKey, seconds)
      return true
    } catch (error) {
      console.error('Redis设置过期时间失败:', error)
      return false
    }
  }
}

// 创建Redis管理器实例
const redisManager = new RedisManager()

export default redisManager
export { RedisManager }
