/**
 * 原生fetch测试 - 完全模拟WutheringWavesUID的请求方式
 */

import fetch from "node-fetch"
import { HttpsProxyAgent } from "https-proxy-agent"

const TEST_CONFIG = {
  token:
    "eyJhbGciOiJIUzI1NiJ9.eyJjcmVhdGVkIjoxNzUzNjMxMjg2Njc1LCJ1c2VySWQiOjEwODE2NzMyfQ.Z9BLJ1jtanrNfl1JLvcifr8ypSXEu7513sQvy9I-A1c",
  did: "6F02FE7B671ACA64694F19FB67EBEBAD07659846",
}

// 生成随机字符串 - 完全按照WutheringWavesUID的方式
function generateRandomString(length = 32) {
  const chars =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!\"#$%&'()*+,-./:;<=>?@[\\]^_`{|}~"
  let result = ""
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

// 获取通用请求头 - 完全按照WutheringWavesUID的get_common_header
function getCommonHeader(platform = "ios") {
  const devCode = generateRandomString()
  return {
    source: platform,
    "Content-Type": "application/x-www-form-urlencoded; charset=utf-8",
    "User-Agent":
      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0",
    devCode: devCode,
    version: "2.5.0",
  }
}

async function testNativeRequest() {
  console.log("🧪 原生fetch测试 - 模拟WutheringWavesUID + 代理")
  console.log(`Token: ${TEST_CONFIG.token.substring(0, 20)}...`)
  console.log(`DID: ${TEST_CONFIG.did}`)

  try {
    const url = "https://api.kurobbs.com/gamer/role/list"

    // 按照WutheringWavesUID的方式设置请求头
    const platform = "ios" // login_platform() 返回 "ios"
    const headers = getCommonHeader(platform)

    // 按照WutheringWavesUID的方式设置请求头
    headers.token = TEST_CONFIG.token
    headers.devCode = TEST_CONFIG.did

    console.log("\n📋 请求头:")
    Object.entries(headers).forEach(([key, value]) => {
      if (key === "token") {
        console.log(`  ${key}: ${value.substring(0, 20)}...`)
      } else {
        console.log(`  ${key}: ${value}`)
      }
    })

    // 按照WutheringWavesUID的方式设置请求体
    const data = { gameId: "3" }

    // 将数据转换为URLSearchParams格式
    const formData = new URLSearchParams()
    Object.entries(data).forEach(([key, value]) => {
      formData.append(key, value)
    })

    console.log("\n📋 请求体:")
    console.log(`  gameId: 3`)
    console.log(`  Content-Type: ${headers["Content-Type"]}`)

    // 配置代理
    const proxyUrl = "http://1907581050:<EMAIL>:9151"
    const agent = new HttpsProxyAgent(proxyUrl)

    console.log("\n📡 发送请求...")
    console.log(`URL: ${url}`)
    console.log(`🌐 使用代理: ${proxyUrl}`)

    // 等待5秒，避免请求过于频繁
    console.log("⏳ 等待5秒...")
    await new Promise(resolve => setTimeout(resolve, 5000))

    const response = await fetch(url, {
      method: "POST",
      headers: headers,
      body: formData.toString(),
      agent: agent,
    })

    const result = await response.json()

    console.log("\n📋 API响应:")
    console.log("Status:", response.status)
    console.log("Code:", result.code)
    console.log("Message:", result.msg)
    console.log("Data:", result.data)

    if (result.code === 200) {
      console.log("✅ API调用成功！")
      if (result.data && result.data.length > 0) {
        console.log(`📊 获取到 ${result.data.length} 个角色`)
        result.data.forEach((role, index) => {
          console.log(`  ${index + 1}. ${role.roleName} (${role.roleId})`)
        })
      }
    } else {
      console.log("❌ API调用失败")
      if (result.code === 270) {
        console.log("🚨 风险控制检测")
        console.log("这表明请求被库街区的安全系统拦截")
        console.log("可能的原因:")
        console.log("1. IP地址被标记为风险")
        console.log("2. 请求频率过高")
        console.log("3. 请求模式被识别为机器人")
        console.log("4. Token或DID有问题")
      }
    }
  } catch (error) {
    console.error("❌ 测试失败:", error)
  }
}

// 运行测试
testNativeRequest()
