import { DEFAULT_CONFIG } from "./constants.js"

/**
 * 统一渲染工具
 * 提供所有功能模块使用的渲染方法
 *
 * 注意: 渲染数据中的 _miao_path、_tpl_path 等字段由 Yunzai 系统自动添加
 * 这些字段用于兼容 miao-plugin 模板，rover-plugin 不直接使用
 * rover-plugin 只使用 _res_path 字段来引用自己的资源
 */
export class Renderer {
  constructor() {
    this.pluginName = "rover-plugin"
    this.defaultOptions = {
      retType: DEFAULT_CONFIG.RENDER_TYPE,
      scale: DEFAULT_CONFIG.RENDER_SCALE,
    }
  }

  /**
   * 统一渲染方法
   * @param {Object} e - 事件对象
   * @param {string} templatePath - 模板路径 (如: "character/profile-list")
   * @param {Object} data - 渲染数据
   * @param {Object} options - 渲染选项
   * @returns {Promise<any>} 渲染结果
   */
  async render(e, templatePath, data, options = {}) {
    try {
      console.log(`🎨 开始渲染模板: ${templatePath}`)

      // 确保runtime存在
      if (!e.runtime) {
        console.log("🔧 初始化runtime...")
        const Runtime = (await import("../../../lib/plugins/runtime.js")).default
        e.runtime = await Runtime.init(e)
      }

      // 合并渲染选项
      const renderOptions = {
        ...this.defaultOptions,
        ...options,
      }

      console.log(`📊 渲染配置:`, renderOptions)
      console.log(`📋 数据键值:`, Object.keys(data))

      // 执行渲染
      const result = await e.runtime.render(this.pluginName, templatePath, data, renderOptions)

      console.log(`🖼️ 模板渲染${result ? "成功" : "失败"}`)

      if (result) {
        console.log(`📏 渲染结果类型: ${typeof result}`)

        // 根据结果类型提供不同的信息
        if (typeof result === "string") {
          console.log(`📏 渲染结果长度: ${result.length} 字符`)
        } else if (result && typeof result === "object") {
          console.log(`📏 渲染结果对象属性:`, Object.keys(result))
        }
      }

      return result
    } catch (error) {
      console.error(`❌ 渲染模板失败 [${templatePath}]:`, error)
      return null
    }
  }

  /**
   * 渲染并回复（不发送失败消息）
   * @param {Object} e - 事件对象
   * @param {string} templatePath - 模板路径
   * @param {Object} data - 渲染数据
   * @param {Object} options - 渲染选项
   * @returns {Promise<boolean>} 是否成功
   */
  async renderAndReply(e, templatePath, data, options = {}) {
    try {
      const result = await this.render(e, templatePath, data, options)

      if (result) {
        await e.reply(result)
        return true
      }
      // 渲染失败时不发送文字消息
      return false
    } catch (error) {
      console.error(`❌ 渲染并回复失败 [${templatePath}]:`, error)
      // 渲染失败时不发送文字消息
      return false
    }
  }

  /**
   * 批量渲染（用于需要渲染多个模板的场景）
   * @param {Object} e - 事件对象
   * @param {Array} templates - 模板配置数组 [{path, data, options}]
   * @returns {Promise<Array>} 渲染结果数组
   */
  async renderBatch(e, templates) {
    const results = []

    for (const template of templates) {
      const { path, data, options = {} } = template
      const result = await this.render(e, path, data, options)
      results.push(result)
    }

    return results
  }
}

// 创建单例实例
export const renderer = new Renderer()

// 导出便捷方法
export const { render, renderAndReply, renderBatch } = renderer
